# -*- coding: utf-8 -*-
"""
author: czsc contributor
create_dt: 2023/10/10 12:00
describe: 通达信数据连接器使用示例
"""

import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from czsc.connectors.tdx_connector import get_raw_bars
from czsc.utils.bar_generator import resample_bars

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


def main():
    # 使用一个常见的股票代码进行测试
    symbol = "000001"  # 平安银行
    # 设置一个较短的时间范围，避免获取过多数据
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d")
    
    print(f"获取 {symbol} 的日线数据，时间范围：{start_date} 至 {end_date}")
    
    try:
        # 获取日线数据
        bars = get_raw_bars(symbol=symbol, freq="日线", sdt=start_date, edt=end_date, fq="前复权")
        
        if not bars:
            print(f"未获取到 {symbol} 的数据，请检查通达信服务器连接或股票代码是否正确")
            return
        
        print(f"成功获取 {len(bars)} 条K线数据")
        
        # 将RawBar对象转换为DataFrame以便于分析和绘图
        data = pd.DataFrame([
            {
                "dt": bar.dt,
                "open": bar.open,
                "high": bar.high,
                "low": bar.low,
                "close": bar.close,
                "vol": bar.vol,
                "amount": bar.amount
            } for bar in bars
        ])
        
        # 打印数据基本信息
        print("\n数据基本信息：")
        print(f"开始日期：{data['dt'].min()}")
        print(f"结束日期：{data['dt'].max()}")
        print(f"数据条数：{len(data)}")
        print(f"收盘价范围：{data['close'].min()} - {data['close'].max()}")
        
        # 绘制K线图
        plt.figure(figsize=(12, 6))
        plt.plot(data['dt'], data['close'], label='收盘价')
        plt.title(f"{symbol} 收盘价走势图 ({start_date} 至 {end_date})")
        plt.xlabel('日期')
        plt.ylabel('价格')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f"{symbol}_close_price.png")
        print(f"收盘价走势图已保存为 {symbol}_close_price.png")
        
        # 尝试获取不同周期的数据并进行转换
        print("\n尝试获取5分钟K线数据并转换为30分钟K线...")
        min5_bars = get_raw_bars(symbol=symbol, freq="5分钟", sdt=start_date, edt=end_date, fq="前复权")
        
        if min5_bars:
            # 将RawBar对象转换为DataFrame
            min5_data = pd.DataFrame([
                {
                    "symbol": bar.symbol,
                    "dt": bar.dt,
                    "open": bar.open,
                    "high": bar.high,
                    "low": bar.low,
                    "close": bar.close,
                    "vol": bar.vol,
                    "amount": bar.amount
                } for bar in min5_bars
            ])
            
            # 使用resample_bars函数将5分钟K线转换为30分钟K线
            min30_bars = resample_bars(min5_data, target_freq="30分钟", raw_bars=True)
            print(f"成功将{len(min5_bars)}条5分钟K线转换为{len(min30_bars)}条30分钟K线")
        else:
            print("未获取到5分钟K线数据")
        
    except ImportError:
        print("请安装QUANTAXIS库以使用QATdx数据源：pip install quantaxis")
    except Exception as e:
        print(f"获取数据时发生错误: {str(e)}")


if __name__ == "__main__":
    main()
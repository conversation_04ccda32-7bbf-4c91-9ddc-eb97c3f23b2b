#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示优化后的LLM分析效果
"""

import sys
import os
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def demo_enhanced_llm_analysis():
    """演示增强版LLM分析"""
    print("🎯 演示增强版LLM分析效果")
    print("="*80)
    print(f"⏰ 演示开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        from system.enhanced_display_system import EnhancedDisplaySystem
        
        # 创建协调器和显示系统
        coordinator = MultiAgentCoordinatorV3()
        display = EnhancedDisplaySystem()
        
        # 执行分析
        fund_code = '518880'
        print(f"📊 分析基金: {fund_code} (黄金ETF)")
        print("🔄 正在执行完整的智能体协调分析...")
        
        analysis_result = coordinator.coordinate_analysis(fund_code)
        
        if analysis_result and 'error' not in analysis_result:
            print("\n✅ 分析完成！")
            
            # 显示LLM分析详情
            llm_analysis = analysis_result.get('llm_analysis', {})
            if llm_analysis and 'error' not in llm_analysis:
                print("\n" + "="*80)
                print("🤖 LLM分析详细结果")
                print("="*80)
                
                print(f"🎯 市场情绪: {llm_analysis.get('market_sentiment', 'N/A')}")
                print(f"🎯 置信度: {llm_analysis.get('confidence_level', 0):.2f}")
                
                # 市场驱动因素
                market_drivers = llm_analysis.get('market_drivers', [])
                if market_drivers:
                    print(f"\n📈 市场驱动因素:")
                    for i, driver in enumerate(market_drivers, 1):
                        print(f"   {i}. {driver}")
                
                # 风险点
                risk_points = llm_analysis.get('risk_points', [])
                if risk_points:
                    print(f"\n⚠️ 风险点:")
                    for i, risk in enumerate(risk_points, 1):
                        print(f"   {i}. {risk}")
                
                # 投资机会
                opportunities = llm_analysis.get('opportunities', [])
                if opportunities:
                    print(f"\n💡 投资机会:")
                    for i, opp in enumerate(opportunities, 1):
                        print(f"   {i}. {opp}")
                
                # 策略建议
                strategy = llm_analysis.get('strategy_suggestion', '')
                if strategy:
                    print(f"\n💼 策略建议:")
                    print(f"   {strategy}")
                
                # 核心洞察
                key_insights = llm_analysis.get('key_insights', '')
                if key_insights:
                    print(f"\n🔍 核心洞察:")
                    print(f"   {key_insights}")
                
                # 原始响应长度
                raw_response = llm_analysis.get('raw_response', '')
                if raw_response:
                    print(f"\n📊 分析详细程度:")
                    print(f"   原始响应长度: {len(raw_response)} 字符")
                    print(f"   使用模型: {llm_analysis.get('llm_model', 'N/A')}")
                
            else:
                print(f"\n❌ LLM分析失败: {llm_analysis.get('error', '未知错误')}")
            
            # 使用显示系统展示完整结果
            print("\n" + "="*80)
            print("📊 完整分析报告")
            print("="*80)
            display.display_comprehensive_analysis(analysis_result)
            
            return True
        else:
            print(f"❌ 分析失败: {analysis_result}")
            return False
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_llm_parameters():
    """比较不同LLM参数的效果"""
    print("\n🔍 比较不同LLM参数效果")
    print("="*80)
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        analyzer = LLMMarketAnalyzer()
        
        if not analyzer.client:
            print("❌ LLM客户端未初始化，跳过参数比较")
            return False
        
        # 准备测试数据
        test_market_data = {
            'fund_code': '518880',
            'technical_analysis': {
                'indicators': {
                    'ma5': 7.4144,
                    'ma20': 7.3901,
                    'macd': -0.0032,
                    'macd_signal': -0.0049,
                    'rsi': 49.56
                }
            },
            'gua_data': {
                'gua_score': 0.323,
                'main_gua': '乾为天',
                'is_buy_gua': True
            },
            'czsc_structure': {
                'fx_list': [
                    {'fx_mark': 'd', 'fx_price': 7.3780, 'dt': '2025-07-16 11:00:00'},
                    {'fx_mark': 'g', 'fx_price': 7.4110, 'dt': '2025-07-17 10:30:00'}
                ],
                'structure_analysis': {
                    'current_trend': '上升趋势',
                    'structure_strength': 0.70
                }
            }
        }
        
        print("📊 使用优化后的参数进行分析...")
        result = analyzer.analyze_market_narrative(test_market_data, '518880')
        
        if 'error' not in result:
            print("✅ 分析成功")
            print(f"📊 响应质量评估:")
            
            # 评估响应质量
            quality_score = 0
            
            # 检查各个字段的内容丰富度
            market_drivers = result.get('market_drivers', [])
            if market_drivers and len(market_drivers) > 0:
                avg_length = sum(len(str(d)) for d in market_drivers) / len(market_drivers)
                print(f"   市场驱动因素: {len(market_drivers)}个，平均长度{avg_length:.0f}字符")
                quality_score += min(20, len(market_drivers) * 5 + avg_length / 10)
            
            strategy = result.get('strategy_suggestion', '')
            if strategy:
                print(f"   策略建议长度: {len(strategy)}字符")
                quality_score += min(30, len(strategy) / 10)
            
            key_insights = result.get('key_insights', '')
            if key_insights:
                print(f"   核心洞察长度: {len(key_insights)}字符")
                quality_score += min(20, len(key_insights) / 10)
            
            raw_response = result.get('raw_response', '')
            if raw_response:
                print(f"   原始响应长度: {len(raw_response)}字符")
                quality_score += min(30, len(raw_response) / 100)
            
            print(f"   综合质量评分: {quality_score:.1f}/100")
            
            if quality_score > 70:
                print("🎉 LLM分析质量优秀！")
            elif quality_score > 50:
                print("✅ LLM分析质量良好")
            else:
                print("⚠️ LLM分析质量需要改进")
            
            return True
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 参数比较失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主演示函数"""
    print("🎯 LLM分析优化演示")
    print("="*80)
    
    demos = [
        ("增强版LLM分析演示", demo_enhanced_llm_analysis),
        ("LLM参数效果比较", compare_llm_parameters),
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*20} {demo_name} {'='*20}")
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name}执行失败: {e}")
            results.append((demo_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 LLM优化演示结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 演示结果: {passed}/{total} 成功")
    print(f"⏰ 演示完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 LLM分析优化成功！")
        print("\n💡 优化效果:")
        print("   ✅ 增加了max_tokens=3000，获得更详细的分析")
        print("   ✅ 优化了提示词，要求更深度的分析")
        print("   ✅ 增加了具体的操作建议要求")
        print("   ✅ 强化了CZSC缠论分析的专业性")
        print("   ✅ 提供了更丰富的市场洞察")
    else:
        print("⚠️ 部分演示失败，可能需要进一步优化")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
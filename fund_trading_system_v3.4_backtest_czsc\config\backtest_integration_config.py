"""
回测集成配置
管理回测功能在交易系统中的集成参数
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum


class BacktestMode(Enum):
    """回测模式"""
    DISABLED = "disabled"           # 禁用回测
    VALIDATION_ONLY = "validation"  # 仅验证，不调整决策
    FULL_INTEGRATION = "full"       # 完全集成，调整决策


class SignalQualityThreshold(Enum):
    """信号质量阈值"""
    STRICT = 0.8      # 严格模式
    NORMAL = 0.6      # 正常模式
    RELAXED = 0.4     # 宽松模式


@dataclass
class BacktestIntegrationConfig:
    """回测集成配置"""
    
    # 基础配置
    enabled: bool = True
    mode: BacktestMode = BacktestMode.FULL_INTEGRATION
    
    # 回测参数
    lookback_days: int = 60          # 回测历史天数
    initial_capital: float = 100000  # 初始资金
    transaction_cost: float = 0.001  # 交易成本
    slippage: float = 0.0005        # 滑点
    
    # 信号验证参数
    min_trades: int = 5                                    # 最少交易次数
    quality_threshold: SignalQualityThreshold = SignalQualityThreshold.NORMAL  # 质量阈值
    confidence_adjustment_limit: float = 0.3               # 置信度调整限制
    
    # 强度优化参数
    strength_optimization_enabled: bool = True
    strength_search_range: float = 0.3      # 强度搜索范围
    strength_search_step: float = 0.1       # 强度搜索步长
    
    # 决策调整参数
    decision_adjustment_enabled: bool = True
    reject_threshold: float = 0.3           # 拒绝信号的质量阈值
    reduce_threshold: float = 0.5           # 降低信号的质量阈值
    enhance_threshold: float = 0.8          # 增强信号的质量阈值
    
    # 风险控制参数
    max_drawdown_limit: float = 0.2         # 最大回撤限制
    min_sharpe_ratio: float = 0.0           # 最小夏普比率
    max_volatility: float = 0.5             # 最大波动率
    
    # 性能权重
    sharpe_weight: float = 0.3              # 夏普比率权重
    return_weight: float = 0.25             # 收益率权重
    win_rate_weight: float = 0.25           # 胜率权重
    drawdown_weight: float = 0.2            # 回撤权重
    
    # 缓存配置
    cache_enabled: bool = True
    cache_duration_hours: int = 24          # 缓存持续时间
    
    # 日志配置
    detailed_logging: bool = True
    log_backtest_results: bool = True
    log_decision_adjustments: bool = True


# 预定义配置模板
class BacktestConfigTemplates:
    """回测配置模板"""
    
    @staticmethod
    def conservative_config() -> BacktestIntegrationConfig:
        """保守型配置"""
        config = BacktestIntegrationConfig()
        config.quality_threshold = SignalQualityThreshold.STRICT
        config.reject_threshold = 0.5
        config.reduce_threshold = 0.7
        config.enhance_threshold = 0.9
        config.confidence_adjustment_limit = 0.2
        config.max_drawdown_limit = 0.15
        config.min_sharpe_ratio = 0.5
        return config
    
    @staticmethod
    def aggressive_config() -> BacktestIntegrationConfig:
        """激进型配置"""
        config = BacktestIntegrationConfig()
        config.quality_threshold = SignalQualityThreshold.RELAXED
        config.reject_threshold = 0.2
        config.reduce_threshold = 0.3
        config.enhance_threshold = 0.6
        config.confidence_adjustment_limit = 0.4
        config.max_drawdown_limit = 0.3
        config.min_sharpe_ratio = -0.5
        return config
    
    @staticmethod
    def research_config() -> BacktestIntegrationConfig:
        """研究型配置"""
        config = BacktestIntegrationConfig()
        config.lookback_days = 120
        config.detailed_logging = True
        config.log_backtest_results = True
        config.log_decision_adjustments = True
        config.cache_enabled = False  # 研究模式不使用缓存
        return config
    
    @staticmethod
    def validation_only_config() -> BacktestIntegrationConfig:
        """仅验证配置"""
        config = BacktestIntegrationConfig()
        config.mode = BacktestMode.VALIDATION_ONLY
        config.decision_adjustment_enabled = False
        config.strength_optimization_enabled = False
        return config
    
    @staticmethod
    def disabled_config() -> BacktestIntegrationConfig:
        """禁用回测配置"""
        config = BacktestIntegrationConfig()
        config.enabled = False
        config.mode = BacktestMode.DISABLED
        return config


class BacktestIntegrationManager:
    """回测集成管理器"""
    
    def __init__(self, config: Optional[BacktestIntegrationConfig] = None):
        self.config = config or BacktestIntegrationConfig()
        self._validate_config()
    
    def _validate_config(self):
        """验证配置参数"""
        if self.config.lookback_days < 20:
            raise ValueError("回测历史天数不能少于20天")
        
        if self.config.initial_capital <= 0:
            raise ValueError("初始资金必须大于0")
        
        if not (0 <= self.config.transaction_cost <= 0.01):
            raise ValueError("交易成本应在0-1%之间")
        
        if not (0 <= self.config.confidence_adjustment_limit <= 0.5):
            raise ValueError("置信度调整限制应在0-50%之间")
    
    def get_backtest_params(self) -> Dict:
        """获取回测参数"""
        return {
            'initial_capital': self.config.initial_capital,
            'transaction_cost': self.config.transaction_cost,
            'slippage': self.config.slippage,
            'min_holding_period': 1
        }
    
    def get_validation_params(self) -> Dict:
        """获取验证参数"""
        return {
            'lookback_days': self.config.lookback_days,
            'min_trades': self.config.min_trades,
            'quality_threshold': self.config.quality_threshold.value
        }
    
    def get_optimization_params(self) -> Dict:
        """获取优化参数"""
        return {
            'enabled': self.config.strength_optimization_enabled,
            'search_range': self.config.strength_search_range,
            'search_step': self.config.strength_search_step
        }
    
    def get_decision_adjustment_params(self) -> Dict:
        """获取决策调整参数"""
        return {
            'enabled': self.config.decision_adjustment_enabled,
            'reject_threshold': self.config.reject_threshold,
            'reduce_threshold': self.config.reduce_threshold,
            'enhance_threshold': self.config.enhance_threshold,
            'confidence_limit': self.config.confidence_adjustment_limit
        }
    
    def get_quality_weights(self) -> Dict[str, float]:
        """获取质量评分权重"""
        return {
            'sharpe_ratio': self.config.sharpe_weight,
            'total_return': self.config.return_weight,
            'win_rate': self.config.win_rate_weight,
            'max_drawdown': self.config.drawdown_weight
        }
    
    def should_enable_backtest(self) -> bool:
        """是否启用回测"""
        return self.config.enabled and self.config.mode != BacktestMode.DISABLED
    
    def should_adjust_decisions(self) -> bool:
        """是否调整决策"""
        return (self.config.enabled and 
                self.config.mode == BacktestMode.FULL_INTEGRATION and
                self.config.decision_adjustment_enabled)
    
    def should_optimize_strength(self) -> bool:
        """是否优化强度"""
        return (self.config.enabled and
                self.config.strength_optimization_enabled)
    
    def get_risk_limits(self) -> Dict[str, float]:
        """获取风险限制"""
        return {
            'max_drawdown': self.config.max_drawdown_limit,
            'min_sharpe_ratio': self.config.min_sharpe_ratio,
            'max_volatility': self.config.max_volatility
        }
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'enabled': self.config.enabled,
            'mode': self.config.mode.value,
            'lookback_days': self.config.lookback_days,
            'initial_capital': self.config.initial_capital,
            'transaction_cost': self.config.transaction_cost,
            'slippage': self.config.slippage,
            'min_trades': self.config.min_trades,
            'quality_threshold': self.config.quality_threshold.value,
            'confidence_adjustment_limit': self.config.confidence_adjustment_limit,
            'strength_optimization_enabled': self.config.strength_optimization_enabled,
            'decision_adjustment_enabled': self.config.decision_adjustment_enabled,
            'reject_threshold': self.config.reject_threshold,
            'reduce_threshold': self.config.reduce_threshold,
            'enhance_threshold': self.config.enhance_threshold,
            'performance_weights': self.get_quality_weights(),
            'risk_limits': self.get_risk_limits()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict) -> 'BacktestIntegrationManager':
        """从字典创建"""
        config = BacktestIntegrationConfig()
        
        # 基础配置
        config.enabled = config_dict.get('enabled', True)
        mode_str = config_dict.get('mode', 'full')
        config.mode = BacktestMode(mode_str)
        
        # 回测参数
        config.lookback_days = config_dict.get('lookback_days', 60)
        config.initial_capital = config_dict.get('initial_capital', 100000)
        config.transaction_cost = config_dict.get('transaction_cost', 0.001)
        config.slippage = config_dict.get('slippage', 0.0005)
        
        # 验证参数
        config.min_trades = config_dict.get('min_trades', 5)
        threshold_value = config_dict.get('quality_threshold', 0.6)
        config.quality_threshold = SignalQualityThreshold(threshold_value)
        config.confidence_adjustment_limit = config_dict.get('confidence_adjustment_limit', 0.3)
        
        # 优化参数
        config.strength_optimization_enabled = config_dict.get('strength_optimization_enabled', True)
        config.decision_adjustment_enabled = config_dict.get('decision_adjustment_enabled', True)
        
        # 阈值参数
        config.reject_threshold = config_dict.get('reject_threshold', 0.3)
        config.reduce_threshold = config_dict.get('reduce_threshold', 0.5)
        config.enhance_threshold = config_dict.get('enhance_threshold', 0.8)
        
        return cls(config)


# 全局配置实例
DEFAULT_BACKTEST_CONFIG = BacktestIntegrationManager()

# 配置选择函数
def get_backtest_config(config_type: str = 'default') -> BacktestIntegrationManager:
    """
    获取回测配置
    
    Args:
        config_type: 配置类型 ('default', 'conservative', 'aggressive', 'research', 'validation', 'disabled')
    
    Returns:
        BacktestIntegrationManager: 配置管理器实例
    """
    if config_type == 'conservative':
        return BacktestIntegrationManager(BacktestConfigTemplates.conservative_config())
    elif config_type == 'aggressive':
        return BacktestIntegrationManager(BacktestConfigTemplates.aggressive_config())
    elif config_type == 'research':
        return BacktestIntegrationManager(BacktestConfigTemplates.research_config())
    elif config_type == 'validation':
        return BacktestIntegrationManager(BacktestConfigTemplates.validation_only_config())
    elif config_type == 'disabled':
        return BacktestIntegrationManager(BacktestConfigTemplates.disabled_config())
    else:
        return DEFAULT_BACKTEST_CONFIG
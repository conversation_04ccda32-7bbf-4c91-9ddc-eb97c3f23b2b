import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from datetime import datetime, timedelta
import warnings
import logging
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import joblib
import os

warnings.filterwarnings('ignore')

# 尝试导入机器学习框架
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 移除tensorflow依赖，只使用PyTorch
TF_AVAILABLE = False

from .backtest_engine import BacktestEngine


class MLEnhancedBacktestEngine(BacktestEngine):
    """
    机器学习增强的回测引擎
    集成ML预测模型到回测流程中，提供基于预测的交易信号生成
    """
    
    def __init__(self, initial_capital: float = 100000, transaction_cost: float = 0.001,
                 slippage: float = 0.0005, min_holding_period: int = 1,
                 ml_config: Optional[Dict] = None):
        """
        初始化ML增强回测引擎
        
        Args:
            initial_capital: 初始资金
            transaction_cost: 交易成本
            slippage: 滑点
            min_holding_period: 最小持仓期
            ml_config: ML配置字典
        """
        super().__init__(initial_capital, transaction_cost, slippage, min_holding_period)
        
        # ML配置
        self.ml_config = ml_config or self._get_default_ml_config()
        
        # ML组件
        self.models = {}  # 存储训练好的模型
        self.scalers = {}  # 存储特征缩放器
        self.feature_columns = []  # 特征列名
        self.prediction_history = []  # 预测历史
        self.model_performance = {}  # 模型性能指标
        
        # ML回测状态
        self.ml_enabled = False
        self.current_model = None
        self.prediction_window = self.ml_config.get('prediction_window', 5)
        self.retrain_frequency = self.ml_config.get('retrain_frequency', 100)
        self.min_train_samples = self.ml_config.get('min_train_samples', 200)
        
        # 日志设置
        self.logger = logging.getLogger(__name__)
        self.ml_log = []
        
    def _get_default_ml_config(self) -> Dict:
        """获取默认ML配置"""
        return {
            'model_type': 'lstm',  # lstm, gru, transformer, ensemble
            'sequence_length': 30,
            'prediction_window': 5,
            'retrain_frequency': 100,
            'min_train_samples': 200,
            'validation_split': 0.2,
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True
            },
            'model_params': {
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'learning_rate': 0.001,
                'epochs': 50,
                'batch_size': 32
            },
            'signal_generation': {
                'method': 'threshold',  # threshold, quantile, classification
                'buy_threshold': 0.6,
                'sell_threshold': 0.4,
                'confidence_threshold': 0.5
            }
        }
    
    def load_ml_model(self, model_path: str, model_type: str = 'pytorch') -> bool:
        """
        加载预训练的ML模型
        
        Args:
            model_path: 模型文件路径
            model_type: 模型类型 ('pytorch', 'tensorflow', 'sklearn')
            
        Returns:
            是否加载成功
        """
        try:
            if model_type == 'pytorch' and TORCH_AVAILABLE:
                model = torch.load(model_path, map_location='cpu')
                self.models['main'] = model
                self.ml_enabled = True
                self.current_model = 'main'
                
            elif model_type == 'tensorflow':
                # TensorFlow不再支持，建议使用PyTorch
                self.ml_log.append("TensorFlow models are no longer supported. Please use PyTorch instead.")
                return False
                
            elif model_type == 'sklearn':
                model = joblib.load(model_path)
                self.models['main'] = model
                self.ml_enabled = True
                self.current_model = 'main'
            
            # 尝试加载对应的scaler
            scaler_path = model_path.replace('.pkl', '_scaler.pkl').replace('.pth', '_scaler.pkl')
            if os.path.exists(scaler_path):
                self.scalers['main'] = joblib.load(scaler_path)
            
            self.ml_log.append(f"Successfully loaded {model_type} model from {model_path}")
            return True
            
        except Exception as e:
            self.ml_log.append(f"Failed to load model: {e}")
            return False
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备ML特征
        
        Args:
            data: 原始数据
            
        Returns:
            特征数据
        """
        features_df = data.copy()
        
        # 基础价格特征
        if self.ml_config['feature_engineering']['price_features']:
            features_df['returns'] = features_df['close'].pct_change()
            features_df['log_returns'] = np.log(features_df['close'] / features_df['close'].shift(1))
            features_df['price_ma_5'] = features_df['close'].rolling(5).mean()
            features_df['price_ma_20'] = features_df['close'].rolling(20).mean()
            features_df['price_ratio_ma5'] = features_df['close'] / features_df['price_ma_5']
            features_df['price_ratio_ma20'] = features_df['close'] / features_df['price_ma_20']
        
        # 技术指标特征
        if self.ml_config['feature_engineering']['technical_indicators']:
            # RSI
            delta = features_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features_df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = features_df['close'].ewm(span=12).mean()
            exp2 = features_df['close'].ewm(span=26).mean()
            features_df['macd'] = exp1 - exp2
            features_df['macd_signal'] = features_df['macd'].ewm(span=9).mean()
            features_df['macd_histogram'] = features_df['macd'] - features_df['macd_signal']
        
        # 成交量特征
        if self.ml_config['feature_engineering']['volume_features']:
            features_df['volume_ma_5'] = features_df['volume'].rolling(5).mean()
            features_df['volume_ma_20'] = features_df['volume'].rolling(20).mean()
            features_df['volume_ratio'] = features_df['volume'] / features_df['volume_ma_20']
            features_df['price_volume'] = features_df['close'] * features_df['volume']
        
        # 波动率特征
        if self.ml_config['feature_engineering']['volatility_features']:
            features_df['volatility_5'] = features_df['returns'].rolling(5).std()
            features_df['volatility_20'] = features_df['returns'].rolling(20).std()
            features_df['high_low_ratio'] = features_df['high'] / features_df['low']
            features_df['true_range'] = np.maximum(
                features_df['high'] - features_df['low'],
                np.maximum(
                    abs(features_df['high'] - features_df['close'].shift(1)),
                    abs(features_df['low'] - features_df['close'].shift(1))
                )
            )
            features_df['atr'] = features_df['true_range'].rolling(14).mean()
        
        # 移除无限值和NaN
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(method='ffill').fillna(0)
        
        return features_df
    
    def create_sequences(self, features: pd.DataFrame, target_col: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列序列用于ML训练/预测
        
        Args:
            features: 特征数据
            target_col: 目标列名
            
        Returns:
            (X, y) 序列数据
        """
        sequence_length = self.ml_config['sequence_length']
        
        # 选择特征列（排除目标列和时间列）
        feature_cols = [col for col in features.columns 
                       if col not in [target_col, 'dt', 'date', 'timestamp']]
        
        if not self.feature_columns:
            self.feature_columns = feature_cols
        
        X, y = [], []
        
        for i in range(sequence_length, len(features)):
            # 特征序列
            X.append(features[feature_cols].iloc[i-sequence_length:i].values)
            
            # 目标值（未来收益率）
            current_price = features[target_col].iloc[i]
            future_price = features[target_col].iloc[min(i + self.prediction_window, len(features) - 1)]
            future_return = (future_price - current_price) / current_price
            y.append(future_return)
        
        return np.array(X), np.array(y)

    def make_prediction(self, features: pd.DataFrame, model_name: str = 'main') -> Optional[float]:
        """
        使用ML模型进行预测

        Args:
            features: 特征数据
            model_name: 模型名称

        Returns:
            预测值
        """
        if not self.ml_enabled or model_name not in self.models:
            return None

        try:
            model = self.models[model_name]
            scaler = self.scalers.get(model_name)

            # 准备输入数据
            X, _ = self.create_sequences(features)
            if len(X) == 0:
                return None

            # 使用最后一个序列进行预测
            last_sequence = X[-1:]

            # 数据缩放
            if scaler:
                original_shape = last_sequence.shape
                last_sequence = scaler.transform(last_sequence.reshape(-1, last_sequence.shape[-1]))
                last_sequence = last_sequence.reshape(original_shape)

            # 模型预测
            if TORCH_AVAILABLE and isinstance(model, torch.nn.Module):
                model.eval()
                with torch.no_grad():
                    input_tensor = torch.FloatTensor(last_sequence)
                    prediction = model(input_tensor).numpy()[0]
            elif hasattr(model, 'predict'):
                # sklearn模型
                if len(last_sequence.shape) > 2:
                    # 如果是序列数据，需要重塑
                    prediction = model.predict(last_sequence.reshape(1, -1))[0]
                else:
                    prediction = model.predict(last_sequence)[0]
            else:
                # 未知模型类型
                self.ml_log.append("Unknown model type for prediction")
                return None

            return float(prediction)

        except Exception as e:
            self.ml_log.append(f"Prediction failed: {e}")
            return None

    def generate_ml_signals(self, features: pd.DataFrame) -> pd.Series:
        """
        基于ML预测生成交易信号

        Args:
            features: 特征数据

        Returns:
            交易信号序列
        """
        signals = pd.Series(0, index=features.index)

        if not self.ml_enabled:
            return signals

        signal_config = self.ml_config['signal_generation']
        method = signal_config['method']

        predictions = []

        # 滚动预测
        sequence_length = self.ml_config['sequence_length']
        for i in range(sequence_length, len(features)):
            current_features = features.iloc[:i+1]
            prediction = self.make_prediction(current_features)
            predictions.append(prediction if prediction is not None else 0)

        if not predictions:
            return signals

        predictions = np.array(predictions)

        # 根据方法生成信号
        if method == 'threshold':
            buy_threshold = signal_config['buy_threshold']
            sell_threshold = signal_config['sell_threshold']

            # 将预测转换为概率（使用sigmoid）
            probabilities = 1 / (1 + np.exp(-predictions * 10))  # 放大预测值

            buy_signals = probabilities > buy_threshold
            sell_signals = probabilities < sell_threshold

            signals.iloc[sequence_length:sequence_length+len(predictions)][buy_signals] = 1
            signals.iloc[sequence_length:sequence_length+len(predictions)][sell_signals] = -1

        elif method == 'quantile':
            # 基于分位数的信号生成
            upper_quantile = np.quantile(predictions, 0.8)
            lower_quantile = np.quantile(predictions, 0.2)

            buy_signals = predictions > upper_quantile
            sell_signals = predictions < lower_quantile

            signals.iloc[sequence_length:sequence_length+len(predictions)][buy_signals] = 1
            signals.iloc[sequence_length:sequence_length+len(predictions)][sell_signals] = -1

        # 记录预测历史
        for i, pred in enumerate(predictions):
            self.prediction_history.append({
                'timestamp': features.index[sequence_length + i],
                'prediction': pred,
                'signal': signals.iloc[sequence_length + i]
            })

        return signals

    def run_ml_backtest(self, data: pd.DataFrame, target_col: str = 'close',
                       train_model: bool = True) -> Dict[str, Any]:
        """
        运行ML增强回测

        Args:
            data: 价格数据
            target_col: 目标列名
            train_model: 是否训练模型

        Returns:
            回测结果
        """
        self.ml_log.append(f"Starting ML backtest with {len(data)} data points")

        # 准备特征
        features = self.prepare_features(data)

        # 训练模型（如果需要）
        if train_model and len(features) >= self.min_train_samples:
            self._train_simple_model(features, target_col)

        # 生成ML信号
        ml_signals = self.generate_ml_signals(features)

        # 运行回测
        try:
            # 尝试使用第一个run_backtest方法（来自BacktestEngine）
            backtest_results = super().run_backtest(data, ml_signals)
        except Exception as e:
            # 如果失败，创建基本的回测结果
            self.ml_log.append(f"Backtest failed: {e}, using simplified results")
            backtest_results = {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'trade_count': 0,
                'final_value': self.initial_capital,
                'volatility': 0.0
            }

        # 计算ML特定指标
        ml_metrics = self._calculate_ml_metrics()

        # 合并结果
        enhanced_results = {
            **backtest_results,
            'ml_metrics': ml_metrics,
            'prediction_history': self.prediction_history.copy(),
            'ml_log': self.ml_log.copy(),
            'feature_columns': self.feature_columns.copy(),
            'ml_config': self.ml_config.copy()
        }

        return enhanced_results

    def _train_simple_model(self, features: pd.DataFrame, target_col: str):
        """
        训练简单的ML模型

        Args:
            features: 特征数据
            target_col: 目标列名
        """
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.model_selection import train_test_split

            # 创建序列数据
            X, y = self.create_sequences(features, target_col)

            if len(X) < self.min_train_samples:
                self.ml_log.append("Insufficient data for training")
                return

            # 重塑数据用于sklearn
            X_reshaped = X.reshape(X.shape[0], -1)

            # 分割训练和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X_reshaped, y, test_size=self.ml_config['validation_split'],
                shuffle=False
            )

            # 训练模型
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )

            # 特征缩放
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)

            # 训练
            model.fit(X_train_scaled, y_train)

            # 验证
            val_predictions = model.predict(X_val_scaled)
            val_mse = np.mean((y_val - val_predictions) ** 2)
            val_mae = np.mean(np.abs(y_val - val_predictions))

            # 保存模型和缩放器
            self.models['main'] = model
            self.scalers['main'] = scaler
            self.ml_enabled = True
            self.current_model = 'main'

            # 记录性能
            self.model_performance['main'] = {
                'validation_mse': val_mse,
                'validation_mae': val_mae,
                'training_samples': len(X_train),
                'validation_samples': len(X_val),
                'feature_count': X_train.shape[1]
            }

            self.ml_log.append(f"Model trained successfully. Val MSE: {val_mse:.6f}, Val MAE: {val_mae:.6f}")

        except Exception as e:
            self.ml_log.append(f"Model training failed: {e}")

    def _calculate_ml_metrics(self) -> Dict[str, Any]:
        """
        计算ML特定的性能指标

        Returns:
            ML指标字典
        """
        if not self.prediction_history:
            return {}

        predictions = [p['prediction'] for p in self.prediction_history]
        signals = [p['signal'] for p in self.prediction_history]

        metrics = {
            'total_predictions': len(predictions),
            'prediction_mean': np.mean(predictions),
            'prediction_std': np.std(predictions),
            'prediction_min': np.min(predictions),
            'prediction_max': np.max(predictions),
            'signal_distribution': {
                'buy_signals': sum(1 for s in signals if s == 1),
                'sell_signals': sum(1 for s in signals if s == -1),
                'hold_signals': sum(1 for s in signals if s == 0)
            },
            'model_performance': self.model_performance.copy()
        }

        return metrics

"""
情绪维度评估器
负责评估市场情绪和投资者心理状态
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import SentimentState


class SentimentEvaluator:
    """
    @class SentimentEvaluator
    @brief 情绪维度评估器
    @details 负责评估市场情绪和投资者心理状态
    """
    
    def __init__(self):
        self.name = "SentimentEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估情绪维度
        @param data: 市场数据
        @return: 情绪评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            gua_data = data.get('gua_data', {})
            
            # 获取情绪相关数据
            change_rate = price_data.get('change_rate', 0)
            rsi = technical_data.get('rsi', 50)
            volume_ratio = data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            gua_score = gua_data.get('gua_score', 0)
            
            # 情绪评分计算
            sentiment_signals = []
            sentiment_score = 0.0
            
            # 1. RSI情绪指标
            if rsi >= 80:
                rsi_sentiment = "极度贪婪"
                rsi_strength = min(1.0, (rsi - 80) / 20)
                sentiment_score += 0.9 + rsi_strength * 0.1
                sentiment_signals.append(f"RSI极度贪婪({rsi:.1f})")
            elif rsi >= 70:
                rsi_sentiment = "贪婪"
                rsi_strength = (rsi - 70) / 10
                sentiment_score += 0.7 + rsi_strength * 0.2
                sentiment_signals.append(f"RSI贪婪({rsi:.1f})")
            elif rsi <= 20:
                rsi_sentiment = "极度恐慌"
                rsi_strength = min(1.0, (20 - rsi) / 20)
                sentiment_score -= 0.9 + rsi_strength * 0.1
                sentiment_signals.append(f"RSI极度恐慌({rsi:.1f})")
            elif rsi <= 30:
                rsi_sentiment = "恐慌"
                rsi_strength = (30 - rsi) / 10
                sentiment_score -= 0.7 + rsi_strength * 0.2
                sentiment_signals.append(f"RSI恐慌({rsi:.1f})")
            else:
                rsi_sentiment = "中性"
                neutral_factor = 1.0 - abs(rsi - 50) / 20
                sentiment_score += (neutral_factor - 0.5) * 0.2
                sentiment_signals.append(f"RSI中性({rsi:.1f})")
            
            # 2. 价格变化情绪
            if change_rate >= 4:
                price_sentiment = "狂热"
                price_strength = min(1.0, change_rate / 8)
                sentiment_score += 0.3 + price_strength * 0.2
                sentiment_signals.append(f"价格狂热(+{change_rate:.2f}%)")
            elif change_rate >= 2:
                price_sentiment = "乐观"
                price_strength = change_rate / 4
                sentiment_score += 0.1 + price_strength * 0.2
                sentiment_signals.append(f"价格乐观(+{change_rate:.2f}%)")
            elif change_rate <= -4:
                price_sentiment = "恐慌"
                price_strength = min(1.0, abs(change_rate) / 8)
                sentiment_score -= 0.3 + price_strength * 0.2
                sentiment_signals.append(f"价格恐慌({change_rate:.2f}%)")
            elif change_rate <= -2:
                price_sentiment = "悲观"
                price_strength = abs(change_rate) / 4
                sentiment_score -= 0.1 + price_strength * 0.2
                sentiment_signals.append(f"价格悲观({change_rate:.2f}%)")
            else:
                price_sentiment = "平静"
                sentiment_signals.append(f"价格平静({change_rate:+.2f}%)")
            
            # 3. 成交量情绪
            if volume_ratio >= 2.0:
                volume_sentiment = "高涨"
                vol_strength = min(1.0, (volume_ratio - 1) / 2)
                sentiment_score += vol_strength * 0.15
                sentiment_signals.append(f"成交高涨({volume_ratio:.2f}倍)")
            elif volume_ratio <= 0.5:
                volume_sentiment = "低迷"
                vol_strength = (1 - volume_ratio) / 0.5
                sentiment_score -= vol_strength * 0.15
                sentiment_signals.append(f"成交低迷({volume_ratio:.2f}倍)")
            else:
                volume_sentiment = "正常"
                sentiment_signals.append(f"成交正常({volume_ratio:.2f}倍)")
            
            # 4. 卦象情绪（传统智慧）
            if gua_score >= 0.7:
                gua_sentiment = "吉"
                sentiment_score += gua_score * 0.1
                sentiment_signals.append(f"卦象吉利({gua_score:.2f})")
            elif gua_score <= -0.7:
                gua_sentiment = "凶"
                sentiment_score += gua_score * 0.1
                sentiment_signals.append(f"卦象凶险({gua_score:.2f})")
            else:
                gua_sentiment = "中性"
                sentiment_signals.append(f"卦象中性({gua_score:.2f})")
            
            # 情绪状态判断
            if sentiment_score >= 0.8:
                sentiment_state = SentimentState.EXTREME_GREED
            elif sentiment_score >= 0.3:
                sentiment_state = SentimentState.GREED
            elif sentiment_score >= -0.3:
                sentiment_state = SentimentState.NEUTRAL
            elif sentiment_score >= -0.8:
                sentiment_state = SentimentState.PANIC
            else:
                sentiment_state = SentimentState.EXTREME_PANIC
            
            # 置信度计算
            signal_strength = abs(sentiment_score)
            confidence = min(0.95, max(0.3, 0.5 + signal_strength * 0.4))
            
            # 数据质量评估
            data_quality = "good" if all([rsi, change_rate is not None]) else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="情绪",
                state=sentiment_state,
                score=sentiment_score,
                confidence=confidence,
                signals=sentiment_signals,
                data_quality=data_quality,
                details={
                    'rsi_sentiment': rsi_sentiment,
                    'price_sentiment': price_sentiment,
                    'volume_sentiment': volume_sentiment,
                    'gua_sentiment': gua_sentiment
                },
                indicators={
                    'rsi': rsi,
                    'change_rate': change_rate,
                    'volume_ratio': volume_ratio,
                    'gua_score': gua_score,
                    'sentiment_score': sentiment_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Sentiment evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="情绪",
                state=SentimentState.NEUTRAL,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BacktestEngine:
    """
    回测引擎模块
    负责无前瞻性偏差的回测、性能指标计算
    """
    
    def __init__(self, initial_capital: float = 100000, transaction_cost: float = 0.001,
                 slippage: float = 0.0005, min_holding_period: int = 1):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            transaction_cost: 交易成本（比例）
            slippage: 滑点（比例）
            min_holding_period: 最小持仓期（防止过度交易）
        """
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost
        self.slippage = slippage
        self.min_holding_period = min_holding_period
        self.backtest_results = {}
        self.trade_log = []
        self.position_history = []
        self.backtest_log = []
        
    def generate_signals(self, predictions: np.ndarray, method: str = 'threshold',
                        threshold: float = 0.6, **kwargs) -> np.ndarray:
        """
        基于预测概率生成交易信号
        
        Args:
            predictions: 预测概率或值
            method: 信号生成方法 ('threshold', 'quantile', 'momentum')
            threshold: 阈值
            **kwargs: 其他参数
            
        Returns:
            交易信号数组 (1: 买入, 0: 持有, -1: 卖出)
        """
        signals = np.zeros(len(predictions))
        
        if method == 'threshold':
            # 基于阈值的信号生成
            upper_threshold = threshold
            lower_threshold = 1 - threshold if threshold < 1 else -threshold
            
            signals[predictions > upper_threshold] = 1  # 买入信号
            signals[predictions < lower_threshold] = -1  # 卖出信号
            
        elif method == 'quantile':
            # 基于分位数的信号生成
            upper_quantile = kwargs.get('upper_quantile', 0.8)
            lower_quantile = kwargs.get('lower_quantile', 0.2)
            
            upper_threshold = np.quantile(predictions, upper_quantile)
            lower_threshold = np.quantile(predictions, lower_quantile)
            
            signals[predictions > upper_threshold] = 1
            signals[predictions < lower_threshold] = -1
            
        elif method == 'momentum':
            # 基于动量的信号生成
            window = kwargs.get('window', 5)
            
            # 计算移动平均
            ma = pd.Series(predictions).rolling(window=window).mean()
            
            # 基于预测值与移动平均的关系生成信号
            signals[predictions > ma * (1 + threshold)] = 1
            signals[predictions < ma * (1 - threshold)] = -1
        
        self.backtest_log.append(f"Generated signals using {method} method: {np.sum(signals == 1)} buy, {np.sum(signals == -1)} sell")
        
        return signals
    
    def apply_signal_filters(self, signals: np.ndarray, prices: pd.Series) -> np.ndarray:
        """
        应用信号过滤规则
        
        Args:
            signals: 原始信号
            prices: 价格序列
            
        Returns:
            过滤后的信号
        """
        filtered_signals = signals.copy()
        
        # 最小持仓期过滤
        position = 0
        holding_days = 0
        
        for i in range(len(filtered_signals)):
            if position != 0:
                holding_days += 1
                
                # 如果还没到最小持仓期，取消反向信号
                if holding_days < self.min_holding_period:
                    if (position > 0 and filtered_signals[i] == -1) or (position < 0 and filtered_signals[i] == 1):
                        filtered_signals[i] = 0
                
            # 更新持仓
            if filtered_signals[i] != 0:
                position = filtered_signals[i]
                holding_days = 0
        
        # 价格跳空过滤（避免在跳空时交易）
        price_returns = prices.pct_change().abs()
        large_gap_threshold = price_returns.quantile(0.95)  # 前5%的大幅波动
        
        for i in range(1, len(filtered_signals)):
            if price_returns.iloc[i] > large_gap_threshold:
                filtered_signals[i] = 0  # 取消大幅跳空时的信号
        
        self.backtest_log.append(f"Applied signal filters. Signals reduced from {np.sum(signals != 0)} to {np.sum(filtered_signals != 0)}")
        
        return filtered_signals
    
    def calculate_position_size(self, signal: int, portfolio_value: float, 
                              volatility: float, max_position_pct: float = 0.1) -> float:
        """
        计算仓位大小
        
        Args:
            signal: 交易信号
            portfolio_value: 组合价值
            volatility: 波动率
            max_position_pct: 最大仓位比例
            
        Returns:
            仓位大小（金额）
        """
        if signal == 0:
            return 0
        
        # 基于波动率调整仓位大小（波动率越高，仓位越小）
        volatility_adjustment = 1 / (1 + volatility * 10)  # 简单的波动率调整
        
        # 计算仓位大小
        position_size = portfolio_value * max_position_pct * volatility_adjustment
        
        return position_size
    
    def run_backtest(self, predictions: np.ndarray, prices: pd.Series, 
                    timestamps: pd.DatetimeIndex, signal_method: str = 'threshold',
                    **signal_params) -> pd.DataFrame:
        """
        执行无前瞻性偏差的回测
        
        Args:
            predictions: 预测值数组
            prices: 价格序列
            timestamps: 时间戳索引
            signal_method: 信号生成方法
            **signal_params: 信号参数
            
        Returns:
            回测结果DataFrame
        """
        self.backtest_log.append(f"Starting backtest with {len(predictions)} predictions")
        
        # 确保数据对齐
        min_length = min(len(predictions), len(prices), len(timestamps))
        predictions = predictions[:min_length]
        prices = prices.iloc[:min_length]
        timestamps = timestamps[:min_length]
        
        # 生成交易信号
        signals = self.generate_signals(predictions, signal_method, **signal_params)
        signals = self.apply_signal_filters(signals, prices)
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        cash = self.initial_capital
        position = 0  # 当前持仓（股数）
        position_value = 0
        
        results = []
        
        # 计算价格波动率（用于仓位计算）
        price_volatility = prices.pct_change().rolling(window=20).std()
        
        for i in range(len(signals)):
            current_price = prices.iloc[i]
            current_signal = signals[i]
            current_volatility = price_volatility.iloc[i] if not pd.isna(price_volatility.iloc[i]) else 0.02
            
            # 计算当前组合价值
            position_value = position * current_price
            portfolio_value = cash + position_value
            
            # 处理交易信号
            if current_signal != 0:
                # 计算目标仓位大小
                target_position_value = self.calculate_position_size(
                    current_signal, portfolio_value, current_volatility)
                
                if current_signal == 1:  # 买入信号
                    if cash > target_position_value:
                        # 计算可买入的股数（考虑交易成本和滑点）
                        effective_price = current_price * (1 + self.transaction_cost + self.slippage)
                        shares_to_buy = target_position_value / effective_price
                        
                        # 执行买入
                        cost = shares_to_buy * effective_price
                        cash -= cost
                        position += shares_to_buy
                        
                        # 记录交易
                        self.trade_log.append({
                            'timestamp': timestamps[i],
                            'action': 'BUY',
                            'shares': shares_to_buy,
                            'price': effective_price,
                            'cost': cost,
                            'portfolio_value': portfolio_value
                        })
                
                elif current_signal == -1 and position > 0:  # 卖出信号
                    # 卖出全部持仓（考虑交易成本和滑点）
                    effective_price = current_price * (1 - self.transaction_cost - self.slippage)
                    proceeds = position * effective_price
                    
                    # 记录交易
                    self.trade_log.append({
                        'timestamp': timestamps[i],
                        'action': 'SELL',
                        'shares': position,
                        'price': effective_price,
                        'proceeds': proceeds,
                        'portfolio_value': portfolio_value
                    })
                    
                    # 执行卖出
                    cash += proceeds
                    position = 0
            
            # 记录当期状态
            position_value = position * current_price
            portfolio_value = cash + position_value
            
            results.append({
                'timestamp': timestamps[i],
                'price': current_price,
                'prediction': predictions[i],
                'signal': current_signal,
                'position': position,
                'cash': cash,
                'position_value': position_value,
                'portfolio_value': portfolio_value,
                'returns': (portfolio_value - self.initial_capital) / self.initial_capital
            })
            
            # 记录持仓历史
            self.position_history.append({
                'timestamp': timestamps[i],
                'position': position,
                'position_value': position_value,
                'cash': cash,
                'total_value': portfolio_value
            })
        
        # 转换为DataFrame
        backtest_df = pd.DataFrame(results)
        backtest_df.set_index('timestamp', inplace=True)
        
        # 计算收益率
        backtest_df['strategy_returns'] = backtest_df['portfolio_value'].pct_change()
        backtest_df['benchmark_returns'] = prices.pct_change()
        
        # 计算累计收益
        backtest_df['cumulative_strategy_returns'] = (1 + backtest_df['strategy_returns']).cumprod() - 1
        backtest_df['cumulative_benchmark_returns'] = (1 + backtest_df['benchmark_returns']).cumprod() - 1
        
        self.backtest_results = backtest_df
        self.backtest_log.append(f"Backtest completed. Total trades: {len(self.trade_log)}")
        
        return backtest_df
    
    def calculate_metrics(self, backtest_results: pd.DataFrame) -> Dict:
        """
        计算回测性能指标
        
        Args:
            backtest_results: 回测结果DataFrame
            
        Returns:
            性能指标字典
        """
        strategy_returns = backtest_results['strategy_returns'].dropna()
        benchmark_returns = backtest_results['benchmark_returns'].dropna()
        
        # 基本收益指标
        total_return = backtest_results['cumulative_strategy_returns'].iloc[-1]
        benchmark_total_return = backtest_results['cumulative_benchmark_returns'].iloc[-1]
        
        # 年化收益率（假设252个交易日）
        trading_days = len(strategy_returns)
        years = trading_days / 252
        annualized_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
        benchmark_annualized_return = (1 + benchmark_total_return) ** (1/years) - 1 if years > 0 else 0
        
        # 波动率
        volatility = strategy_returns.std() * np.sqrt(252)
        benchmark_volatility = benchmark_returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率为2%
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
        benchmark_sharpe = (benchmark_annualized_return - risk_free_rate) / benchmark_volatility if benchmark_volatility > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + strategy_returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdowns.min()
        
        # 基准最大回撤
        benchmark_cumulative = (1 + benchmark_returns).cumprod()
        benchmark_rolling_max = benchmark_cumulative.expanding().max()
        benchmark_drawdowns = (benchmark_cumulative - benchmark_rolling_max) / benchmark_rolling_max
        benchmark_max_drawdown = benchmark_drawdowns.min()
        
        # 胜率
        positive_returns = strategy_returns[strategy_returns > 0]
        win_rate = len(positive_returns) / len(strategy_returns) if len(strategy_returns) > 0 else 0
        
        # 信息比率
        excess_returns = strategy_returns - benchmark_returns
        information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        # 索提诺比率（下行波动率）
        negative_returns = strategy_returns[strategy_returns < 0]
        downside_volatility = negative_returns.std() * np.sqrt(252) if len(negative_returns) > 0 else 0
        sortino_ratio = (annualized_return - risk_free_rate) / downside_volatility if downside_volatility > 0 else 0
        
        # 卡尔马比率
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        # 交易相关指标
        total_trades = len(self.trade_log)
        buy_trades = len([t for t in self.trade_log if t['action'] == 'BUY'])
        sell_trades = len([t for t in self.trade_log if t['action'] == 'SELL'])
        
        metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'information_ratio': information_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'benchmark_total_return': benchmark_total_return,
            'benchmark_annualized_return': benchmark_annualized_return,
            'benchmark_volatility': benchmark_volatility,
            'benchmark_sharpe': benchmark_sharpe,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'excess_return': total_return - benchmark_total_return
        }
        
        self.backtest_log.append("Performance metrics calculated")
        
        return metrics
    
    def get_trade_analysis(self) -> Dict:
        """
        获取交易分析
        
        Returns:
            交易分析结果
        """
        if not self.trade_log:
            return {'message': 'No trades executed'}
        
        trade_df = pd.DataFrame(self.trade_log)
        
        # 配对买卖交易分析
        buy_trades = trade_df[trade_df['action'] == 'BUY']
        sell_trades = trade_df[trade_df['action'] == 'SELL']
        
        trade_profits = []
        holding_periods = []
        
        # 简单配对（假设FIFO）
        for i in range(min(len(buy_trades), len(sell_trades))):
            buy_cost = buy_trades.iloc[i]['cost']
            sell_proceeds = sell_trades.iloc[i]['proceeds']
            profit = sell_proceeds - buy_cost
            profit_pct = profit / buy_cost
            
            trade_profits.append(profit_pct)
            
            # 计算持仓期
            buy_time = buy_trades.iloc[i]['timestamp']
            sell_time = sell_trades.iloc[i]['timestamp']
            holding_period = (sell_time - buy_time).days
            holding_periods.append(holding_period)
        
        if trade_profits:
            avg_profit = np.mean(trade_profits)
            win_rate = len([p for p in trade_profits if p > 0]) / len(trade_profits)
            avg_win = np.mean([p for p in trade_profits if p > 0]) if any(p > 0 for p in trade_profits) else 0
            avg_loss = np.mean([p for p in trade_profits if p < 0]) if any(p < 0 for p in trade_profits) else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            avg_holding_period = np.mean(holding_periods) if holding_periods else 0
        else:
            avg_profit = win_rate = avg_win = avg_loss = profit_factor = avg_holding_period = 0
        
        analysis = {
            'total_trades': len(self.trade_log),
            'completed_roundtrips': len(trade_profits),
            'avg_profit_per_trade': avg_profit,
            'trade_win_rate': win_rate,
            'avg_winning_trade': avg_win,
            'avg_losing_trade': avg_loss,
            'profit_factor': profit_factor,
            'avg_holding_period_days': avg_holding_period,
            'trade_profits': trade_profits,
            'holding_periods': holding_periods
        }
        
        return analysis
    
    def get_backtest_summary(self) -> Dict:
        """
        获取回测总结
        
        Returns:
            回测总结字典
        """
        if self.backtest_results.empty:
            return {'message': 'No backtest results available'}
        
        metrics = self.calculate_metrics(self.backtest_results)
        trade_analysis = self.get_trade_analysis()
        
        summary = {
            'backtest_period': {
                'start': self.backtest_results.index[0],
                'end': self.backtest_results.index[-1],
                'duration_days': (self.backtest_results.index[-1] - self.backtest_results.index[0]).days
            },
            'performance_metrics': metrics,
            'trade_analysis': trade_analysis,
            'final_portfolio_value': self.backtest_results['portfolio_value'].iloc[-1],
            'backtest_log': self.backtest_log.copy()
        }
        
        return summary 
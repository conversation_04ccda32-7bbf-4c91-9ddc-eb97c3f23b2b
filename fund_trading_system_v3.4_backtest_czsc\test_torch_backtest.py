#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyTorch版本的回测验证器
"""

import sys
import os
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_torch_availability():
    """测试PyTorch可用性"""
    print("🔍 测试PyTorch可用性...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        print(f"✅ 设备: {torch.device('cuda' if torch.cuda.is_available() else 'cpu')}")
        return True
    except ImportError as e:
        print(f"❌ PyTorch不可用: {e}")
        return False

def test_backtest_modules():
    """测试回测模块"""
    print("\n🔍 测试回测模块...")
    
    try:
        from backtest import NetworkArchitecture, ModelTrainer, DATA_PROCESSING_AVAILABLE
        print("✅ 回测模块导入成功")
        print(f"✅ 数据处理可用: {DATA_PROCESSING_AVAILABLE}")
        
        # 测试网络架构
        try:
            net_arch = NetworkArchitecture(input_shape=(10, 5), output_dim=1)
            print("✅ 网络架构初始化成功")
        except Exception as e:
            print(f"❌ 网络架构初始化失败: {e}")
            return False
        
        # 测试模型训练器
        try:
            trainer = ModelTrainer()
            print("✅ 模型训练器初始化成功")
        except Exception as e:
            print(f"❌ 模型训练器初始化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 回测模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backtest_validator():
    """测试回测验证器"""
    print("\n🔍 测试回测验证器...")
    
    try:
        from analyzers.backtest_signal_validator import BacktestSignalValidator
        
        validator = BacktestSignalValidator()
        print("✅ 完整版回测验证器初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 完整版回测验证器失败: {e}")
        
        # 尝试简化版
        try:
            from analyzers.simple_backtest_validator import SimpleBacktestValidator
            
            validator = SimpleBacktestValidator()
            print("✅ 简化版回测验证器初始化成功")
            return True
            
        except Exception as e2:
            print(f"❌ 简化版回测验证器也失败: {e2}")
            return False

def test_coordinator_integration():
    """测试协调器集成"""
    print("\n🔍 测试协调器集成...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        
        coordinator = MultiAgentCoordinatorV3()
        
        if coordinator.backtest_validator is not None:
            print("✅ 协调器回测验证器初始化成功")
            print(f"✅ 验证器类型: {type(coordinator.backtest_validator)}")
            return True
        else:
            print("❌ 协调器回测验证器为None")
            return False
            
    except Exception as e:
        print(f"❌ 协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_signal_validation():
    """测试简单信号验证"""
    print("\n🔍 测试简单信号验证...")
    
    try:
        from analyzers.simple_backtest_validator import SimpleBacktestValidator
        
        validator = SimpleBacktestValidator()
        
        # 准备测试信号
        test_signal = {
            'fund_code': '518880',
            'decision': 'buy',
            'confidence': 0.7,
            'strength': 0.8,
            'signals': ['technical_buy', 'volume_surge']
        }
        
        print("📊 执行信号验证...")
        result = validator.validate_signal('518880', test_signal)
        
        print("✅ 信号验证成功")
        print(f"📊 验证结果:")
        print(f"   - 质量评分: {result.quality_score:.3f}")
        print(f"   - 建议: {result.recommendation}")
        print(f"   - 置信度调整: {result.confidence_adjustment:.3f}")
        print(f"   - 优化强度: {result.optimized_strength:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单信号验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 PyTorch版本回测验证器测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    tests = [
        ("PyTorch可用性测试", test_torch_availability),
        ("回测模块测试", test_backtest_modules),
        ("回测验证器测试", test_backtest_validator),
        ("协调器集成测试", test_coordinator_integration),
        ("简单信号验证测试", test_simple_signal_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 PyTorch回测验证器测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed >= total * 0.8:
        print("🎉 PyTorch版本回测验证器基本可用！")
        print("\n💡 总结:")
        print("   ✅ PyTorch替代TensorFlow成功")
        print("   ✅ 回测验证器可以正常工作")
        print("   ✅ 协调器集成正常")
    else:
        print("⚠️ 仍有问题需要解决")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
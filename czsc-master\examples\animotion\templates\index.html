<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
    <script src="https://cdn.bootcss.com/jquery/3.0.0/jquery.min.js"></script>
    <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
</head>
<body>
<div id="bar" style="width:1600px; height:800px;"></div>
<script>
    var chart = echarts.init(document.getElementById('bar'), 'white', {renderer: 'canvas'});

    $(
        function () {
            fetchData(chart);
            setInterval(fetchData, 2000);
        }
    );

    function fetchData() {
        $.ajax({
            type: "GET",
            url: "http://127.0.0.1:5000/barChart",
            dataType: 'json',
            success: function (result) {
                chart.setOption(result);
            }
        });
    }
</script>
</body>
</html>
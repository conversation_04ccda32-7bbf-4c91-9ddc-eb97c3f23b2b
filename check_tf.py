#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查TensorFlow兼容性
"""

import sys
import platform

def check_system_info():
    """检查系统信息"""
    print("🔍 系统环境检查")
    print("="*50)
    print(f"Python版本: {sys.version}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    print()

def check_existing_packages():
    """检查现有包"""
    print("📦 现有相关包检查")
    print("="*50)
    
    packages_to_check = [
        'numpy', 'pandas', 'scipy', 'matplotlib', 
        'tensorflow', 'keras', 'torch'
    ]
    
    for package in packages_to_check:
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
    print()

def check_tensorflow_compatibility():
    """检查TensorFlow兼容性"""
    print("🔍 TensorFlow兼容性分析")
    print("="*50)
    
    python_version = sys.version_info
    is_64bit = sys.maxsize > 2**32
    
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    print(f"64位系统: {is_64bit}")
    print(f"操作系统: {platform.system()}")
    
    # TensorFlow兼容性检查
    recommendations = []
    
    if python_version >= (3, 7) and python_version <= (3, 11):
        if is_64bit and platform.system() == 'Windows':
            if python_version >= (3, 9):
                recommendations.append("✅ 推荐: tensorflow>=2.6.0 (支持Python 3.9)")
                recommendations.append("✅ 推荐: tensorflow==2.10.0 (稳定版本)")
                recommendations.append("✅ 推荐: tensorflow==2.13.0 (较新稳定版)")
            else:
                recommendations.append("✅ 推荐: tensorflow>=2.4.0")
        else:
            recommendations.append("⚠️ 需要64位Windows系统")
    else:
        recommendations.append("❌ Python版本不兼容，需要Python 3.7-3.11")
    
    print("\n💡 TensorFlow版本推荐:")
    for rec in recommendations:
        print(f"   {rec}")
    
    print()

def check_numpy_compatibility():
    """检查NumPy兼容性"""
    print("🔍 NumPy兼容性检查")
    print("="*50)
    
    try:
        import numpy as np
        numpy_version = np.__version__
        print(f"当前NumPy版本: {numpy_version}")
        
        # 检查与TensorFlow的兼容性
        numpy_major, numpy_minor = map(int, numpy_version.split('.')[:2])
        
        if numpy_major == 1 and numpy_minor >= 19:
            print("✅ NumPy版本与TensorFlow 2.x兼容")
        else:
            print("⚠️ 可能需要升级NumPy到>=1.19.0")
            
    except ImportError:
        print("❌ NumPy未安装")
    
    print()

def generate_installation_commands():
    """生成安装命令（仅供参考）"""
    print("💻 安装命令参考（请勿直接执行）")
    print("="*50)
    
    commands = [
        "# CPU版本（推荐用于开发和测试）",
        "pip install tensorflow==2.10.0",
        "",
        "# 或者最新稳定版",
        "pip install tensorflow==2.13.0",
        "",
        "# 如果需要GPU支持（需要CUDA）",
        "pip install tensorflow-gpu==2.10.0",
        "",
        "# 轻量级版本（如果只需要推理）",
        "pip install tensorflow-cpu==2.10.0",
        "",
        "# 同时安装Keras（通常包含在TensorFlow中）",
        "pip install keras==2.10.0",
    ]
    
    for cmd in commands:
        print(f"   {cmd}")
    
    print()

def check_disk_space():
    """检查磁盘空间"""
    print("💾 磁盘空间检查")
    print("="*50)
    
    try:
        import shutil
        total, used, free = shutil.disk_usage("C:\\")
        
        print(f"C盘总空间: {total // (1024**3)} GB")
        print(f"已使用: {used // (1024**3)} GB")
        print(f"可用空间: {free // (1024**3)} GB")
        
        # TensorFlow大约需要500MB-1GB空间
        if free > 2 * (1024**3):  # 2GB
            print("✅ 磁盘空间充足")
        else:
            print("⚠️ 磁盘空间可能不足，建议清理后安装")
            
    except Exception as e:
        print(f"❌ 无法检查磁盘空间: {e}")
    
    print()

def main():
    """主函数"""
    print("🎯 TensorFlow兼容性检查报告")
    print("="*80)
    
    check_system_info()
    check_existing_packages()
    check_tensorflow_compatibility()
    check_numpy_compatibility()
    check_disk_space()
    generate_installation_commands()
    
    print("📋 总结建议:")
    print("="*50)
    print("1. ✅ 你的系统（Python 3.9.7, Windows 64位）完全支持TensorFlow")
    print("2. 💡 推荐安装 tensorflow==2.10.0 或 tensorflow==2.13.0")
    print("3. ⚠️ 当前NumPy版本(1.20.3)兼容，但可能需要升级到更新版本")
    print("4. 🚀 可以选择CPU版本进行开发和测试")
    print("5. 💾 确保有足够的磁盘空间（至少2GB）")
    print()
    print("⚠️ 注意：本脚本仅进行兼容性检查，未执行任何安装操作")

if __name__ == "__main__":
    main()
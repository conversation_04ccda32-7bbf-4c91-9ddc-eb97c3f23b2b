#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于PyTorch的网络架构模块
替代TensorFlow版本，提供相同的功能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from typing import Tuple, Dict, List, Optional, Union
import numpy as np
import logging


class LSTMModel(nn.Module):
    """LSTM模型"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], 
                 output_size: int = 1, dropout_rate: float = 0.2, 
                 bidirectional: bool = False):
        super(LSTMModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        self.bidirectional = bidirectional
        
        # LSTM层
        self.lstm_layers = nn.ModuleList()
        
        # 第一层LSTM
        self.lstm_layers.append(
            nn.LSTM(input_size, hidden_sizes[0], batch_first=True, 
                   bidirectional=bidirectional, dropout=dropout_rate if len(hidden_sizes) > 1 else 0)
        )
        
        # 后续LSTM层
        for i in range(1, len(hidden_sizes)):
            input_dim = hidden_sizes[i-1] * (2 if bidirectional else 1)
            self.lstm_layers.append(
                nn.LSTM(input_dim, hidden_sizes[i], batch_first=True,
                       bidirectional=bidirectional, 
                       dropout=dropout_rate if i < len(hidden_sizes) - 1 else 0)
            )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 输出层
        final_hidden_size = hidden_sizes[-1] * (2 if bidirectional else 1)
        self.fc = nn.Linear(final_hidden_size, output_size)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        
        for lstm in self.lstm_layers:
            x, _ = lstm(x)
            x = self.dropout(x)
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]  # (batch_size, hidden_size)
        
        # 输出层
        x = self.fc(x)
        
        return x


class GRUModel(nn.Module):
    """GRU模型"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], 
                 output_size: int = 1, dropout_rate: float = 0.2, 
                 bidirectional: bool = False):
        super(GRUModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        self.bidirectional = bidirectional
        
        # GRU层
        self.gru_layers = nn.ModuleList()
        
        # 第一层GRU
        self.gru_layers.append(
            nn.GRU(input_size, hidden_sizes[0], batch_first=True, 
                  bidirectional=bidirectional, dropout=dropout_rate if len(hidden_sizes) > 1 else 0)
        )
        
        # 后续GRU层
        for i in range(1, len(hidden_sizes)):
            input_dim = hidden_sizes[i-1] * (2 if bidirectional else 1)
            self.gru_layers.append(
                nn.GRU(input_dim, hidden_sizes[i], batch_first=True,
                       bidirectional=bidirectional, 
                       dropout=dropout_rate if i < len(hidden_sizes) - 1 else 0)
            )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 输出层
        final_hidden_size = hidden_sizes[-1] * (2 if bidirectional else 1)
        self.fc = nn.Linear(final_hidden_size, output_size)
        
    def forward(self, x):
        for gru in self.gru_layers:
            x, _ = gru(x)
            x = self.dropout(x)
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]
        
        # 输出层
        x = self.fc(x)
        
        return x


class TransformerModel(nn.Module):
    """Transformer模型"""
    
    def __init__(self, input_size: int, d_model: int = 64, nhead: int = 8, 
                 num_layers: int = 2, output_size: int = 1, dropout_rate: float = 0.1):
        super(TransformerModel, self).__init__()
        
        self.input_size = input_size
        self.d_model = d_model
        
        # 输入投影
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, dropout_rate)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=nhead, dropout=dropout_rate, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 输出层
        self.fc = nn.Linear(d_model, output_size)
        self.dropout = nn.Dropout(dropout_rate)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = self.pos_encoding(x)
        
        # Transformer编码
        x = self.transformer(x)  # (batch_size, seq_len, d_model)
        
        # 全局平均池化
        x = torch.mean(x, dim=1)  # (batch_size, d_model)
        
        # 输出层
        x = self.dropout(x)
        x = self.fc(x)
        
        return x


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        x = x + self.pe[:x.size(1), :].transpose(0, 1)
        return self.dropout(x)


class CNNLSTMModel(nn.Module):
    """CNN-LSTM混合模型"""
    
    def __init__(self, input_size: int, cnn_filters: List[int] = [32, 64], 
                 kernel_sizes: List[int] = [3, 3], lstm_hidden_size: int = 50,
                 output_size: int = 1, dropout_rate: float = 0.2):
        super(CNNLSTMModel, self).__init__()
        
        # CNN层
        self.conv_layers = nn.ModuleList()
        in_channels = 1  # 假设输入是单通道
        
        for i, (filters, kernel_size) in enumerate(zip(cnn_filters, kernel_sizes)):
            self.conv_layers.append(nn.Conv1d(in_channels, filters, kernel_size, padding=kernel_size//2))
            self.conv_layers.append(nn.ReLU())
            self.conv_layers.append(nn.MaxPool1d(2))
            self.conv_layers.append(nn.Dropout(dropout_rate))
            in_channels = filters
        
        # 计算CNN输出维度
        self.cnn_output_size = cnn_filters[-1]
        
        # LSTM层
        self.lstm = nn.LSTM(self.cnn_output_size, lstm_hidden_size, batch_first=True)
        
        # 输出层
        self.fc = nn.Linear(lstm_hidden_size, output_size)
        self.dropout = nn.Dropout(dropout_rate)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        batch_size, seq_len, input_size = x.shape
        
        # 重塑为CNN输入格式: (batch_size, channels, seq_len)
        x = x.transpose(1, 2)  # (batch_size, input_size, seq_len)
        
        # CNN层
        for layer in self.conv_layers:
            x = layer(x)
        
        # 重塑为LSTM输入格式: (batch_size, seq_len, features)
        x = x.transpose(1, 2)  # (batch_size, seq_len, features)
        
        # LSTM层
        x, _ = self.lstm(x)
        
        # 取最后一个时间步
        x = x[:, -1, :]
        
        # 输出层
        x = self.dropout(x)
        x = self.fc(x)
        
        return x


class NetworkArchitecture:
    """
    基于PyTorch的神经网络架构模块
    替代TensorFlow版本
    """
    
    def __init__(self, input_shape: Tuple[int, int], output_dim: int = 1):
        """
        初始化网络架构构建器
        
        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            output_dim: 输出维度
        """
        self.input_shape = input_shape
        self.seq_len, self.input_size = input_shape
        self.output_dim = output_dim
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.logger.info(f"PyTorch网络架构初始化 - 设备: {self.device}")
        
    def build_lstm_model(self, lstm_units: List[int] = [64, 32], 
                        dropout_rate: float = 0.2, 
                        bidirectional: bool = False) -> LSTMModel:
        """
        构建LSTM网络
        
        Args:
            lstm_units: LSTM层单元数列表
            dropout_rate: Dropout比率
            bidirectional: 是否使用双向LSTM
            
        Returns:
            LSTM模型
        """
        model = LSTMModel(
            input_size=self.input_size,
            hidden_sizes=lstm_units,
            output_size=self.output_dim,
            dropout_rate=dropout_rate,
            bidirectional=bidirectional
        )
        
        model = model.to(self.device)
        self.logger.info(f"LSTM模型构建完成 - 参数数量: {sum(p.numel() for p in model.parameters())}")
        
        return model
    
    def build_gru_model(self, gru_units: List[int] = [64, 32], 
                       dropout_rate: float = 0.2, 
                       bidirectional: bool = False) -> GRUModel:
        """
        构建GRU网络
        
        Args:
            gru_units: GRU层单元数列表
            dropout_rate: Dropout比率
            bidirectional: 是否使用双向GRU
            
        Returns:
            GRU模型
        """
        model = GRUModel(
            input_size=self.input_size,
            hidden_sizes=gru_units,
            output_size=self.output_dim,
            dropout_rate=dropout_rate,
            bidirectional=bidirectional
        )
        
        model = model.to(self.device)
        self.logger.info(f"GRU模型构建完成 - 参数数量: {sum(p.numel() for p in model.parameters())}")
        
        return model
    
    def build_transformer_model(self, d_model: int = 64, nhead: int = 8, 
                               num_layers: int = 2, dropout_rate: float = 0.1) -> TransformerModel:
        """
        构建Transformer网络
        
        Args:
            d_model: 模型维度
            nhead: 注意力头数
            num_layers: 编码器层数
            dropout_rate: Dropout比率
            
        Returns:
            Transformer模型
        """
        model = TransformerModel(
            input_size=self.input_size,
            d_model=d_model,
            nhead=nhead,
            num_layers=num_layers,
            output_size=self.output_dim,
            dropout_rate=dropout_rate
        )
        
        model = model.to(self.device)
        self.logger.info(f"Transformer模型构建完成 - 参数数量: {sum(p.numel() for p in model.parameters())}")
        
        return model
    
    def build_cnn_lstm_model(self, cnn_filters: List[int] = [32, 64], 
                            kernel_sizes: List[int] = [3, 3],
                            lstm_hidden_size: int = 50,
                            dropout_rate: float = 0.2) -> CNNLSTMModel:
        """
        构建CNN-LSTM混合网络
        
        Args:
            cnn_filters: CNN滤波器数量列表
            kernel_sizes: 卷积核大小列表
            lstm_hidden_size: LSTM隐藏层大小
            dropout_rate: Dropout比率
            
        Returns:
            CNN-LSTM模型
        """
        model = CNNLSTMModel(
            input_size=self.input_size,
            cnn_filters=cnn_filters,
            kernel_sizes=kernel_sizes,
            lstm_hidden_size=lstm_hidden_size,
            output_size=self.output_dim,
            dropout_rate=dropout_rate
        )
        
        model = model.to(self.device)
        self.logger.info(f"CNN-LSTM模型构建完成 - 参数数量: {sum(p.numel() for p in model.parameters())}")
        
        return model
    
    def get_optimizer(self, model: nn.Module, optimizer_type: str = 'adam', 
                     learning_rate: float = 0.001, **kwargs) -> optim.Optimizer:
        """
        获取优化器
        
        Args:
            model: 模型
            optimizer_type: 优化器类型 ('adam', 'rmsprop', 'sgd')
            learning_rate: 学习率
            **kwargs: 其他优化器参数
            
        Returns:
            优化器
        """
        if optimizer_type.lower() == 'adam':
            return optim.Adam(model.parameters(), lr=learning_rate, **kwargs)
        elif optimizer_type.lower() == 'rmsprop':
            return optim.RMSprop(model.parameters(), lr=learning_rate, **kwargs)
        elif optimizer_type.lower() == 'sgd':
            return optim.SGD(model.parameters(), lr=learning_rate, **kwargs)
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    def get_loss_function(self, loss_type: str = 'mse') -> nn.Module:
        """
        获取损失函数
        
        Args:
            loss_type: 损失函数类型 ('mse', 'mae', 'huber', 'crossentropy')
            
        Returns:
            损失函数
        """
        if loss_type.lower() == 'mse':
            return nn.MSELoss()
        elif loss_type.lower() == 'mae':
            return nn.L1Loss()
        elif loss_type.lower() == 'huber':
            return nn.HuberLoss()
        elif loss_type.lower() == 'crossentropy':
            return nn.CrossEntropyLoss()
        else:
            raise ValueError(f"不支持的损失函数类型: {loss_type}")
    
    def create_data_loader(self, X: np.ndarray, y: np.ndarray, 
                          batch_size: int = 32, shuffle: bool = True) -> DataLoader:
        """
        创建数据加载器
        
        Args:
            X: 输入数据
            y: 目标数据
            batch_size: 批次大小
            shuffle: 是否打乱数据
            
        Returns:
            数据加载器
        """
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y)
        
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)
        
        return dataloader
    
    def train_model(self, model: nn.Module, train_loader: DataLoader, 
                   val_loader: Optional[DataLoader] = None,
                   optimizer: Optional[optim.Optimizer] = None,
                   criterion: Optional[nn.Module] = None,
                   epochs: int = 100, patience: int = 10) -> Dict[str, List[float]]:
        """
        训练模型
        
        Args:
            model: 模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            optimizer: 优化器
            criterion: 损失函数
            epochs: 训练轮数
            patience: 早停耐心值
            
        Returns:
            训练历史
        """
        if optimizer is None:
            optimizer = self.get_optimizer(model)
        if criterion is None:
            criterion = self.get_loss_function()
        
        history = {'train_loss': [], 'val_loss': []}
        best_val_loss = float('inf')
        patience_counter = 0
        
        model.train()
        
        for epoch in range(epochs):
            # 训练阶段
            train_loss = 0.0
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            train_loss /= len(train_loader)
            history['train_loss'].append(train_loss)
            
            # 验证阶段
            if val_loader is not None:
                model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                        outputs = model(batch_X)
                        loss = criterion(outputs, batch_y)
                        val_loss += loss.item()
                
                val_loss /= len(val_loader)
                history['val_loss'].append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        self.logger.info(f"早停触发，在第{epoch+1}轮停止训练")
                        break
                
                model.train()
            
            if (epoch + 1) % 10 == 0:
                self.logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}" + 
                               (f", Val Loss: {val_loss:.6f}" if val_loader else ""))
        
        return history


# NetworkArchitecture类已经定义在上面
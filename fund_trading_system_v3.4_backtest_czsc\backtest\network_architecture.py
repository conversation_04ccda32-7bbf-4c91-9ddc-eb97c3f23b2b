"""
PyTorch神经网络架构模块
构建适合时间序列的神经网络结构
"""

from typing import Tuple, Dict, List, Optional
import numpy as np

# 尝试导入torch，如果不可用则提供备用方案
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    # 创建占位符类以避免导入错误
    class torch:
        class nn:
            class Module:
                def __init__(self):
                    pass
            class LSTM:
                pass
            class Linear:
                pass
            class Dropout:
                pass


class LSTMModel(nn.Module if TORCH_AVAILABLE else object):
    """PyTorch LSTM模型"""
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 2, 
                 output_size: int = 1, dropout: float = 0.2, bidirectional: bool = False):
        if TORCH_AVAILABLE:
            super(LSTMModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.output_size = output_size
        
        if TORCH_AVAILABLE:
            self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                               batch_first=True, dropout=dropout, bidirectional=bidirectional)
            
            lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
            self.fc = nn.Linear(lstm_output_size, output_size)
            self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch is not available")
            
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]
        output = self.dropout(last_output)
        output = self.fc(output)
        return output


class NetworkArchitecture:
    """
    神经网络架构模块
    构建适合时间序列的PyTorch神经网络结构
    """
    
    def __init__(self, input_shape: Tuple[int, int], output_dim: int = 1):
        """
        初始化网络架构构建器
        
        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            output_dim: 输出维度
        """
        self.input_shape = input_shape
        self.sequence_length, self.n_features = input_shape
        self.output_dim = output_dim
        self.model_configs = {}
        self.architecture_log = []
        
    def build_lstm_model(self, lstm_units: List[int] = [64, 32], 
                        dropout_rate: float = 0.2, 
                        bidirectional: bool = False,
                        activation: str = 'tanh'):
        """
        构建LSTM网络
        
        Args:
            lstm_units: LSTM层单元数列表
            dropout_rate: Dropout比率
            bidirectional: 是否使用双向LSTM
            activation: 激活函数
            
        Returns:
            LSTM模型
        """
        if not TORCH_AVAILABLE:
            # 如果torch不可用，返回一个简单的占位符
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.architecture_log.append(f"Built RandomForest model (PyTorch not available)")
            return model
        
        # 使用第一个单元数作为隐藏层大小，层数为列表长度
        hidden_size = lstm_units[0] if lstm_units else 64
        num_layers = len(lstm_units) if len(lstm_units) > 1 else 2
        
        model = LSTMModel(
            input_size=self.n_features,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=self.output_dim,
            dropout=dropout_rate,
            bidirectional=bidirectional
        )
        
        self.architecture_log.append(f"Built LSTM model with units: {lstm_units}, bidirectional: {bidirectional}")
        
        return model
    
    def build_gru_model(self, gru_units: List[int] = [64, 32],
                       dropout_rate: float = 0.2,
                       bidirectional: bool = False):
        """
        构建GRU网络 - 如果torch不可用则返回RandomForest
        """
        if not TORCH_AVAILABLE:
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.architecture_log.append(f"Built RandomForest model (PyTorch not available)")
            return model
        
        # 这里可以添加GRU实现，现在先返回LSTM
        return self.build_lstm_model(gru_units, dropout_rate, bidirectional)
    
    def build_transformer_model(self, d_model: int = 64, nhead: int = 8, 
                               num_layers: int = 2, dropout_rate: float = 0.2):
        """
        构建Transformer网络 - 如果torch不可用则返回RandomForest
        """
        if not TORCH_AVAILABLE:
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.architecture_log.append(f"Built RandomForest model (PyTorch not available)")
            return model
        
        # 简化实现，返回LSTM
        return self.build_lstm_model([d_model], dropout_rate)
    
    def build_ensemble_model(self, model_types: List[str] = ['lstm', 'gru'], 
                           voting_method: str = 'average'):
        """
        构建集成模型 - 如果torch不可用则返回RandomForest
        """
        if not TORCH_AVAILABLE:
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=200, random_state=42)
            self.architecture_log.append(f"Built RandomForest ensemble model (PyTorch not available)")
            return model
        
        # 简化实现，返回LSTM
        return self.build_lstm_model([64, 32])
    
    def compile_model(self, model, optimizer: str = 'adam', 
                     learning_rate: float = 0.001, loss: str = 'mse'):
        """
        编译模型 - 兼容sklearn和torch模型
        """
        if TORCH_AVAILABLE and hasattr(model, 'parameters'):
            # PyTorch模型
            if optimizer == 'adam':
                optimizer_obj = torch.optim.Adam(model.parameters(), lr=learning_rate)
            else:
                optimizer_obj = torch.optim.SGD(model.parameters(), lr=learning_rate)
            
            if loss == 'mse':
                loss_fn = nn.MSELoss()
            else:
                loss_fn = nn.L1Loss()
            
            # 将优化器和损失函数附加到模型
            model.optimizer = optimizer_obj
            model.loss_fn = loss_fn
            
            self.architecture_log.append(f"Compiled PyTorch model with {optimizer} optimizer")
        else:
            # sklearn模型不需要编译
            self.architecture_log.append(f"Sklearn model does not require compilation")
        
        return model
    
    def get_model_summary(self, model) -> Dict:
        """
        获取模型摘要
        """
        summary = {
            'model_type': type(model).__name__,
            'torch_available': TORCH_AVAILABLE,
            'architecture_log': self.architecture_log.copy()
        }
        
        if TORCH_AVAILABLE and hasattr(model, 'parameters'):
            # PyTorch模型
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            summary.update({
                'total_parameters': total_params,
                'trainable_parameters': trainable_params,
                'input_size': getattr(model, 'input_size', 'Unknown'),
                'output_size': getattr(model, 'output_size', 'Unknown')
            })
        else:
            # sklearn模型
            summary.update({
                'sklearn_model': True,
                'model_params': getattr(model, 'get_params', lambda: {})()
            })
        
        return summary

"""
回测模块集成测试
验证backtest模块是否成功集成到fund_trading_system_v3中
"""

import sys
import os
import traceback
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_backtest_imports():
    """测试回测模块导入"""
    print("🔍 测试回测模块导入...")
    
    try:
        # 测试核心模块导入
        from fund_trading_system_v3.backtest import (
            BacktestEngine,
            RiskManager
        )
        print("   ✅ 核心回测模块导入成功")
        
        # 测试可选模块导入
        from fund_trading_system_v3.backtest import (
            DATA_PROCESSING_AVAILABLE,
            ML_AVAILABLE,
            ADVANCED_AVAILABLE
        )
        
        optional_modules = []
        
        if DATA_PROCESSING_AVAILABLE:
            from fund_trading_system_v3.backtest import DataPreprocessor, FeatureEngineer, TimeSeriesProcessor
            optional_modules.append("数据处理模块")
        
        if ML_AVAILABLE:
            from fund_trading_system_v3.backtest import NetworkArchitecture, ModelTrainer
            optional_modules.append("机器学习模块")
            
        if ADVANCED_AVAILABLE:
            from fund_trading_system_v3.backtest import AdvancedBacktestSystem
            optional_modules.append("高级回测模块")
        
        if optional_modules:
            print(f"   ✅ 可选模块导入成功: {', '.join(optional_modules)}")
        else:
            print("   ⚠️ 可选模块不可用（缺少依赖），但核心功能正常")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_backtest_functionality():
    """测试回测功能"""
    print("🧪 测试回测功能...")
    
    try:
        from fund_trading_system_v3.backtest.backtest_engine import BacktestEngine
        
        # 创建回测引擎实例
        engine = BacktestEngine(initial_capital=100000)
        print("   ✅ BacktestEngine 创建成功")
        
        # 测试基本属性
        assert engine.initial_capital == 100000
        assert hasattr(engine, 'transaction_cost')
        assert hasattr(engine, 'backtest_results')
        print("   ✅ BacktestEngine 属性验证通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_main_system_integration():
    """测试主系统集成"""
    print("🔗 测试主系统集成...")
    
    try:
        # 测试从主包导入核心模块
        from fund_trading_system_v3 import (
            BacktestEngine,
            RiskManager
        )
        print("   ✅ 从主包导入核心回测模块成功")
        
        # 检查可选模块可用性
        from fund_trading_system_v3 import (
            DATA_PROCESSING_AVAILABLE,
            ML_AVAILABLE,
            ADVANCED_AVAILABLE
        )
        print(f"   📊 数据处理模块: {'✅ 可用' if DATA_PROCESSING_AVAILABLE else '⚠️ 不可用'}")
        print(f"   🤖 机器学习模块: {'✅ 可用' if ML_AVAILABLE else '⚠️ 不可用'}")
        print(f"   🚀 高级回测模块: {'✅ 可用' if ADVANCED_AVAILABLE else '⚠️ 不可用'}")
        
        # 测试主系统
        from fund_trading_system_v3 import EnhancedFundTradingSystemV3
        print("   ✅ 主系统导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 主系统集成测试失败: {e}")
        return False

def test_enhanced_signal_system():
    """测试增强信号系统"""
    print("📊 测试增强信号系统...")
    
    try:
        # 测试增强信号系统是否能正常导入新的回测模块
        import enhanced_signal_system
        print("   ✅ enhanced_signal_system 导入成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 增强信号系统测试失败: {e}")
        return False

def run_integration_tests():
    """运行所有集成测试"""
    print("🚀 开始回测模块集成测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_backtest_imports),
        ("功能测试", test_backtest_functionality),
        ("主系统集成测试", test_main_system_integration),
        ("增强信号系统测试", test_enhanced_signal_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！回测模块集成成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查集成配置")
        return False

if __name__ == "__main__":
    success = run_integration_tests()
    
    if success:
        print(f"\n✅ 集成验证完成")
        print("🚀 可以安全删除原始的 backtest_modules 目录")
    else:
        print(f"\n❌ 集成验证失败")
        print("🔧 请检查配置后重新测试")
# -*- coding: utf-8 -*-
"""
TDX数据获取模块

基于pytdx的数据接口，提供通达信行情数据的获取功能
可在Windows/Linux/Mac上使用

参考：
- pytdx: https://github.com/rainx/pytdx
- QUANTAXIS.QAFetch.QATdx
"""

import datetime
import json
import os
import time
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Pool, cpu_count

import pandas as pd
from pytdx.exhq import TdxExHq_API
from pytdx.hq import TdxHq_API
from retrying import retry

# 单例模式装饰器
def singleton(cls):
    instances = {}
    def _wrapper(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    return _wrapper

# 简单的缓存系统
@singleton
class TDXCache:
    '''简单的缓存系统
    把变量保存在内存里, 同时给它一个过期时间, 过期则失效.
    '''
    def __init__(self):
        '''初始化'''
        self.mem = {}
        self.time = {}

    def set(self, key, data, age=-1):
        '''保存键为key的值，存活时间为age秒'''
        self.mem[key] = data
        if age == -1:
            self.time[key] = -1
        else:
            self.time[key] = time.time() + age
        return True

    def get(self,key):
        '''获取键key对应的值'''
        if key in self.mem.keys():
            if self.time[key] == -1 or self.time[key] > time.time():
                return self.mem[key]
            else:
                self.delete(key)
                return None
        else:
            return None

    def delete(self,key):
        '''删除键为key的条目'''
        del self.mem[key]
        del self.time[key]
        return True

    def clear(self):
        '''清空所有缓存'''
        self.mem.clear()
        self.time.clear()

# 多进程并行类
class Parallelism:
    """ 多进程map类
        pl = Parallelism()
        pl.run(yourFunc, yourIter)
        data = pl.get_results()
    """

    def __init__(self, processes=cpu_count()):
        self.total_processes = 0
        self.completed_processes = 0
        self.results = []
        self.data = []
        self.cores = processes  # cpu核心数量
        self.pool = Pool(processes=processes)

    def get_results(self):
        return self.results

    def complete(self, result):
        self.results.extend(result)
        self.completed_processes += 1
        print('Progress: {:.2f}%'.format(
            (self.completed_processes / self.total_processes) * 100))
            
    def exception(self, e):
        print(f"Exception in worker process: {e}")

    def run(self, func, iter_data):
        if isinstance(iter_data, list) and self.cores > 1 and len(
                iter_data) > self.cores:
            j = self.cores
            for i in range(j):
                pLen = int(len(iter_data) / j) + 1
                self.data.append(
                    self.pool.starmap_async(func,
                                            iter_data[i * pLen:(i + 1) * pLen],
                                            callback=self.complete,
                                            error_callback=self.exception))
                self.total_processes += 1
        else:
            self.data.append(
                self.pool.starmap_async(func=func,
                                        iterable=iter_data,
                                        callback=self.complete,
                                        error_callback=self.exception)
            )
            self.total_processes += 1
        for i in range(self.total_processes):
            try:
                while not self.data[i].ready():
                    time.sleep(0.5)
                self.data[i].get()
            except Exception as e:
                print(e)

# 默认的IP列表
default_stock_ip_list = [
    {"ip": "**************", "port": 7709, "name": "上海双线主站14"},
    {"ip": "*************", "port": 7709, "name": "武汉电信主站1"},
    {"ip": "*************", "port": 7709, "name": "深圳双线主站7"},
    {"ip": "*************", "port": 7709, "name": "北京双线主站4"},
    {"ip": "************", "port": 7709, "name": "广州双线主站4"},
    {"ip": "************", "port": 7709, "name": "上海双线主站15"},
    {"ip": "*************", "port": 7719, "name": "深圳双线主站8"},
    {"ip": "**************", "port": 7709, "name": "北京双线主站5"},
    {"ip": "*************", "port": 7709, "name": "北京双线主站6"},
    {"ip": "*************", "port": 7709, "name": "北京双线主站7"},
    {"ip": "***************", "port": 7709, "name": "广州双线主站5"},
    {"ip": "***************", "port": 7709, "name": "广州双线主站6"},
    {"ip": "***************", "port": 7709, "name": "广州双线主站7"},
]

default_future_ip_list = [
    {"ip": "*************", "port": 7727, "name": "扩展市场上海双线"},
    {"ip": "*************", "port": 7727, "name": "扩展市场深圳双线1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场深圳主站"},
    {"ip": "************", "port": 7727, "name": "扩展市场武汉主站1"},
    {"ip": "***********", "port": 7727, "name": "扩展市场深圳双线2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场深圳双线3"},
    {"ip": "*************", "port": 7727, "name": "扩展市场北京主站"},
    {"ip": "**************", "port": 7727, "name": "扩展市场上海主站1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场上海主站2"},
]

# 创建配置目录
def create_config_dir():
    """创建配置目录"""
    home_path = os.path.expanduser('~')
    tdx_path = os.path.join(home_path, '.tdx_fetcher')
    if not os.path.exists(tdx_path):
        os.makedirs(tdx_path)
    return tdx_path

# 获取配置文件路径
def get_config_path(filename):
    """获取配置文件路径"""
    config_dir = create_config_dir()
    return os.path.join(config_dir, filename)

# 加载IP列表
def load_ip_list(ip_type='stock'):
    """加载IP列表
    
    Args:
        ip_type: 'stock' 或 'future'
        
    Returns:
        list: IP列表
    """
    if ip_type == 'stock':
        filename = 'stock_ip.json'
        default_list = default_stock_ip_list
    else:
        filename = 'future_ip.json'
        default_list = default_future_ip_list
        
    file_path = get_config_path(filename)
    
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            return json.load(f)
    else:
        # 如果文件不存在，保存默认列表并返回
        with open(file_path, 'w') as f:
            json.dump(default_list, f)
        return default_list

# 保存IP列表
def save_ip_list(ip_list, ip_type='stock'):
    """保存IP列表
    
    Args:
        ip_list: IP列表
        ip_type: 'stock' 或 'future'
    """
    if ip_type == 'stock':
        filename = 'stock_ip.json'
    else:
        filename = 'future_ip.json'
        
    file_path = get_config_path(filename)
    
    with open(file_path, 'w') as f:
        json.dump(ip_list, f)

# 从IP列表中排除指定IP
def exclude_from_ip_list(exclude_ip_list):
    """从IP列表中排除指定IP
    
    Args:
        exclude_ip_list: 要排除的IP列表
    """
    stock_ip_list = load_ip_list('stock')
    future_ip_list = load_ip_list('future')
    
    for exc in exclude_ip_list:
        if exc in stock_ip_list:
            stock_ip_list.remove(exc)
    
    for exc in exclude_ip_list:
        if exc in future_ip_list:
            future_ip_list.remove(exc)
    
    save_ip_list(stock_ip_list, 'stock')
    save_ip_list(future_ip_list, 'future')

# 测试IP连接速度
def ping(ip, port=7709, type_='stock'):
    """测试IP连接速度
    
    Args:
        ip: IP地址
        port: 端口号
        type_: 'stock' 或 'future'
        
    Returns:
        datetime.timedelta: 连接耗时
    """
    api = TdxHq_API()
    apix = TdxExHq_API(raise_exception=True)
    __time1 = datetime.datetime.now()
    try:
        if type_ in ['stock']:
            with api.connect(ip, port, time_out=0.7):
                res = api.get_security_list(0, 1)

                if res is not None:
                    if len(api.get_security_list(0, 1)) > 800:
                        return datetime.datetime.now() - __time1
                    else:
                        print('BAD RESPONSE {}'.format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:
                    print('BAD RESPONSE {}'.format(ip))
                    return datetime.timedelta(9, 9, 0)
        elif type_ in ['future']:
            with apix.connect(ip, port, time_out=0.7):
                res = apix.get_instrument_count()
                if res is not None:
                    if res > 20000:
                        return datetime.datetime.now() - __time1
                    else:
                        print('️Bad FUTUREIP REPSONSE {}'.format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:
                    print('️Bad FUTUREIP REPSONSE {}'.format(ip))
                    return datetime.timedelta(9, 9, 0)
    except Exception as e:
        if isinstance(e, TypeError):
            print(e)
            print('Tushare内置的pytdx版本和使用的pytdx版本不同, 请重新安装pytdx以解决此问题')
            print('pip uninstall pytdx')
            print('pip install pytdx')
        else:
            print('BAD RESPONSE {}'.format(ip))
        return datetime.timedelta(9, 9, 0)

# 获取最优IP
def get_ip_list_by_ping(ip_list=[], _type='stock'):
    """根据ping排序返回可用的ip列表中的最优IP
    
    Args:
        ip_list: IP列表
        _type: 'stock' 或 'future'
        
    Returns:
        dict: 最优IP信息
    """
    best_ip = get_ip_list_by_multi_process_ping(ip_list, 1, _type)
    return best_ip[0]

# 多进程测试IP
def get_ip_list_by_multi_process_ping(ip_list=[], n=0, _type='stock',
                                      cache_age=86400):
    """根据ping排序返回可用的ip列表
    
    Args:
        ip_list: IP列表
        n: 最多返回的ip数量， 当可用ip数量小于n，返回所有可用的ip；n=0时，返回所有可用ip
        _type: 'stock' 或 'future'
        cache_age: ip类型缓存时间（秒），默认为一天（86400秒）
        
    Returns:
        list: 可以ping通的ip列表
    """
    cache = TDXCache()
    results = cache.get(_type)
    if results:
        # read the data from cache
        print('loading ip list from {} cache.'.format(_type))
    else:
        ips = [(x['ip'], x['port'], _type) for x in ip_list]
        ps = Parallelism()
        ps.run(ping, ips)
        data = list(ps.get_results())
        results = []
        for i in range(len(data)):
            # 删除ping不通的数据
            if data[i] < datetime.timedelta(0, 9, 0):
                results.append((data[i], ip_list[i]))
        # 按照ping值从小大大排序
        results = [x[1] for x in sorted(results, key=lambda x: x[0])]
        if _type:
            # store the data as binary data stream
            cache.set(_type, results, age=cache_age)
            print('saving ip list to {} cache {}'.format(_type, len(results)))
    if len(results) > 0:
        if n == 0 and len(results) > 0:
            return results
        else:
            return results[:n]
    else:
        print('ALL IP PING TIMEOUT!')
        return [{'ip': None, 'port': None}]

# 全局变量，存储最优IP
best_ip = {
    'stock': {
        'ip': None, 'port': None
    },
    'future': {
        'ip': None, 'port': None
    }
}

# 选择最优IP
def select_best_ip():
    """选择最优IP
    
    Returns:
        dict: 包含股票和期货最优IP的字典
    """
    print('Selecting the Best Server IP of TDX')

    stock_ip_list = load_ip_list('stock')
    future_ip_list = load_ip_list('future')

    best_stock_ip = get_ip_list_by_ping(stock_ip_list)
    best_future_ip = get_ip_list_by_ping(future_ip_list, _type='future')

    ipbest = {'stock': best_stock_ip, 'future': best_future_ip}

    print('=== The BEST SERVER ===\n stock_ip {} future_ip {}'.format(
        best_stock_ip['ip'], best_future_ip['ip']))
    return ipbest

# 获取主市场IP
def get_mainmarket_ip(ip, port):
    """获取主市场IP
    
    Args:
        ip: IP地址
        port: 端口号
        
    Returns:
        tuple: (ip, port)
    """
    global best_ip
    if ip is None and port is None and best_ip['stock']['ip'] is None and \
            best_ip['stock']['port'] is None:
        best_ip = select_best_ip()
        ip = best_ip['stock']['ip']
        port = best_ip['stock']['port']
    elif ip is None and port is None and \
            best_ip['stock']['ip'] is not None and \
            best_ip['stock']['port'] is not None:
        ip = best_ip['stock']['ip']
        port = best_ip['stock']['port']
    else:
        pass
    return ip, port

# 获取扩展市场IP
def get_extensionmarket_ip(ip, port):
    """获取扩展市场IP
    
    Args:
        ip: IP地址
        port: 端口号
        
    Returns:
        tuple: (ip, port)
    """
    global best_ip
    if ip is None and port is None and best_ip['future']['ip'] is None and \
            best_ip['future']['port'] is None:
        best_ip = select_best_ip()
        ip = best_ip['future']['ip']
        port = best_ip['future']['port']
    elif ip is None and port is None and \
            best_ip['future']['ip'] is not None and \
            best_ip['future']['port'] is not None:
        ip = best_ip['future']['ip']
        port = best_ip['future']['port']
    else:
        pass
    return ip, port

# 市场代码选择函数
def select_market_code(code):
    """市场代码选择函数
    
    Args:
        code: 证券代码
        
    Returns:
        int: 市场代码 (0: 深圳, 1: 上海)
    """
    code = str(code)
    if code[0] in ['5', '6', '9'] or code[:3] in ['009', '126', '110', '201', '202', '203', '204']:
        return 1
    return 0

# 指数代码选择函数
def select_index_code(code):
    """指数代码选择函数
    
    Args:
        code: 指数代码
        
    Returns:
        int: 市场代码 (0: 深圳, 1: 上海)
    """
    code = str(code)
    if code[0] in ['0']:
        return 0
    return 1

# 债券市场代码选择函数
def select_bond_market_code(code):
    """债券市场代码选择函数
    
    Args:
        code: 债券代码
        
    Returns:
        int: 市场代码 (0: 深圳, 1: 上海)
    """
    code = str(code)
    if code[0] in ['5', '6', '9']:
        return 1
    return 0

# 周期类型选择函数
def select_type(frequence):
    """周期类型选择函数
    
    Args:
        frequence: 周期字符串
        
    Returns:
        int: 周期类型代码
    """
    if frequence in ['day', 'd', 'D', 'DAY', 'Day']:
        frequence = 9
    elif frequence in ['w', 'W', 'Week', 'week']:
        frequence = 5
    elif frequence in ['month', 'M', 'm', 'Month']:
        frequence = 6
    elif frequence in ['quarter', 'Q', 'Quarter', 'q']:
        frequence = 10
    elif frequence in ['y', 'Y', 'year', 'Year']:
        frequence = 11
    elif frequence in ['5', '5m', '5min', 'five']:
        frequence = 0
    elif frequence in ['1', '1m', '1min', 'one']:
        frequence = 8
    elif frequence in ['15', '15m', '15min', 'fifteen']:
        frequence = 1
    elif frequence in ['30', '30m', '30min', 'half']:
        frequence = 2
    elif frequence in ['60', '60m', '60min', '1h']:
        frequence = 3
    return frequence

# 日期转换函数
def util_date_stamp(date):
    """日期转换为时间戳
    
    Args:
        date: 日期字符串，格式为 'YYYY-MM-DD' 或 'YYYYMMDD HH:MM:SS' 或 datetime对象
        
    Returns:
        int: 日期戳 (YYYYMMDD 格式的整数)
    """
    if isinstance(date, str):
        # 处理带时间的字符串，如 '20230726 09:35:00'
        if ' ' in date:
            date = date.split(' ')[0]  # 取日期部分
        # 统一格式为 YYYYMMDD
        date = date.replace('-', '').replace('/', '').replace(' ', '')
        # 确保是有效的日期格式
        if len(date) >= 8:
            return int(date[:8])
        return int(date)
    elif hasattr(date, 'strftime'):  # 处理 datetime 对象
        return int(date.strftime('%Y%m%d'))
    return date

# 时间戳转换函数
def util_time_stamp(time_):
    """时间转换为时间戳
    
    Args:
        time_: 时间字符串，格式为 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD HH:MM' 或 datetime对象
        
    Returns:
        float: 时间戳
    """
    if isinstance(time_, str):
        # 尝试带秒的格式
        try:
            return time.mktime(time.strptime(time_, '%Y-%m-%d %H:%M:%S'))
        except ValueError:
            # 如果不带秒，添加秒部分
            try:
                return time.mktime(time.strptime(time_ + ':00', '%Y-%m-%d %H:%M:%S'))
            except Exception as e:
                # 如果其他格式错误，返回当前时间戳
                print(f"Warning: Could not parse time string '{time_}': {str(e)}")
                return time.time()
    elif hasattr(time_, 'timestamp'):  # 处理 datetime 对象
        return time_.timestamp()
    return time_

# 通达信时间戳转换函数
def util_tdxtimestamp(value):
    """通达信时间戳转换
    
    Args:
        value: 通达信时间戳
        
    Returns:
        datetime: 转换后的时间对象
    """
    try:
        # 检查值是否在合理范围内
        if not isinstance(value, (int, float)) or value <= 0 or value > 999999:
            raise ValueError(f"Invalid TDX timestamp value: {value}")
            
        # 计算年月日
        year = value // 2048 + 2004
        month = (value % 2048) // 100
        day = (value % 2048) % 100
        
        # 验证日期范围
        if not (2004 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31):
            raise ValueError(f"Invalid date from TDX timestamp: {year}-{month}-{day}")
            
        return datetime.datetime(year=year, month=month, day=day, hour=9, minute=30)
        
    except Exception as e:
        # 如果转换失败，记录警告并返回当前时间
        print(f"Warning: Failed to convert TDX timestamp {value}: {str(e)}")
        return datetime.datetime.now()

# 计算交易日间隔
def util_get_trade_gap(start_date, end_date):
    """计算交易日间隔
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        int: 交易日间隔
    """
    start_date = str(start_date)[0:10]
    end_date = str(end_date)[0:10]
    if len(start_date) == 10:
        start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
    if len(end_date) == 10:
        end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
    return (end_date - start_date).days

# 获取证券行情数据
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_security_bars(code, _type, lens, ip=None, port=None):
    """获取证券行情数据
    
    Args:
        code: 证券代码
        _type: 周期类型
        lens: 获取数据长度
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 行情数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    with api.connect(ip, port):
        data = pd.concat([api.to_df(
            api.get_security_bars(select_type(_type), select_market_code(
                code), code, (i - 1) * 800, 800)) for i in
            range(1, int(lens / 800) + 2)], axis=0, sort=False)
        data = data \
            .drop(['year', 'month', 'day', 'hour', 'minute'], axis=1,
                  inplace=False) \
            .assign(datetime=pd.to_datetime(data['datetime'], utc=False),
                    date=data['datetime'].apply(lambda x: str(x)[0:10]),
                    date_stamp=data['datetime'].apply(
                        lambda x: util_date_stamp(x)),
                    time_stamp=data['datetime'].apply(
                        lambda x: util_time_stamp(x)),
                    type=_type, code=str(code)) \
            .set_index('datetime', drop=False, inplace=False).tail(lens)
        if data is not None:
            return data
        else:
            return None

# 获取股票日线数据
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_day(code, start_date, end_date, if_fq='00',
                       frequence='day', ip=None, port=None):
    """获取股票日线数据
    
    Args:
        code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        if_fq: 复权类型，'00'或'bfq'表示不复权
        frequence: 周期类型
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 日线数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    try:
        with api.connect(ip, port, time_out=0.7):

            if frequence in ['day', 'd', 'D', 'DAY', 'Day']:
                frequence = 9
            elif frequence in ['w', 'W', 'Week', 'week']:
                frequence = 5
            elif frequence in ['month', 'M', 'm', 'Month']:
                frequence = 6
            elif frequence in ['quarter', 'Q', 'Quarter', 'q']:
                frequence = 10
            elif frequence in ['y', 'Y', 'year', 'Year']:
                frequence = 11
            start_date = str(start_date)[0:10]
            today_ = datetime.date.today()
            lens = util_get_trade_gap(start_date, today_)

            data = pd.concat([api.to_df(
                api.get_security_bars(frequence, select_market_code(
                    code), code, (int(lens / 800) - i) * 800, 800)) for i in
                range(int(lens / 800) + 1)], axis=0, sort=False)

            # 这里的问题是: 如果只取了一天的股票,而当天停牌, 那么就直接返回None了
            if len(data) < 1:
                return None
            data = data[data['open'] != 0]

            data = data.assign(
                date=data['datetime'].apply(lambda x: str(x[0:10])),
                code=str(code),
                date_stamp=data['datetime'].apply(
                    lambda x: util_date_stamp(str(x)[0:10]))) \
                .set_index('date', drop=False, inplace=False)

            end_date = str(end_date)[0:10]
            data = data.drop(
                ['year', 'month', 'day', 'hour', 'minute', 'datetime'],
                axis=1)[
                start_date:end_date]
            if if_fq in ['00', 'bfq']:
                return data
            else:
                print('CURRENTLY NOT SUPPORT REALTIME FUQUAN')
                return None
    except Exception as e:
        if isinstance(e, TypeError):
            print('Tushare内置的pytdx版本和使用的pytdx版本不同, 请重新安装pytdx以解决此问题')
            print('pip uninstall pytdx')
            print('pip install pytdx')
        else:
            print(e)

# 获取股票分钟线数据
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_min(code, start, end, frequence='1min', ip=None,
                       port=None):
    """获取股票分钟线数据
    
    Args:
        code: 股票代码
        start: 开始时间
        end: 结束时间
        frequence: 周期类型，'1min', '5min', '15min', '30min', '60min'
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 分钟线数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    type_ = ''
    start_date = str(start)[0:10]
    today_ = datetime.date.today()
    lens = util_get_trade_gap(start_date, today_)
    if str(frequence) in ['5', '5m', '5min', 'five']:
        frequence, type_ = 0, '5min'
        lens = 48 * lens
    elif str(frequence) in ['1', '1m', '1min', 'one']:
        frequence, type_ = 8, '1min'
        lens = 240 * lens
    elif str(frequence) in ['15', '15m', '15min', 'fifteen']:
        frequence, type_ = 1, '15min'
        lens = 16 * lens
    elif str(frequence) in ['30', '30m', '30min', 'half']:
        frequence, type_ = 2, '30min'
        lens = 8 * lens
    elif str(frequence) in ['60', '60m', '60min', '1h']:
        frequence, type_ = 3, '60min'
        lens = 4 * lens
    if lens > 20800:
        lens = 20800
    with api.connect(ip, port):

        data = pd.concat(
            [api.to_df(
                api.get_security_bars(
                    frequence, select_market_code(
                        str(code)),
                    str(code),
                    (int(lens / 800) - i) * 800, 800)) for i
             in range(int(lens / 800) + 1)], axis=0, sort=False)
        data = data \
            .drop(['year', 'month', 'day', 'hour', 'minute'], axis=1,
                  inplace=False) \
            .assign(datetime=pd.to_datetime(data['datetime'], utc=False),
                    code=str(code),
                    date=data['datetime'].apply(lambda x: str(x)[0:10]),
                    date_stamp=data['datetime'].apply(
                lambda x: util_date_stamp(x)),
                time_stamp=data['datetime'].apply(
                lambda x: util_time_stamp(x)),
                type=type_).set_index('datetime', drop=False,
                                      inplace=False)[start:end]
        return data.assign(datetime=data['datetime'].apply(lambda x: str(x)))

# 获取股票最新行情
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_latest(code, frequence='day', ip=None, port=None):
    """获取股票最新行情
    
    Args:
        code: 股票代码，可以是单个代码或代码列表
        frequence: 周期类型
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 最新行情数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    code = [code] if isinstance(code, str) else code
    api = TdxHq_API(multithread=True)

    if frequence in ['w', 'W', 'Week', 'week']:
        frequence = 5
    elif frequence in ['month', 'M', 'm', 'Month']:
        frequence = 6
    elif frequence in ['Q', 'Quarter', 'q']:
        frequence = 10
    elif frequence in ['y', 'Y', 'year', 'Year']:
        frequence = 11
    elif frequence in ['5', '5m', '5min', 'five']:
        frequence = 0
    elif frequence in ['1', '1m', '1min', 'one']:
        frequence = 8
    elif frequence in ['15', '15m', '15min', 'fifteen']:
        frequence = 1
    elif frequence in ['30', '30m', '30min', 'half']:
        frequence = 2
    elif frequence in ['60', '60m', '60min', '1h']:
        frequence = 3
    else:
        frequence = 9

    with api.connect(ip, port):
        data = pd.concat([api.to_df(api.get_security_bars(
            frequence, select_market_code(item), item, 0, 1)).assign(
            code=item) for item in code], axis=0, sort=False)
        return data \
            .assign(date=pd.to_datetime(data['datetime']
                                        .apply(lambda x: x[0:10]), utc=False),
                    date_stamp=data['datetime']
                    .apply(lambda x: util_date_stamp(str(x[0:10])))) \
            .set_index('date', drop=False) \
            .drop(['year', 'month', 'day', 'hour', 'minute', 'datetime'],
                  axis=1)

# 获取股票实时行情
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_realtime(code=['000001', '000002'], ip=None, port=None):
    """获取股票实时行情
    
    Args:
        code: 股票代码，可以是单个代码或代码列表
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 实时行情数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    __data = pd.DataFrame()
    with api.connect(ip, port):
        code = [code] if isinstance(code, str) else code
        for id_ in range(int(len(code) / 80) + 1):
            __data = __data.append(api.to_df(api.get_security_quotes(
                [(select_market_code(x), x) for x in
                 code[80 * id_:80 * (id_ + 1)]])))
            __data = __data.assign(datetime=datetime.datetime.now(
            ), servertime=__data['reversed_bytes0'].apply(util_tdxtimestamp))
        data = __data[
            ['datetime', 'servertime', 'active1', 'active2', 'last_close', 'code', 'open',
             'high', 'low', 'price', 'cur_vol',
             's_vol', 'b_vol', 'vol', 'ask1', 'ask_vol1', 'bid1', 'bid_vol1',
             'ask2', 'ask_vol2',
             'bid2', 'bid_vol2', 'ask3', 'ask_vol3', 'bid3', 'bid_vol3',
             'ask4',
             'ask_vol4', 'bid4', 'bid_vol4', 'ask5', 'ask_vol5', 'bid5',
             'bid_vol5']]
        return data.set_index(['datetime', 'code'])

# 获取指数实时行情
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_index_realtime(code=['000001'], ip=None, port=None):
    """获取指数实时行情
    
    Args:
        code: 指数代码，可以是单个代码或代码列表
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 实时行情数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    __data = pd.DataFrame()
    with api.connect(ip, port):
        code = [code] if isinstance(code, str) else code
        for id_ in range(int(len(code) / 80) + 1):
            __data = __data.append(api.to_df(api.get_security_quotes(
                [(select_index_code(x), x) for x in
                 code[80 * id_:80 * (id_ + 1)]])))
            __data = __data.assign(datetime=datetime.datetime.now(
            ), servertime=__data['reversed_bytes0'].apply(util_tdxtimestamp))
        data = __data[
            ['datetime', 'servertime', 'active1', 'active2', 'last_close', 'code', 'open',
             'high', 'low', 'price', 'cur_vol',
             's_vol', 'b_vol', 'vol', 'ask1', 'ask_vol1', 'bid1', 'bid_vol1',
             'ask2', 'ask_vol2',
             'bid2', 'bid_vol2', 'ask3', 'ask_vol3', 'bid3', 'bid_vol3',
             'ask4',
             'ask_vol4', 'bid4', 'bid_vol4', 'ask5', 'ask_vol5', 'bid5',
             'bid_vol5']]
        return data.set_index(['datetime', 'code'])

# 获取股票列表
@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_list(type_='stock', ip=None, port=None):
    """获取股票列表
    
    Args:
        type_: 类型，'stock'表示股票，'index'表示指数，'etf'表示ETF
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 股票列表
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    with api.connect(ip, port):
        data = pd.concat(
            [pd.concat([api.to_df(api.get_security_list(j, i * 1000)).assign(
                sse='sz' if j == 0 else 'sh') for i in
                range(int(api.get_security_count(j) / 1000) + 1)], axis=0, sort=False) for
                j
                in range(2)], axis=0, sort=False)
        data = data.drop_duplicates()
        data = data.loc[:,['code','volunit','decimal_point','name','pre_close','sse']].set_index(
                ['code', 'sse'], drop=False)
        sz = data.query('sse=="sz"')
        sh = data.query('sse=="sh"')

        sz = sz.assign(sec=sz.code.apply(for_sz))
        sh = sh.assign(sec=sh.code.apply(for_sh))

        if type_ in ['stock', 'gp']:
            return pd.concat([sz, sh], sort=False).query(
                'sec=="stock_cn"').sort_index().assign(
                name=data['name'].apply(lambda x: str(x)[0:6]))

        elif type_ in ['index', 'zs']:
            return pd.concat([sz, sh], sort=False).query(
                'sec=="index_cn"').sort_index().assign(
                name=data['name'].apply(lambda x: str(x)[0:6]))
        elif type_ in ['etf', 'ETF']:
            return pd.concat([sz, sh], sort=False).query(
                'sec=="etf_cn"').sort_index().assign(
                name=data['name'].apply(lambda x: str(x)[0:6]))
        else:
            return data.assign(
                code=data['code'].apply(lambda x: str(x))).assign(
                name=data['name'].apply(lambda x: str(x)[0:6]))

# 深市代码分类
def for_sz(code):
    """深市代码分类
    
    Args:
        code: 证券代码
        
    Returns:
        str: 证券类型
    """
    if str(code)[0:2] in ['00', '30', '02']:
        return 'stock_cn'
    elif str(code)[0:2] in ['39']:
        return 'index_cn'
    elif str(code)[0:2] in ['15', '16']:
        return 'etf_cn'
    elif str(code)[0:3] in ['101', '104', '105', '106', '107', '108', '109',
                            '111', '112', '114', '115', '116', '117', '118', '119',
                            '123', '127', '128',
                            '131', '139', ]:
        return 'bond_cn'
    elif str(code)[0:2] in ['20']:
        return 'stockB_cn'
    else:
        return 'undefined'

# 沪市代码分类
def for_sh(code):
    """沪市代码分类
    
    Args:
        code: 证券代码
        
    Returns:
        str: 证券类型
    """
    if str(code)[0] == '6':
        return 'stock_cn'
    elif str(code)[0:3] in ['000', '880']:
        return 'index_cn'
    elif str(code)[0:2] in ['51', '58']:
        return 'etf_cn'
    elif str(code)[0:3] in ['102', '110', '113', '120', '122', '124',
                            '130', '132', '133', '134', '135', '136',
                            '140', '141', '143', '144', '147', '148']:
        return 'bond_cn'
    else:
        return 'undefined'




def __fetch_get_stock_transaction(code, day, retry, api):
    """获取单只股票单日历史分笔成交数据
    
    Args:
        code: 股票代码
        day: 交易日期，格式为'YYYY-MM-DD'
        retry: 重试次数
        api: TdxHq_API 实例
        
    Returns:
        pd.DataFrame: 分笔成交数据
    """
    batch_size = 2000
    data_arr = []
    max_offset = 21
    cur_offset = 0
    type_ = 'tick'
    
    while cur_offset <= max_offset:
        one_chunk = api.get_history_transaction_data(
            select_market_code(str(code)), str(code), cur_offset * batch_size,
            batch_size, int(day.replace('-', '')))
        if one_chunk is None or one_chunk == []:
            break
        data_arr = one_chunk + data_arr
        cur_offset += 1
    
    data_ = api.to_df(data_arr)
    
    if len(data_) < 2 and retry > 0:
        return __fetch_get_stock_transaction(code, day, retry - 1, api)
    
    if len(data_) > 0:
        data_ = data_.assign(
            date=day,
            datetime=pd.to_datetime(data_['time'].apply(
                lambda x: str(day) + ' ' + x), utc=False),
            code=str(code))
        
        data_ = data_.assign(
            date_stamp=data_['datetime'].apply(lambda x: util_date_stamp(x)),
            time_stamp=data_['datetime'].apply(lambda x: util_time_stamp(x)),
            type=type_,
            order=range(len(data_.index))
        ).set_index('datetime', drop=False)
        
        data_['datetime'] = data_['datetime'].apply(lambda x: str(x)[0:19])
    
    return data_


def __fetch_get_index_transaction(code, day, retry, api):
    """获取单个指数单日历史分笔成交数据
    
    Args:
        code: 指数代码
        day: 交易日期，格式为'YYYY-MM-DD'
        retry: 重试次数
        api: TdxHq_API 实例
        
    Returns:
        pd.DataFrame: 分笔成交数据
    """
    batch_size = 2000
    data_arr = []
    max_offset = 21
    cur_offset = 0
    type_ = 'tick'
    
    while cur_offset <= max_offset:
        one_chunk = api.get_history_transaction_data(
            select_index_code(str(code)), str(code), cur_offset * batch_size,
            batch_size, int(day.replace('-', '')))
        if one_chunk is None or one_chunk == []:
            break
        data_arr = one_chunk + data_arr
        cur_offset += 1
    
    data_ = api.to_df(data_arr)
    
    if len(data_) < 2 and retry > 0:
        return __fetch_get_index_transaction(code, day, retry - 1, api)
    
    if len(data_) > 0:
        data_ = data_.assign(
            date=day,
            datetime=pd.to_datetime(data_['time'].apply(
                lambda x: str(day) + ' ' + x), utc=False),
            code=str(code))
        
        data_ = data_.assign(
            date_stamp=data_['datetime'].apply(lambda x: util_date_stamp(x)),
            time_stamp=data_['datetime'].apply(lambda x: util_time_stamp(x)),
            type=type_,
            order=range(len(data_.index))
        ).set_index('datetime', drop=False)
        
        data_['datetime'] = data_['datetime'].apply(lambda x: str(x)[0:19])
    
    return data_


@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_transaction(code, start, end, retry=2, ip=None, port=None):
    """获取股票历史分笔成交数据
    
    Args:
        code: 股票代码
        start: 开始日期，格式为'YYYY-MM-DD'
        end: 结束日期，格式为'YYYY-MM-DD'
        retry: 重试次数，默认为2
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 历史分笔成交数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    
    # 生成日期范围
    date_range = pd.date_range(start=start, end=end)
    trade_dates = [d.strftime('%Y-%m-%d') for d in date_range]
    
    if not trade_dates:
        return None
    
    data = pd.DataFrame()
    with api.connect(ip, port):
        for day in trade_dates:
            try:
                data_ = __fetch_get_stock_transaction(code, day, retry, api)
                if data_ is not None and len(data_) > 0:
                    data = pd.concat([data, data_], axis=0)
                    print(f'Successfully got {code} transaction data on {day}')
                else:
                    print(f'No data for {code} on {day}')
            except Exception as e:
                print(f'Error getting {code} transaction data on {day}: {str(e)}')
    
    if len(data) > 0:
        return data.assign(datetime=data['datetime'].apply(lambda x: str(x)[0:19]))
    else:
        return None


@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_index_transaction(code, start, end, retry=2, ip=None, port=None):
    """获取指数历史分笔成交数据
    
    Args:
        code: 指数代码
        start: 开始日期，格式为'YYYY-MM-DD'
        end: 结束日期，格式为'YYYY-MM-DD'
        retry: 重试次数，默认为2
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 历史分笔成交数据
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    
    # 生成日期范围
    date_range = pd.date_range(start=start, end=end)
    trade_dates = [d.strftime('%Y-%m-%d') for d in date_range]
    
    if not trade_dates:
        return None
    
    data = pd.DataFrame()
    with api.connect(ip, port):
        for day in trade_dates:
            try:
                data_ = __fetch_get_index_transaction(code, day, retry, api)
                if data_ is not None and len(data_) > 0:
                    data = pd.concat([data, data_], axis=0)
                    print(f'Successfully got {code} index transaction data on {day}')
                else:
                    print(f'No data for {code} on {day}')
            except Exception as e:
                print(f'Error getting {code} index transaction data on {day}: {str(e)}')
    
    if len(data) > 0:
        return data.assign(datetime=data['datetime'].apply(lambda x: str(x)[0:19]))
    else:
        return None


@retry(stop_max_attempt_number=3, wait_random_min=50, wait_random_max=100)
def fetch_get_stock_transaction_realtime(code, ip=None, port=None):
    """获取股票实时分笔成交数据
    
    Args:
        code: 股票代码
        ip: 服务器IP，默认使用最优IP
        port: 服务器端口，默认使用最优端口
        
    Returns:
        pd.DataFrame: 实时分笔成交数据
        
    Note:
        buyorsell 字段含义: 1--sell, 0--buy, 2--盘前
    """
    ip, port = get_mainmarket_ip(ip, port)
    api = TdxHq_API()
    
    try:
        with api.connect(ip, port):
            data = pd.DataFrame()
            # 获取最多3个批次的数据
            data = pd.concat([api.to_df(api.get_transaction_data(
                select_market_code(str(code)), code, (2 - i) * 2000, 2000))
                for i in range(3)], axis=0, sort=False)
                
            if 'value' in data.columns:
                data = data.drop(['value'], axis=1)
                
            data = data.dropna()
            
            if len(data) > 0:
                day = datetime.date.today().strftime('%Y-%m-%d')
                data = data.assign(
                    date=day,
                    datetime=pd.to_datetime(data['time'].apply(
                        lambda x: str(day) + ' ' + str(x)), utc=False),
                    code=str(code),
                    order=range(len(data.index))
                ).set_index('datetime', drop=False, inplace=False)
                
                return data
            else:
                return None
    except Exception as e:
        print(f'Error getting realtime transaction data for {code}: {str(e)}')
        return None


# 使用示例
def main():
    # 初始化，自动选择最优服务器
    # best_ip = select_best_ip()
    # print(f"最优股票服务器: {best_ip['stock']['ip']}:{best_ip['stock']['port']}")
    # print(f"最优期货服务器: {best_ip['future']['ip']}:{best_ip['future']['port']}")

    # 获取股票日线数据
    # stock_day = fetch_get_stock_day('000001', '2023-01-01', '2023-01-10')
    # print("股票日线数据:")
    # print(stock_day)

    # 获取股票分钟线数据
    stock_min = fetch_get_stock_min('518880', '2025-06-06', '2025-06-06', frequence='5min')
    print("\n股票5分钟线数据:")
    print(stock_min.head())

    # 获取股票实时行情
    stock_tick = fetch_get_stock_realtime('518880')
    print(stock_tick)

    # Get historical stock transaction data
    stock_tx = fetch_get_stock_transaction('000001', '2025-06-05', '2025-06-05')
    print(stock_tx.head())

    # Get real-time stock transaction data
    realtime_tx = fetch_get_stock_transaction_realtime('000001')
    print(realtime_tx.head())

    # Get historical index transaction data
    index_tx = fetch_get_index_transaction('000001', '2025-06-06', '2025-06-06')
    print(index_tx.head())

    # 获取股票列表
    # stock_list = fetch_get_stock_list()
    # print(f"\n股票列表数量: {len(stock_list)}")
    # print(stock_list.head())


if __name__ == "__main__":
    main()
"""
回测信号验证器
集成回测功能到信号生成流程中，提高信号质量
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from backtest import BacktestEngine, RiskManager, DATA_PROCESSING_AVAILABLE
from core.utils import get_kline, CZSC_FUNC_AVAILABLE
from config.backtest_integration_config import BacktestIntegrationManager, get_backtest_config


@dataclass
class SignalBacktestResult:
    """信号回测结果"""
    signal_name: str
    original_strength: float
    optimized_strength: float
    backtest_performance: Dict[str, float]
    quality_score: float
    confidence_adjustment: float
    risk_metrics: Dict[str, float]
    recommendation: str  # 'enhance', 'maintain', 'reduce', 'reject'


class BacktestSignalValidator:
    """
    回测信号验证器
    使用历史数据验证和优化交易信号
    """
    
    def __init__(self, config_type: str = 'default'):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化配置管理器
        self.config_manager = get_backtest_config(config_type)
        
        # 初始化回测引擎
        backtest_params = self.config_manager.get_backtest_params()
        self.backtest_engine = BacktestEngine(**backtest_params)
        
        # 初始化风险管理器
        self.risk_manager = RiskManager()
        
        # 从配置获取参数
        validation_params = self.config_manager.get_validation_params()
        self.lookback_days = validation_params['lookback_days']
        self.min_trades = validation_params['min_trades']
        self.quality_threshold = validation_params['quality_threshold']
        
        # 优化参数
        optimization_params = self.config_manager.get_optimization_params()
        self.strength_optimization_enabled = optimization_params['enabled']
        self.strength_search_range = optimization_params['search_range']
        self.strength_search_step = optimization_params['search_step']
        
        # 决策调整参数
        self.decision_params = self.config_manager.get_decision_adjustment_params()
        
        # 质量评分权重
        self.quality_weights = self.config_manager.get_quality_weights()
        
        self.logger.info(f"BacktestSignalValidator initialized with config: {config_type}")
        self.logger.info(f"回测启用: {self.config_manager.should_enable_backtest()}")
        self.logger.info(f"决策调整启用: {self.config_manager.should_adjust_decisions()}")
    
    def validate_signal(self, fund_code: str, signal_data: Dict[str, Any], 
                       market_data: Optional[pd.DataFrame] = None) -> SignalBacktestResult:
        """
        验证单个信号的质量
        
        Args:
            fund_code: 基金代码
            signal_data: 信号数据，包含decision, confidence, strength等
            market_data: 市场数据（可选，如果不提供会自动获取）
            
        Returns:
            SignalBacktestResult: 回测验证结果
        """
        try:
            self.logger.info(f"开始验证信号: {fund_code}")
            
            # 获取历史数据 - 可选择不同的时间周期
            # 支持的频率: 'D'(日线), '30min'(30分钟), '60min'(60分钟), '5min'(5分钟), '15min'(15分钟)
            if market_data is None:
                market_data = self._get_historical_data(fund_code, freq='30min')
            
            if market_data is None or len(market_data) < 20:
                return self._create_default_result(signal_data, "数据不足")
            
            # 提取信号信息
            original_strength = signal_data.get('strength', 0.5)
            decision = signal_data.get('decision', 'hold')
            
            # 所有决策类型都进行回测验证，包括hold
            
            # 执行回测验证
            backtest_results = self._run_signal_backtest(
                market_data, decision, original_strength
            )
            
            # 优化信号强度
            optimized_strength, best_performance = self._optimize_signal_strength(
                market_data, decision, original_strength
            )
            
            # 计算质量评分
            quality_score = self._calculate_quality_score(best_performance)
            
            # 计算置信度调整
            confidence_adjustment = self._calculate_confidence_adjustment(
                quality_score, best_performance
            )
            
            # 生成建议
            recommendation = self._generate_recommendation(
                quality_score, original_strength, optimized_strength
            )
            
            # 计算风险指标
            risk_metrics = self._calculate_risk_metrics(best_performance)
            
            result = SignalBacktestResult(
                signal_name=f"{fund_code}_{decision}",
                original_strength=original_strength,
                optimized_strength=optimized_strength,
                backtest_performance=best_performance,
                quality_score=quality_score,
                confidence_adjustment=confidence_adjustment,
                risk_metrics=risk_metrics,
                recommendation=recommendation
            )
            
            self.logger.info(f"信号验证完成: {fund_code}, 质量评分: {quality_score:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"信号验证失败 {fund_code}: {e}")
            return self._create_default_result(signal_data, f"验证失败: {str(e)}")
    
    def _get_historical_data(self, fund_code: str, freq: str = '30min') -> Optional[pd.DataFrame]:
        """获取历史数据
        
        Args:
            fund_code: 基金代码
            freq: 数据频率，支持 'D'(日线), '30min'(30分钟), '60min'(60分钟), '5min'(5分钟)等
        """
        try:
            if not CZSC_FUNC_AVAILABLE:
                self.logger.warning("CZSC函数不可用，无法获取历史数据")
                return None
            
            # 获取历史K线数据
            end_date = datetime.now()
            
            # 根据频率调整数据获取的天数
            if freq == 'D':
                # 日线数据，获取指定的回测天数
                lookback_period = self.lookback_days
            elif freq in ['30min', '60min']:
                # 30分钟或60分钟数据，需要更多天数来获得足够的数据点
                # 30分钟数据：每天约8个交易小时 = 16个30分钟K线
                # 为了获得相当于日线数据的信息量，需要更多天数
                lookback_period = max(self.lookback_days * 3, 60)  # 至少60天
            elif freq in ['5min', '15min']:
                # 更高频数据需要更多天数
                lookback_period = max(self.lookback_days * 5, 90)  # 至少90天
            else:
                lookback_period = self.lookback_days
            
            self.logger.info(f"获取{fund_code}的{freq}数据，回测期间: {lookback_period}天")
            
            kline_data = get_kline(
                fund_code, 
                end_date=end_date.strftime('%Y-%m-%d'),
                freq=freq  # 使用指定的频率
            )
            
            if kline_data is None or len(kline_data) < 20:
                self.logger.warning(f"数据不足: {fund_code}, 数据长度: {len(kline_data) if kline_data is not None else 0}")
                return None
            
            # 确保数据格式正确并进行列名映射
            # get_kline返回的列名: ['symbol', 'dt', 'open', 'close', 'high', 'low', 'vol']
            # 需要映射为标准格式: ['open', 'high', 'low', 'close', 'volume']
            if 'vol' in kline_data.columns:
                kline_data = kline_data.rename(columns={'vol': 'volume'})
            
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in kline_data.columns for col in required_columns):
                self.logger.warning(f"数据格式不完整: {fund_code}, 现有列: {list(kline_data.columns)}")
                return None
            
            # 对于高频数据，取更多的数据点来保证回测的有效性
            if freq == 'D':
                data_points = self.lookback_days
            elif freq in ['30min', '60min']:
                # 30分钟数据：相当于日线数据点数的16倍（每天16个30分钟K线）
                data_points = min(len(kline_data), self.lookback_days * 16)
            elif freq in ['5min', '15min']:
                # 更高频数据取更多点
                data_points = min(len(kline_data), self.lookback_days * 48)
            else:
                data_points = min(len(kline_data), self.lookback_days)
            
            result_data = kline_data.tail(data_points)
            self.logger.info(f"成功获取{fund_code}的{freq}数据: {len(result_data)}个数据点")
            
            return result_data
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败 {fund_code}: {e}")
            return None
    
    def _run_signal_backtest(self, data: pd.DataFrame, decision: str, 
                           strength: float) -> Dict[str, float]:
        """运行信号回测"""
        try:
            # 生成交易信号
            signals = self._generate_trading_signals(data, decision, strength)
            
            # 直接实现简化的回测逻辑，避免方法重载问题
            backtest_result = self._execute_simple_backtest(data, signals)
            
            return backtest_result
            
        except Exception as e:
            self.logger.error(f"回测执行失败: {e}")
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'trade_count': 0
            }
    
    def _generate_trading_signals(self, data: pd.DataFrame, decision: str, 
                                strength: float) -> pd.Series:
        """基于CZSC缠论结构生成交易信号"""
        signals = pd.Series(0, index=data.index)  # 0=hold, 1=buy, -1=sell
        
        # 尝试获取CZSC结构数据
        czsc_signals = self._get_czsc_signals_from_data(data)
        
        if czsc_signals:
            self.logger.info("使用CZSC缠论信号生成交易策略")
            return self._generate_czsc_based_signals(data, decision, strength, czsc_signals)
        else:
            self.logger.warning("未找到CZSC结构数据，使用简化技术指标策略")
            return self._generate_fallback_signals(data, decision, strength)
    
    def _get_czsc_signals_from_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """从数据中提取CZSC信号（模拟从系统中获取）"""
        try:
            # 这里应该从系统的CZSC分析器中获取真实的信号
            # 目前先模拟CZSC信号的生成逻辑
            
            # 简化的CZSC信号识别
            czsc_signals = {
                'fx_signals': [],  # 分型信号
                'bi_signals': [],  # 笔信号
                'xd_signals': [],  # 线段信号
                'structure_signals': []  # 结构信号
            }
            
            # 基于价格数据识别简化的分型
            for i in range(2, len(data) - 2):
                current = data.iloc[i]
                prev = data.iloc[i-1]
                next_bar = data.iloc[i+1]
                
                # 顶分型：前低后低
                if (current['high'] > prev['high'] and 
                    current['high'] > next_bar['high'] and
                    current['high'] > data.iloc[i-2]['high'] and
                    current['high'] > data.iloc[i+2]['high']):
                    
                    czsc_signals['fx_signals'].append({
                        'index': i,
                        'type': 'top',
                        'price': current['high'],
                        'dt': current.name,
                        'strength': self._calculate_fx_strength(data, i, 'top')
                    })
                
                # 底分型：前高后高
                elif (current['low'] < prev['low'] and 
                      current['low'] < next_bar['low'] and
                      current['low'] < data.iloc[i-2]['low'] and
                      current['low'] < data.iloc[i+2]['low']):
                    
                    czsc_signals['fx_signals'].append({
                        'index': i,
                        'type': 'bottom',
                        'price': current['low'],
                        'dt': current.name,
                        'strength': self._calculate_fx_strength(data, i, 'bottom')
                    })
            
            # 基于分型生成笔信号
            czsc_signals['bi_signals'] = self._generate_bi_signals(czsc_signals['fx_signals'])
            
            # 基于笔生成线段信号
            czsc_signals['xd_signals'] = self._generate_xd_signals(czsc_signals['bi_signals'])
            
            # 生成结构信号
            czsc_signals['structure_signals'] = self._generate_structure_signals(
                czsc_signals['fx_signals'], 
                czsc_signals['bi_signals'], 
                czsc_signals['xd_signals']
            )
            
            return czsc_signals if czsc_signals['fx_signals'] else None
            
        except Exception as e:
            self.logger.error(f"CZSC信号提取失败: {e}")
            return None
    
    def _calculate_fx_strength(self, data: pd.DataFrame, index: int, fx_type: str) -> float:
        """计算分型强度"""
        try:
            window = 5
            start_idx = max(0, index - window)
            end_idx = min(len(data), index + window + 1)
            
            if fx_type == 'top':
                current_high = data.iloc[index]['high']
                nearby_highs = data.iloc[start_idx:end_idx]['high']
                strength = (current_high - nearby_highs.mean()) / nearby_highs.std()
            else:  # bottom
                current_low = data.iloc[index]['low']
                nearby_lows = data.iloc[start_idx:end_idx]['low']
                strength = (nearby_lows.mean() - current_low) / nearby_lows.std()
            
            return max(0.1, min(1.0, strength / 2.0))  # 标准化到0.1-1.0
            
        except:
            return 0.5
    
    def _generate_bi_signals(self, fx_signals: List[Dict]) -> List[Dict]:
        """基于分型生成笔信号"""
        bi_signals = []
        
        if len(fx_signals) < 2:
            return bi_signals
        
        for i in range(len(fx_signals) - 1):
            current_fx = fx_signals[i]
            next_fx = fx_signals[i + 1]
            
            # 确保分型类型不同（顶底交替）
            if current_fx['type'] != next_fx['type']:
                bi_signals.append({
                    'start_fx': current_fx,
                    'end_fx': next_fx,
                    'direction': 'up' if current_fx['type'] == 'bottom' else 'down',
                    'strength': (current_fx['strength'] + next_fx['strength']) / 2
                })
        
        return bi_signals
    
    def _generate_xd_signals(self, bi_signals: List[Dict]) -> List[Dict]:
        """基于笔生成线段信号"""
        xd_signals = []
        
        if len(bi_signals) < 3:
            return xd_signals
        
        # 简化的线段识别：连续同方向的笔
        current_direction = None
        start_bi = None
        
        for bi in bi_signals:
            if current_direction is None:
                current_direction = bi['direction']
                start_bi = bi
            elif bi['direction'] != current_direction:
                # 方向改变，形成线段
                if start_bi:
                    xd_signals.append({
                        'start_bi': start_bi,
                        'end_bi': bi_signals[bi_signals.index(bi) - 1],
                        'direction': current_direction,
                        'strength': start_bi['strength']
                    })
                
                current_direction = bi['direction']
                start_bi = bi
        
        return xd_signals
    
    def _generate_structure_signals(self, fx_signals: List[Dict], 
                                  bi_signals: List[Dict], 
                                  xd_signals: List[Dict]) -> List[str]:
        """生成CZSC结构信号"""
        structure_signals = []
        
        # 基于最近的结构生成信号
        if fx_signals:
            recent_fx = fx_signals[-3:] if len(fx_signals) >= 3 else fx_signals
            
            # 连续底分型 -> 买入信号
            if len([fx for fx in recent_fx if fx['type'] == 'bottom']) >= 2:
                structure_signals.append('底分型聚集_买入')
            
            # 连续顶分型 -> 卖出信号
            if len([fx for fx in recent_fx if fx['type'] == 'top']) >= 2:
                structure_signals.append('顶分型聚集_卖出')
        
        if bi_signals:
            recent_bi = bi_signals[-2:] if len(bi_signals) >= 2 else bi_signals
            
            # 连续上涨笔 -> 持续买入
            if all(bi['direction'] == 'up' for bi in recent_bi):
                structure_signals.append('上涨笔延续_买入')
            
            # 连续下跌笔 -> 持续卖出
            if all(bi['direction'] == 'down' for bi in recent_bi):
                structure_signals.append('下跌笔延续_卖出')
        
        if xd_signals:
            recent_xd = xd_signals[-1:] if xd_signals else []
            
            for xd in recent_xd:
                if xd['direction'] == 'up' and xd['strength'] > 0.6:
                    structure_signals.append('上涨线段_强买入')
                elif xd['direction'] == 'down' and xd['strength'] > 0.6:
                    structure_signals.append('下跌线段_强卖出')
        
        return structure_signals
    
    def _generate_czsc_based_signals(self, data: pd.DataFrame, decision: str, 
                                   strength: float, czsc_signals: Dict[str, Any]) -> pd.Series:
        """基于CZSC信号生成交易信号"""
        signals = pd.Series(0, index=data.index)
        
        fx_signals = czsc_signals.get('fx_signals', [])
        bi_signals = czsc_signals.get('bi_signals', [])
        structure_signals = czsc_signals.get('structure_signals', [])
        
        self.logger.info(f"CZSC信号统计: 分型={len(fx_signals)}, 笔={len(bi_signals)}, 结构信号={len(structure_signals)}")
        
        if decision == 'buy':
            # 买入策略：基于CZSC买入信号
            position = 0
            
            for fx in fx_signals:
                if fx['type'] == 'bottom' and fx['strength'] >= strength:
                    # 底分型买入
                    if position == 0:
                        signals.iloc[fx['index']] = 1
                        position = 1
                
                elif fx['type'] == 'top' and position == 1:
                    # 顶分型卖出
                    signals.iloc[fx['index']] = -1
                    position = 0
        
        elif decision == 'sell':
            # 卖出策略：基于CZSC卖出信号
            position = 0
            
            for fx in fx_signals:
                if fx['type'] == 'top' and fx['strength'] >= strength:
                    # 顶分型卖出
                    if position == 0:
                        signals.iloc[fx['index']] = -1
                        position = -1
                
                elif fx['type'] == 'bottom' and position == -1:
                    # 底分型买回
                    signals.iloc[fx['index']] = 1
                    position = 0
        
        elif decision == 'hold':
            # Hold策略：基于CZSC结构信号的趋势跟踪
            position = 0
            
            # 使用笔信号进行趋势跟踪
            for bi in bi_signals:
                if bi['direction'] == 'up' and bi['strength'] >= strength * 0.8:
                    # 上涨笔买入
                    if position == 0:
                        signals.iloc[bi['start_fx']['index']] = 1
                        position = 1
                
                elif bi['direction'] == 'down' and bi['strength'] >= strength * 0.8:
                    # 下跌笔卖出
                    if position == 1:
                        signals.iloc[bi['start_fx']['index']] = -1
                        position = 0
        
        # 记录信号统计
        buy_count = (signals == 1).sum()
        sell_count = (signals == -1).sum()
        self.logger.info(f"CZSC策略生成信号: 买入={buy_count}, 卖出={sell_count}")
        
        return signals
    
    def _generate_fallback_signals(self, data: pd.DataFrame, decision: str, 
                                 strength: float) -> pd.Series:
        """兜底策略：简化的技术指标信号"""
        signals = pd.Series(0, index=data.index)
        
        # 计算简单移动平均线
        data = data.copy()
        data['sma_short'] = data['close'].rolling(window=10).mean()
        data['sma_long'] = data['close'].rolling(window=30).mean()
        
        position = 0
        
        for i in range(30, len(data)):
            if decision == 'buy':
                if position == 0 and data['sma_short'].iloc[i] > data['sma_long'].iloc[i]:
                    signals.iloc[i] = 1
                    position = 1
                elif position == 1 and data['sma_short'].iloc[i] < data['sma_long'].iloc[i]:
                    signals.iloc[i] = -1
                    position = 0
            
            elif decision == 'hold':
                # 简单的趋势跟踪
                if position == 0 and data['sma_short'].iloc[i] > data['sma_long'].iloc[i] * 1.01:
                    signals.iloc[i] = 1
                    position = 1
                elif position == 1 and data['sma_short'].iloc[i] < data['sma_long'].iloc[i] * 0.99:
                    signals.iloc[i] = -1
                    position = 0
        
        return signals
    
    def _execute_simple_backtest(self, data: pd.DataFrame, signals: pd.Series) -> Dict[str, float]:
        """执行简化的回测逻辑"""
        try:
            # 初始化
            initial_capital = 100000
            capital = initial_capital
            position = 0  # 持仓数量
            cash = capital
            portfolio_values = []
            trades = []
            transaction_cost = 0.001
            
            # 确保信号和数据长度一致
            if len(signals) != len(data):
                signals = signals.reindex(data.index, fill_value=0)
            
            # 调试信息
            signal_count = (signals != 0).sum()
            buy_signal_count = (signals == 1).sum()
            sell_signal_count = (signals == -1).sum()
            
            self.logger.info(f"回测开始: 数据长度={len(data)}, 信号长度={len(signals)}")
            self.logger.info(f"信号统计: 买入={buy_signal_count}, 卖出={sell_signal_count}, 总信号={signal_count}")
            
            trade_attempts = 0
            successful_trades = 0
            
            for i, (date, row) in enumerate(data.iterrows()):
                current_price = row['close']
                signal = signals.iloc[i] if i < len(signals) else 0
                
                # 计算当前组合价值
                portfolio_value = cash + position * current_price
                portfolio_values.append(portfolio_value)
                
                # 执行交易
                if signal == 1 and position == 0:  # 买入信号且无持仓
                    trade_attempts += 1
                    # 计算可买入数量
                    available_cash = cash * 0.95  # 保留5%现金
                    shares_to_buy = int(available_cash / (current_price * (1 + transaction_cost)))
                    
                    if shares_to_buy > 0:
                        cost = shares_to_buy * current_price * (1 + transaction_cost)
                        cash -= cost
                        position += shares_to_buy
                        successful_trades += 1
                        
                        trades.append({
                            'date': date,
                            'action': 'buy',
                            'price': current_price,
                            'shares': shares_to_buy,
                            'cost': cost
                        })
                        
                        self.logger.debug(f"买入交易: 日期={date}, 价格={current_price:.4f}, 股数={shares_to_buy}, 成本={cost:.2f}")
                
                elif signal == -1 and position > 0:  # 卖出信号且有持仓
                    trade_attempts += 1
                    # 卖出所有持仓
                    proceeds = position * current_price * (1 - transaction_cost)
                    cash += proceeds
                    successful_trades += 1
                    
                    trades.append({
                        'date': date,
                        'action': 'sell',
                        'price': current_price,
                        'shares': position,
                        'proceeds': proceeds
                    })
                    
                    self.logger.debug(f"卖出交易: 日期={date}, 价格={current_price:.4f}, 股数={position}, 收入={proceeds:.2f}")
                    
                    position = 0
                
                elif signal != 0:
                    # 记录无法执行的交易信号
                    action = "买入" if signal == 1 else "卖出"
                    reason = f"已有持仓" if signal == 1 and position > 0 else f"无持仓" if signal == -1 and position == 0 else "未知"
                    self.logger.debug(f"无法执行{action}信号: 日期={date}, 原因={reason}, 当前持仓={position}")
            
            self.logger.debug(f"交易执行完成: 尝试={trade_attempts}, 成功={successful_trades}, 实际交易记录={len(trades)}")
            
            # 最终清算
            if position > 0:
                final_price = data['close'].iloc[-1]
                final_proceeds = position * final_price * (1 - transaction_cost)
                cash += final_proceeds
                position = 0
            
            # 计算性能指标
            portfolio_values = np.array(portfolio_values)
            returns = np.diff(portfolio_values) / portfolio_values[:-1]
            
            total_return = (portfolio_values[-1] - initial_capital) / initial_capital
            
            # 计算夏普比率
            if len(returns) > 1 and np.std(returns) > 0:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
            else:
                sharpe_ratio = 0.0
            
            # 计算最大回撤
            peak = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - peak) / peak
            max_drawdown = abs(np.min(drawdown))
            
            # 计算胜率 - 修复的简化配对逻辑
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            self.logger.info(f"交易统计: 买入={len(buy_trades)}, 卖出={len(sell_trades)}, 总交易={len(trades)}")
            
            # 简化的配对逻辑：按顺序配对买卖交易
            total_trades = min(len(buy_trades), len(sell_trades))
            winning_trades = 0
            
            if total_trades > 0:
                for i in range(total_trades):
                    buy_cost = buy_trades[i]['cost']
                    sell_proceeds = sell_trades[i]['proceeds']
                    profit = sell_proceeds - buy_cost
                    
                    if profit > 0:
                        winning_trades += 1
                    
                    self.logger.info(f"交易{i+1}: 买入成本={buy_cost:.2f}, 卖出收入={sell_proceeds:.2f}, 盈亏={profit:.2f}")
                
                win_rate = winning_trades / total_trades
                self.logger.info(f"胜率计算: {winning_trades}胜/{total_trades}笔 = {win_rate:.1%}")
            else:
                win_rate = 0.0
                self.logger.info("没有完成的交易对，胜率为0%")
            
            # 计算波动率
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0.0
            
            return {
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'trade_count': total_trades,
                'volatility': volatility,
                'final_value': portfolio_values[-1],
                'var_95': np.percentile(returns, 5) if len(returns) > 0 else 0.0,
                'downside_deviation': np.std(returns[returns < 0]) * np.sqrt(252) if len(returns[returns < 0]) > 1 else 0.0,
                'calmar_ratio': total_return / max_drawdown if max_drawdown > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"简化回测执行失败: {e}")
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'trade_count': 0,
                'volatility': 0.0,
                'final_value': 100000,
                'var_95': 0.0,
                'downside_deviation': 0.0,
                'calmar_ratio': 0.0
            }
    
    def _optimize_signal_strength(self, data: pd.DataFrame, decision: str, 
                                original_strength: float) -> Tuple[float, Dict[str, float]]:
        """优化信号强度"""
        best_strength = original_strength
        best_performance = None
        best_score = -float('inf')
        
        # 在原始强度附近搜索最优值
        search_range = np.clip(
            np.arange(max(0.1, original_strength - 0.3), 
                     min(1.0, original_strength + 0.3), 0.1),
            0.1, 0.9
        )
        
        for strength in search_range:
            try:
                performance = self._run_signal_backtest(data, decision, strength)
                
                # 综合评分：夏普比率 + 胜率 - 最大回撤
                score = (performance.get('sharpe_ratio', 0) * 0.4 + 
                        performance.get('win_rate', 0) * 0.3 - 
                        performance.get('max_drawdown', 0) * 0.3)
                
                if score > best_score and performance.get('trade_count', 0) >= self.min_trades:
                    best_score = score
                    best_strength = strength
                    best_performance = performance
                    
            except Exception as e:
                self.logger.warning(f"强度优化失败 {strength}: {e}")
                continue
        
        # 如果没有找到更好的结果，使用原始强度的回测结果
        if best_performance is None:
            best_performance = self._run_signal_backtest(data, decision, original_strength)
        
        return best_strength, best_performance
    
    def _calculate_quality_score(self, performance: Dict[str, float]) -> float:
        """计算信号质量评分"""
        try:
            # 多维度质量评分，使用配置的权重
            sharpe_score = min(performance.get('sharpe_ratio', 0) / 2.0, 1.0)  # 夏普比率标准化
            return_score = min(performance.get('total_return', 0) / 0.2, 1.0)   # 收益率标准化
            win_rate_score = performance.get('win_rate', 0)                      # 胜率
            drawdown_score = max(0, 1 - performance.get('max_drawdown', 0) / 0.2)  # 回撤惩罚
            
            # 使用配置的权重进行加权平均
            quality_score = (
                sharpe_score * self.quality_weights['sharpe_ratio'] +
                return_score * self.quality_weights['total_return'] +
                win_rate_score * self.quality_weights['win_rate'] +
                drawdown_score * self.quality_weights['max_drawdown']
            )
            
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            self.logger.error(f"质量评分计算失败: {e}")
            return 0.0
    
    def _calculate_confidence_adjustment(self, quality_score: float, 
                                       performance: Dict[str, float]) -> float:
        """计算置信度调整系数"""
        try:
            # 基础调整：基于质量评分
            base_adjustment = (quality_score - 0.5) * 0.4  # -0.2 到 +0.2
            
            # 交易频率调整：交易次数太少会降低置信度
            trade_count = performance.get('trade_count', 0)
            frequency_adjustment = 0
            if trade_count < self.min_trades:
                frequency_adjustment = -0.1
            elif trade_count > 15:
                frequency_adjustment = 0.05
            
            # 稳定性调整：基于夏普比率
            sharpe_ratio = performance.get('sharpe_ratio', 0)
            stability_adjustment = 0
            if sharpe_ratio > 1.0:
                stability_adjustment = 0.1
            elif sharpe_ratio < 0:
                stability_adjustment = -0.15
            
            total_adjustment = base_adjustment + frequency_adjustment + stability_adjustment
            return max(-0.3, min(0.3, total_adjustment))  # 限制在-0.3到+0.3之间
            
        except Exception as e:
            self.logger.error(f"置信度调整计算失败: {e}")
            return 0.0
    
    def _generate_recommendation(self, quality_score: float, original_strength: float, 
                               optimized_strength: float) -> str:
        """生成优化建议"""
        try:
            # 基于质量评分的基础建议
            if quality_score >= 0.8:
                base_rec = 'enhance'
            elif quality_score >= 0.6:
                base_rec = 'maintain'
            elif quality_score >= 0.4:
                base_rec = 'reduce'
            else:
                base_rec = 'reject'
            
            # 考虑强度优化结果
            strength_improvement = optimized_strength - original_strength
            if abs(strength_improvement) > 0.2:
                if strength_improvement > 0 and base_rec in ['enhance', 'maintain']:
                    return 'enhance'
                elif strength_improvement < 0 and base_rec in ['reduce', 'reject']:
                    return 'reduce'
            
            return base_rec
            
        except Exception as e:
            self.logger.error(f"建议生成失败: {e}")
            return 'maintain'
    
    def _calculate_risk_metrics(self, performance: Dict[str, float]) -> Dict[str, float]:
        """计算风险指标"""
        try:
            return {
                'volatility': performance.get('volatility', 0.0),
                'max_drawdown': performance.get('max_drawdown', 0.0),
                'var_95': performance.get('var_95', 0.0),
                'downside_deviation': performance.get('downside_deviation', 0.0),
                'calmar_ratio': performance.get('calmar_ratio', 0.0)
            }
        except Exception as e:
            self.logger.error(f"风险指标计算失败: {e}")
            return {}
    
    def _create_default_result(self, signal_data: Dict[str, Any], reason: str) -> SignalBacktestResult:
        """创建默认结果"""
        original_strength = signal_data.get('strength', 0.5)
        
        return SignalBacktestResult(
            signal_name=f"default_{reason}",
            original_strength=original_strength,
            optimized_strength=original_strength,
            backtest_performance={
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.5,
                'trade_count': 0
            },
            quality_score=0.5,
            confidence_adjustment=0.0,
            risk_metrics={},
            recommendation='maintain'
        )
    
    def batch_validate_signals(self, signals: List[Dict[str, Any]]) -> List[SignalBacktestResult]:
        """批量验证信号"""
        results = []
        
        for signal in signals:
            fund_code = signal.get('fund_code')
            if fund_code:
                result = self.validate_signal(fund_code, signal)
                results.append(result)
        
        return results
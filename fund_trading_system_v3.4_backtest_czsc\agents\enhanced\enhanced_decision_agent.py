"""
增强版决策智能体 V3
集成六大维度评估体系的高级决策智能体，提供全方位的基金投资决策支持
"""

import logging
import sys
import os
from datetime import datetime
from collections import deque
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import *
from core.data_structures import *
from evaluators import *
from analyzers import *


class EnhancedDecisionAgentV3(BaseAgent):
    """
    @class EnhancedDecisionAgentV3
    @brief 增强版决策智能体 V3
    @details 集成六大维度评估体系的高级决策智能体，提供全方位的基金投资决策支持
    """
    
    def __init__(self, name: str = "EnhancedDecisionAgentV3"):
        super().__init__(name, "enhanced_decision_v3")
        
        # 初始化六大维度评估器
        self.trend_evaluator = TrendEvaluator()
        self.volatility_evaluator = VolatilityEvaluator()
        self.liquidity_evaluator = LiquidityEvaluator()
        self.sentiment_evaluator = SentimentEvaluator()
        self.structural_evaluator = StructuralEvaluator()
        self.transition_evaluator = TransitionEvaluator()
        
        # 初始化智能组件
        self.conflict_resolver = SignalConflictResolver()
        self.weight_manager = DynamicWeightManager()
        self.fractal_validator = FractalValidator()
        self.market_classifier = MultiDimensionalMarketClassifier()
        
        # 决策历史记录
        self.decision_history = deque(maxlen=100)
        
        # 性能指标
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'accuracy_rate': 0.0,
            'last_update': datetime.now()
        }
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 处理增强决策请求
        @param data: 包含基金代码和各类数据的字典
        @return: 增强版决策结果
        """
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            # 执行增强版决策流程
            enhanced_result = self.make_enhanced_decision_v3(data)
            
            # 记录决策历史
            self._record_decision(enhanced_result)
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Enhanced decision V3 failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'decision': 'hold',
                'confidence': 0.0
            }
    
    def make_enhanced_decision_v3(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行增强版决策流程 V3
        @param data: 输入数据
        @return: 决策结果
        """
        fund_code = data.get('fund_code')
        
        try:
            # 第一阶段：数据收集和预处理
            processed_data = self._preprocess_data(data)
            
            # 第二阶段：六大维度评估
            dimension_evaluations = self._evaluate_all_dimensions(processed_data)
            
            # 第三阶段：市场分类
            # 获取基金类别信息（如果可用）
            fund_category = self._get_fund_category(fund_code) if fund_code else None
            market_classification = self.market_classifier.classify_market(
                dimension_evaluations, fund_code=fund_code, fund_category=fund_category
            )
            
            # 第四阶段：信号冲突解决
            conflict_resolution = self.conflict_resolver.resolve_conflicts(dimension_evaluations, fund_code)
            
            # 第五阶段：动态权重调整
            market_condition = self.weight_manager.detect_market_condition(dimension_evaluations)
            dynamic_weights = self.weight_manager.calculate_dynamic_weights(dimension_evaluations, market_condition)
            
            # 第六阶段：分型质量验证
            fractal_validation = self.fractal_validator.validate_fractal_quality(processed_data)
            
            # 第七阶段：综合决策生成
            final_decision = self._generate_final_decision_v3(
                dimension_evaluations, 
                market_classification,
                conflict_resolution,
                dynamic_weights,
                fractal_validation,
                processed_data
            )
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"Enhanced decision V3 process failed: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'decision': 'hold',
                'confidence': 0.0
            }

    def _preprocess_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据预处理"""
        try:
            # 数据质量评估
            data_quality = self._assess_data_quality(data)

            # 添加质量评估结果到数据中
            processed_data = data.copy()
            processed_data['data_quality'] = data_quality

            return processed_data
        except Exception as e:
            self.logger.error(f"Data preprocessing failed: {str(e)}")
            return data

    def _evaluate_all_dimensions(self, data: Dict[str, Any]) -> Dict[str, DimensionEvaluationResult]:
        """执行六大维度评估"""
        evaluations = {}

        try:
            # 趋势维度评估
            evaluations["趋势"] = self.trend_evaluator.evaluate(data)

            # 波动性维度评估
            evaluations["波动性"] = self.volatility_evaluator.evaluate(data)

            # 流动性维度评估
            evaluations["流动性"] = self.liquidity_evaluator.evaluate(data)

            # 情绪维度评估
            evaluations["情绪"] = self.sentiment_evaluator.evaluate(data)

            # 结构维度评估
            evaluations["结构"] = self.structural_evaluator.evaluate(data)

            # 转换维度评估
            evaluations["转换"] = self.transition_evaluator.evaluate(data)

        except Exception as e:
            self.logger.error(f"Dimension evaluation failed: {str(e)}")

        return evaluations

    def _generate_final_decision_v3(self, dimension_evaluations: Dict[str, DimensionEvaluationResult],
                                   market_classification: Dict[str, Any],
                                   conflict_resolution: Dict[str, Any],
                                   dynamic_weights: Dict[str, float],
                                   fractal_validation: Dict[str, Any],
                                   processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终决策"""
        try:
            fund_code = processed_data.get('fund_code', 'UNKNOWN')

            # 计算加权综合评分
            weighted_score = 0.0
            for dim_name, eval_result in dimension_evaluations.items():
                if dim_name in dynamic_weights:
                    weighted_score += eval_result.score * dynamic_weights[dim_name]

            # 根据市场分类调整决策阈值
            classification = market_classification.get('primary_classification', '未知市场')
            decision_threshold = self._get_decision_threshold(classification)

            # 生成基础决策
            if weighted_score >= decision_threshold:
                base_decision = "buy"
            elif weighted_score <= -decision_threshold:
                base_decision = "sell"
            else:
                base_decision = "hold"

            # 应用风险控制
            final_decision = self._apply_risk_controls(
                base_decision, weighted_score, fractal_validation, dimension_evaluations
            )

            # 计算置信度
            confidence = self._calculate_decision_confidence(
                dimension_evaluations, conflict_resolution, fractal_validation
            )

            return {
                'fund_code': fund_code,
                'decision': final_decision,
                'confidence': confidence,
                'weighted_score': weighted_score,
                'market_classification': market_classification,
                'dimension_evaluations': {k: v.__dict__ for k, v in dimension_evaluations.items()},
                'dynamic_weights': dynamic_weights,
                'fractal_validation': fractal_validation,
                'conflict_resolution': conflict_resolution,
                'decision_time': datetime.now().isoformat(),
                'decision_threshold': decision_threshold
            }

        except Exception as e:
            self.logger.error(f"Final decision generation failed: {str(e)}")
            return {
                'fund_code': processed_data.get('fund_code', 'UNKNOWN'),
                'decision': 'hold',
                'confidence': 0.0,
                'error': str(e)
            }

    def _assess_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality_score = 1.0
            issues = []

            # 检查必要字段
            required_fields = ['fund_code', 'price_data', 'technical_data']
            for field in required_fields:
                if field not in data or not data[field]:
                    quality_score -= 0.3
                    issues.append(f"Missing {field}")

            return {
                'quality_score': max(0.0, quality_score),
                'issues': issues,
                'overall_quality': quality_score >= 0.7
            }
        except Exception:
            return {'quality_score': 0.0, 'issues': ['Assessment failed'], 'overall_quality': False}

    def _get_fund_category(self, fund_code: str) -> str:
        """获取基金类别"""
        try:
            # 尝试从系统中获取基金分析器
            from optimizers.fund_analyzer import FundCharacteristicsAnalyzer
            analyzer = FundCharacteristicsAnalyzer()
            return analyzer.get_fund_category(fund_code)
        except Exception:
            # 如果无法获取，返回None
            return None

    def _get_decision_threshold(self, classification: str) -> float:
        """根据市场分类获取决策阈值"""
        threshold_map = {
            "强势突破": 0.2,
            "趋势确认": 0.3,
            "震荡整理": 0.5,
            "风险警示": 0.7,
            "未知市场": 0.4
        }
        return threshold_map.get(classification, 0.4)

    def _apply_risk_controls(self, base_decision: str, weighted_score: float,
                           fractal_validation: Dict[str, Any],
                           dimension_evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """应用风险控制"""
        try:
            # 分型质量控制
            fractal_quality = fractal_validation.get('overall_quality', 0.0)
            if fractal_quality < 0.3 and base_decision in ['buy', 'sell']:
                return 'hold'  # 分型质量太差，保持观望

            # 波动性控制
            volatility_eval = dimension_evaluations.get('波动性')
            if volatility_eval and volatility_eval.score >= 0.8:
                if base_decision == 'buy':
                    return 'hold'  # 波动性过高，暂缓买入

            # 流动性控制
            liquidity_eval = dimension_evaluations.get('流动性')
            if liquidity_eval and liquidity_eval.score <= 0.2:
                if base_decision in ['buy', 'sell']:
                    return 'hold'  # 流动性太差，暂缓交易

            return base_decision

        except Exception:
            return 'hold'

    def _calculate_decision_confidence(self, dimension_evaluations: Dict[str, DimensionEvaluationResult],
                                     conflict_resolution: Dict[str, Any],
                                     fractal_validation: Dict[str, Any]) -> float:
        """计算决策置信度"""
        try:
            # 基础置信度（各维度置信度平均值）
            base_confidence = np.mean([eval_result.confidence for eval_result in dimension_evaluations.values()])

            # 冲突解决置信度
            conflict_confidence = conflict_resolution.get('confidence', 0.5)

            # 分型质量置信度
            fractal_confidence = fractal_validation.get('confidence', 0.5)

            # 综合置信度
            final_confidence = (base_confidence * 0.5 + conflict_confidence * 0.3 + fractal_confidence * 0.2)

            return max(0.1, min(0.95, final_confidence))

        except Exception:
            return 0.3

    def _record_decision(self, decision_result: Dict[str, Any]) -> None:
        """记录决策历史"""
        try:
            self.decision_history.append({
                'timestamp': datetime.now(),
                'fund_code': decision_result.get('fund_code'),
                'decision': decision_result.get('decision'),
                'confidence': decision_result.get('confidence'),
                'weighted_score': decision_result.get('weighted_score')
            })

            # 更新性能指标
            self.performance_metrics['total_decisions'] += 1
            self.performance_metrics['last_update'] = datetime.now()

        except Exception as e:
            self.logger.error(f"Failed to record decision: {str(e)}")

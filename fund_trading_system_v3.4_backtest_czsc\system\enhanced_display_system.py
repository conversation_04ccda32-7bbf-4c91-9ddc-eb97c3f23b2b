#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版展示系统
整合enhanced_backtest.py的优秀展示功能和原系统的智能体分析
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, List
import json


class EnhancedDisplaySystem:
    """增强版展示系统"""
    
    def __init__(self):
        self.display_width = 80
        
    def display_comprehensive_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """
        综合展示分析结果 - 整合原系统智能体分析和增强回测展示
        """
        print("\n" + "="*self.display_width)
        print(f"📈 增强版基金分析报告 - {analysis_result.get('fund_code', 'N/A')}")
        print("="*self.display_width)
        
        # 1. 基础信息展示
        self._display_basic_info(analysis_result)
        
        # 2. 智能体分析结果
        self._display_agent_analysis(analysis_result)
        
        # 3. 技术指标分析
        self._display_technical_analysis(analysis_result)
        
        # 4. 风险控制信息
        self._display_risk_control(analysis_result)
        
        # 5. 最终决策
        self._display_final_decision(analysis_result)
        
        # 6. 如果有回测结果，展示回测指标
        if 'backtest_results' in analysis_result:
            self._display_backtest_metrics(analysis_result['backtest_results'])
    
    def _display_basic_info(self, analysis_result: Dict[str, Any]) -> None:
        """展示基础信息"""
        print(f"\n💰 基金基础信息:")
        print(f"   代码: {analysis_result.get('fund_code', 'N/A')}")
        
        # 使用正确的时间字段
        analysis_time = analysis_result.get('analysis_time') or analysis_result.get('analysis_timestamp')
        if analysis_time:
            if isinstance(analysis_time, str):
                # 如果是ISO格式字符串，转换为更友好的格式
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(analysis_time.replace('Z', '+00:00'))
                    analysis_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
            print(f"   分析时间: {analysis_time}")
        else:
            print(f"   分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 价格信息 - 从资金流向分析中获取
        flow_data = analysis_result.get('traditional_agents', {}).get('flow_analysis', {})
        if flow_data and 'price_data' in flow_data:
            price_data = flow_data['price_data']
            print(f"   当前价格: {price_data.get('price', 0):.3f}")
            print(f"   涨跌幅: {price_data.get('change_rate', 0):.2f}%")
            print(f"   成交量: {price_data.get('volume', 0):,.0f}")
        else:
            # 如果没有价格信息，显示N/A
            print(f"   当前价格: N/A")
            print(f"   涨跌幅: N/A")
            print(f"   成交量: N/A")
    
    def _display_agent_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """展示智能体分析结果"""
        print(f"\n🤖 智能体分析结果:")
        
        # 传统智能体 - 使用正确的字段名
        if 'traditional_agents' in analysis_result:
            traditional = analysis_result['traditional_agents']
            
            # 技术分析
            tech_analysis = traditional.get('technical_analysis', {})
            if tech_analysis:
                decision = tech_analysis.get('decision') or tech_analysis.get('buy_signal', False)
                if isinstance(decision, bool):
                    decision = 'BUY' if decision else 'HOLD'
                print(f"   📊 技术分析: {decision}")
            else:
                print(f"   📊 技术分析: N/A")
            
            # 卦象分析
            gua_analysis = traditional.get('gua_analysis', {})
            if gua_analysis:
                gua_decision = 'N/A'
                if gua_analysis.get('is_buy_gua'):
                    gua_decision = 'BUY'
                elif gua_analysis.get('is_sell_gua'):
                    gua_decision = 'SELL'
                elif gua_analysis.get('is_select_gua'):
                    gua_decision = 'SELECT'
                else:
                    gua_decision = 'HOLD'
                print(f"   🔮 卦象分析: {gua_decision} (卦分: {gua_analysis.get('gua_score', 0):.2f})")
            else:
                print(f"   🔮 卦象分析: N/A")
            
            # 资金流向分析
            flow_analysis = traditional.get('flow_analysis', {})
            if flow_analysis:
                flow_decision = 'N/A'
                if flow_analysis.get('high_liquidity'):
                    flow_decision = 'POSITIVE'
                elif flow_analysis.get('low_liquidity'):
                    flow_decision = 'NEGATIVE'
                else:
                    flow_decision = 'NEUTRAL'
                print(f"   💰 资金流向: {flow_decision}")
            else:
                print(f"   💰 资金流向: N/A")
        
        # LLM分析
        if 'llm_analysis' in analysis_result:
            llm = analysis_result['llm_analysis']
            if 'error' not in llm:
                print(f"   🤖 LLM分析: {llm.get('market_sentiment', 'N/A')} (置信度: {llm.get('confidence_level', 0):.2f})")
                if llm.get('market_drivers'):
                    # 处理market_drivers，可能是字符串或列表
                    drivers = llm['market_drivers']
                    if isinstance(drivers, str):
                        print(f"   - 市场驱动: {drivers[:200]}...")
                    elif isinstance(drivers, list):
                        print(f"   - 市场驱动: {', '.join(str(d) for d in drivers[:3])}")
            else:
                print(f"   🤖 LLM分析: 失败 - {llm.get('error', '未知错误')[:50]}...")
    
    def _display_technical_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """展示技术分析详情"""
        tech_analysis = analysis_result.get('traditional_agents', {}).get('technical_analysis', {})
        if not tech_analysis:
            return
            
        print(f"\n📈 技术指标详情:")
        
        # 从技术指标中提取实际可用的数据
        indicators = tech_analysis.get('technical_indicators', {})
        signals = tech_analysis.get('signals', {})
        
        # 移动平均线
        if 'ma5' in indicators and 'ma20' in indicators:
            ma5 = indicators.get('ma5', 0)
            ma20 = indicators.get('ma20', 0)
            trend = "上升" if ma5 > ma20 else "下降"
            print(f"   MA趋势: {trend} | MA5: {ma5:.4f} | MA20: {ma20:.4f}")
        
        # MACD
        if 'macd' in indicators:
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            macd_hist = indicators.get('macd_hist', 0)
            signal_type = "金叉" if macd > macd_signal else "死叉"
            print(f"   MACD: {signal_type} | MACD: {macd:.4f} | Signal: {macd_signal:.4f}")
        
        # RSI
        if 'rsi' in indicators:
            rsi_value = indicators.get('rsi', 50)
            if rsi_value > 70:
                rsi_level = "超买"
            elif rsi_value < 30:
                rsi_level = "超卖"
            else:
                rsi_level = "正常"
            print(f"   RSI: {rsi_level} ({rsi_value:.1f})")
        
        # 布林带
        if 'bb_upper' in indicators and 'bb_lower' in indicators:
            bb_upper = indicators.get('bb_upper', 0)
            bb_lower = indicators.get('bb_lower', 0)
            bb_middle = indicators.get('bb_middle', 0)
            current_price = indicators.get('close', bb_middle)
            
            if current_price > bb_upper:
                bb_position = "上轨突破"
            elif current_price < bb_lower:
                bb_position = "下轨突破"
            else:
                bb_position = "中轨区间"
            
            bb_width = (bb_upper - bb_lower) / bb_middle if bb_middle > 0 else 0
            print(f"   布林带: {bb_position} | 宽度: {bb_width:.3f}")
        
        # KDJ指标
        if 'kdj_k' in indicators:
            kdj_k = indicators.get('kdj_k', 50)
            kdj_d = indicators.get('kdj_d', 50)
            kdj_j = indicators.get('kdj_j', 50)
            print(f"   KDJ: K={kdj_k:.1f} D={kdj_d:.1f} J={kdj_j:.1f}")
        
        # 成交量分析
        if 'volume_ratio' in indicators:
            vol_ratio = indicators.get('volume_ratio', 1.0)
            vol_status = "放量" if vol_ratio > 1.5 else "缩量" if vol_ratio < 0.8 else "正常"
            print(f"   成交量: {vol_status} (比率: {vol_ratio:.2f})")
    
    def _display_risk_control(self, analysis_result: Dict[str, Any]) -> None:
        """展示风险控制信息"""
        risk_control = analysis_result.get('risk_control', {})
        if not risk_control:
            return
            
        print(f"\n🛡️ 风险控制:")
        print(f"   风险等级: {risk_control.get('risk_level', 'N/A')}")
        print(f"   仓位限制: {risk_control.get('position_limit', 0):.1%}")
        
        if risk_control.get('risk_alerts'):
            print(f"   ⚠️ 风险警告: {len(risk_control['risk_alerts'])}项")
            for alert in risk_control['risk_alerts'][:3]:  # 只显示前3项
                print(f"     - {alert.get('message', 'N/A')}")
    
    def _display_final_decision(self, analysis_result: Dict[str, Any]) -> None:
        """展示最终决策"""
        print(f"\n🎯 最终决策:")
        
        # 原始决策
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        print(f"   原始决策: {enhanced_decision.get('decision', 'N/A')}")
        print(f"   决策强度: {enhanced_decision.get('strength', 0):.2f}")
        print(f"   置信度: {enhanced_decision.get('confidence', 0):.2f}")
        
        # 最终决策（经过风控）
        final_decision = analysis_result.get('final_decision', 'N/A')
        print(f"   最终决策: {final_decision}")
        
        # 决策理由
        if enhanced_decision.get('reasoning'):
            print(f"   决策理由: {enhanced_decision['reasoning'][:100]}...")
    
    def _display_backtest_metrics(self, backtest_results: Dict[str, Any]) -> None:
        """展示回测指标 - 来自enhanced_backtest.py的优秀展示"""
        print(f"\n📊 回测性能指标:")
        print("-" * 60)
        
        metrics = backtest_results.get('metrics', {})
        
        print(f"💰 收益指标:")
        print(f"   总收益率: {metrics.get('total_return', 0):.2%}")
        print(f"   年化收益率: {metrics.get('annual_return', 0):.2%}")
        print(f"   基准收益率: {metrics.get('benchmark_total_return', 0):.2%}")
        print(f"   超额收益: {metrics.get('excess_return', 0):.2%}")
        
        print(f"\n📈 风险指标:")
        print(f"   年化波动率: {metrics.get('volatility', 0):.2%}")
        print(f"   夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
        print(f"   最大回撤: {metrics.get('max_drawdown', 0):.2%}")
        print(f"   卡尔马比率: {metrics.get('calmar_ratio', 0):.3f}")
        
        print(f"\n💼 交易统计:")
        print(f"   总交易次数: {metrics.get('total_trades', 0)}")
        print(f"   交易胜率: {metrics.get('win_rate', 0):.2%}")
        print(f"   盈亏比: {metrics.get('profit_loss_ratio', 0):.2f}")
        print(f"   信息比率: {metrics.get('information_ratio', 0):.3f}")
    
    def display_strategy_comparison(self, strategy_results: List[Dict[str, Any]]) -> None:
        """
        展示策略对比结果 - 来自enhanced_backtest.py的多策略对比
        """
        if not strategy_results:
            return
            
        print("\n" + "="*self.display_width)
        print("📊 策略对比分析")
        print("="*self.display_width)
        
        # 创建对比表格
        comparison_data = []
        for result in strategy_results:
            comparison_data.append({
                '策略': result.get('strategy_name', 'N/A'),
                '总收益率': f"{result.get('total_return', 0):.2%}",
                '年化收益': f"{result.get('annual_return', 0):.2%}",
                '夏普比率': f"{result.get('sharpe_ratio', 0):.3f}",
                '最大回撤': f"{result.get('max_drawdown', 0):.2%}",
                '胜率': f"{result.get('win_rate', 0):.2%}",
                '交易次数': result.get('total_trades', 0)
            })
        
        # 打印表格
        if comparison_data:
            df = pd.DataFrame(comparison_data)
            print(df.to_string(index=False))
            
            # 找出最佳策略
            best_sharpe_idx = max(range(len(strategy_results)), 
                                key=lambda i: strategy_results[i].get('sharpe_ratio', 0))
            best_return_idx = max(range(len(strategy_results)), 
                                key=lambda i: strategy_results[i].get('total_return', 0))
            
            print(f"\n🏆 最佳夏普比率: {strategy_results[best_sharpe_idx].get('strategy_name', 'N/A')}")
            print(f"🏆 最佳收益率: {strategy_results[best_return_idx].get('strategy_name', 'N/A')}")
    
    def generate_comprehensive_report(self, analysis_result: Dict[str, Any], 
                                    strategy_results: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成综合报告 - 整合两个系统的报告功能
        """
        report = {
            'report_type': 'comprehensive_analysis',
            'generated_at': datetime.now().isoformat(),
            'fund_code': analysis_result.get('fund_code', 'N/A'),
            'analysis_summary': {
                'final_decision': analysis_result.get('final_decision', 'N/A'),
                'decision_strength': analysis_result.get('enhanced_decision', {}).get('strength', 0),
                'risk_level': analysis_result.get('risk_control', {}).get('risk_level', 'N/A')
            },
            'agent_decisions': self._extract_agent_decisions(analysis_result),
            'technical_indicators': self._extract_technical_indicators(analysis_result),
            'risk_metrics': self._extract_risk_metrics(analysis_result)
        }
        
        # 添加策略对比结果
        if strategy_results:
            report['strategy_comparison'] = strategy_results
            
        # 添加回测结果
        if 'backtest_results' in analysis_result:
            report['backtest_performance'] = analysis_result['backtest_results']
            
        return report
    
    def _extract_agent_decisions(self, analysis_result: Dict[str, Any]) -> Dict[str, str]:
        """提取智能体决策"""
        decisions = {}
        
        # 传统智能体
        traditional = analysis_result.get('traditional_agents', {})
        decisions['technical'] = traditional.get('technical_analysis', {}).get('decision', 'N/A')
        decisions['fundamental'] = traditional.get('fundamental_analysis', {}).get('decision', 'N/A')
        decisions['fund_flow'] = traditional.get('fund_flow_analysis', {}).get('decision', 'N/A')
        
        # 增强智能体
        enhanced = analysis_result.get('enhanced_agents', {})
        decisions['soros'] = enhanced.get('soros_analysis', {}).get('decision', 'N/A')
        decisions['ai'] = enhanced.get('ai_analysis', {}).get('decision', 'N/A')
        
        # LLM分析
        llm = analysis_result.get('llm_analysis', {})
        decisions['llm'] = llm.get('market_sentiment', 'N/A') if 'error' not in llm else 'Error'
        
        return decisions
    
    def _extract_technical_indicators(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取技术指标"""
        tech_analysis = analysis_result.get('traditional_agents', {}).get('technical_analysis', {})
        return {
            'ma_trend': tech_analysis.get('ma_analysis', {}).get('trend', 'N/A'),
            'macd_signal': tech_analysis.get('macd_analysis', {}).get('signal', 'N/A'),
            'rsi_level': tech_analysis.get('rsi_analysis', {}).get('level', 'N/A'),
            'bollinger_position': tech_analysis.get('bollinger_analysis', {}).get('position', 'N/A')
        }
    
    def _extract_risk_metrics(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取风险指标"""
        risk_control = analysis_result.get('risk_control', {})
        return {
            'risk_level': risk_control.get('risk_level', 'N/A'),
            'position_limit': risk_control.get('position_limit', 0),
            'alert_count': len(risk_control.get('risk_alerts', []))
        }
    
    def save_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'enhanced_analysis_report_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 综合报告已保存: {filename}")
        return filename
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版股票回测系统
包含多种策略和详细分析
使用QUANTAXIS获取真实数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

# 直接导入QUANTAXIS的TDX函数
from QUANTAXIS.QAFetch.QATdx import QA_fetch_get_stock_day, QA_fetch_get_stock_min


def get_kline(symbol, freq='D', start_date=None, end_date=None):
    """
    使用原版QATDX获取K线数据
    
    Args:
        symbol: 股票代码
        freq: 频率 ('D'=日线, '5min'=5分钟, '1min'=1分钟等)
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
    
    Returns:
        pd.DataFrame: K线数据，包含dt, open, high, low, close, vol列
    """
    try:
        # 设置默认日期范围
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            # 默认获取2年数据，确保有足够的数据
            start_date = (datetime.now() - timedelta(days=2500)).strftime('%Y-%m-%d')
        
        print(f"📊 使用原版QATDX获取{symbol}数据，频率:{freq}，时间范围:{start_date}至{end_date}")
        
        # 使用原版QATDX的函数获取数据
        if freq == 'D' or freq == 'day':
            # 获取日线数据
            df = QA_fetch_get_stock_day(symbol, start_date, end_date,  frequence='day')
        elif freq in ['5min', '5m']:
            # 获取5分钟数据
            df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='5min')
        elif freq in ['1min', '1m']:
            # 获取1分钟数据
            df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='1min')
        elif freq in ['15min', '15m']:
            # 获取15分钟数据
            df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='15min')
        elif freq in ['30min', '30m']:
            # 获取30分钟数据
            df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='30min')
        elif freq in ['60min', '60m', '1h']:
            # 获取60分钟数据
            df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='60min')
        else:
            # 默认使用日线
            df = QA_fetch_get_stock_day(symbol, start_date, end_date, frequence='day')
        
        if df is None or df.empty:
            raise ValueError(f"获取{symbol}数据为空")
        
        # 重置索引，确保有干净的数据结构
        if 'date' in df.columns:
            df = df.reset_index(drop=True)
        else:
            df = df.reset_index()
        
        # 统一列名格式，适配原有代码
        if 'date' in df.columns:
            df.rename(columns={'date': 'dt'}, inplace=True)
        elif 'datetime' in df.columns:
            df.rename(columns={'datetime': 'dt'}, inplace=True)
        elif df.index.name == 'date':
            df['dt'] = df.index
            df = df.reset_index(drop=True)
        
        # 确保有volume列
        if 'vol' not in df.columns and 'volume' in df.columns:
            df.rename(columns={'volume': 'vol'}, inplace=True)
        elif 'vol' not in df.columns:
            df['vol'] = 100000  # 如果没有成交量数据，设为默认值
        
        # 确保日期格式正确
        if 'dt' in df.columns:
            df['dt'] = pd.to_datetime(df['dt'])
        else:
            # 如果没有dt列，尝试从索引获取
            if hasattr(df.index, 'to_pydatetime'):
                df['dt'] = pd.to_datetime(df.index)
            else:
                raise ValueError("无法找到日期列")
        
        # 按日期排序
        df = df.sort_values('dt').reset_index(drop=True)
        
        # 确保必要的列存在
        required_cols = ['dt', 'open', 'high', 'low', 'close', 'vol']
        for col in required_cols:
            if col not in df.columns:
                if col == 'vol':
                    df[col] = 100000  # 默认成交量
                else:
                    raise ValueError(f"缺少必要列: {col}")
        
        # 数据清洗：去除空值和异常值
        df = df.dropna(subset=['open', 'high', 'low', 'close'])
        df = df[df['open'] > 0]  # 去除价格为0的数据
        
        print(f"✅ 成功获取{len(df)}条{symbol}数据")
        print(f"📅 数据时间范围: {df['dt'].min()} 至 {df['dt'].max()}")
        
        return df[required_cols]
        
    except Exception as e:
        print(f"❌ 获取{symbol}数据失败: {e}")
        import traceback
        traceback.print_exc()
        raise Exception(f"无法获取股票{symbol}的数据: {e}")


class EnhancedBacktest:
    """增强版股票回测系统"""
    
    def __init__(self, symbol, initial_capital=100000, commission=0.0003, slippage=0.0001):
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.reset_portfolio()
        self.symbol = symbol
        
    def reset_portfolio(self):
        """重置投资组合"""
        self.cash = self.initial_capital
        self.position = 0
        self.trades = []
        self.portfolio_values = []
        self.signals_history = []
        
    def get_real_data(self, symbol, freq='D'):
        """获取真实股票数据"""
        print(f"📊 获取{symbol}真实数据 (频率:{freq})")

        symbol = self.symbol
        
        try:
            # 使用新的get_kline函数获取真实数据
            kline_data = get_kline(symbol, freq=freq)
            
            if kline_data is None or kline_data.empty:
                raise ValueError(f"获取的{symbol}数据为空")
            
            print(f"✅ 成功获取{len(kline_data)}条真实数据")
            
            # 转换数据格式
            df = kline_data.copy()
            df.rename(columns={
                'dt': 'date',
                'vol': 'volume'
            }, inplace=True)
            
            # 确保日期格式正确
            df['date'] = pd.to_datetime(df['date'])
            
            # 计算收益率
            df['returns'] = df['close'].pct_change()
            
            # 重置索引
            df.reset_index(drop=True, inplace=True)
            
            print(f"📈 数据范围: {df['date'].min()} 至 {df['date'].max()}")
            print(f"📈 价格范围: {df['close'].min():.3f} 至 {df['close'].max():.3f}")
            
            return df
                
        except Exception as e:
            print(f"❌ 获取真实数据失败: {e}")
            raise Exception(f"无法获取股票{symbol}的真实数据: {e}")
    

    
    def calculate_advanced_indicators(self, df):
        """计算高级技术指标"""
        print("📈 计算高级技术指标...")
        
        # 基础移动平均
        for period in [5, 10, 20, 60, 120]:
            df[f'ma{period}'] = df['close'].rolling(window=period).mean()
        
        # 指数移动平均
        for period in [12, 26]:
            df[f'ema{period}'] = df['close'].ewm(span=period).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD系统
        df['macd'] = df['ema12'] - df['ema26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # KDJ指标
        low_min = df['low'].rolling(window=9).min()
        high_max = df['high'].rolling(window=9).max()
        rsv = (df['close'] - low_min) / (high_max - low_min) * 100
        df['kdj_k'] = rsv.ewm(com=2).mean()
        df['kdj_d'] = df['kdj_k'].ewm(com=2).mean()
        df['kdj_j'] = 3 * df['kdj_k'] - 2 * df['kdj_d']
        
        # 成交量指标
        df['vol_ma5'] = df['volume'].rolling(window=5).mean()
        df['vol_ma20'] = df['volume'].rolling(window=20).mean()
        df['vol_ratio'] = df['volume'] / df['vol_ma20']
        
        # OBV (On Balance Volume)
        df['obv'] = (df['volume'] * np.sign(df['close'].diff())).cumsum()
        df['obv_ma'] = df['obv'].rolling(window=20).mean()
        
        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        df['atr'] = true_range.rolling(window=14).mean()
        
        # 价格动量
        df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['momentum_20'] = df['close'] / df['close'].shift(20) - 1
        
        return df
    
    def strategy_ma_crossover(self, df):
        """移动平均交叉策略"""
        signals = pd.Series(0, index=df.index)
        
        for i in range(1, len(df)):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            
            # 金叉买入
            if (current['ma5'] > current['ma20'] and 
                prev['ma5'] <= prev['ma20'] and
                current['volume'] > current['vol_ma20']):
                signals.iloc[i] = 1
            
            # 死叉卖出
            elif (current['ma5'] < current['ma20'] and 
                  prev['ma5'] >= prev['ma20']):
                signals.iloc[i] = -1
        
        return signals
    
    def strategy_macd_rsi(self, df):
        """MACD+RSI组合策略"""
        signals = pd.Series(0, index=df.index)
        
        for i in range(1, len(df)):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            
            # 买入条件：MACD金叉 + RSI超卖反弹
            macd_golden = (current['macd'] > current['macd_signal'] and 
                          prev['macd'] <= prev['macd_signal'])
            rsi_oversold = current['rsi'] < 30 and prev['rsi'] >= 30
            
            if macd_golden or rsi_oversold:
                signals.iloc[i] = 1
            
            # 卖出条件：MACD死叉 + RSI超买
            macd_death = (current['macd'] < current['macd_signal'] and 
                         prev['macd'] >= prev['macd_signal'])
            rsi_overbought = current['rsi'] > 70
            
            if macd_death or rsi_overbought:
                signals.iloc[i] = -1
        
        return signals
    
    def strategy_bollinger_kdj(self, df):
        """布林带+KDJ策略"""
        signals = pd.Series(0, index=df.index)
        
        for i in range(1, len(df)):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            
            # 买入：价格触及下轨反弹 + KDJ金叉
            bb_bounce = (current['close'] > current['bb_lower'] and 
                        prev['close'] <= prev['bb_lower'])
            kdj_golden = (current['kdj_k'] > current['kdj_d'] and 
                         prev['kdj_k'] <= prev['kdj_d'] and
                         current['kdj_k'] < 20)
            
            if bb_bounce or kdj_golden:
                signals.iloc[i] = 1
            
            # 卖出：价格触及上轨 + KDJ死叉
            bb_top = current['close'] >= current['bb_upper']
            kdj_death = (current['kdj_k'] < current['kdj_d'] and 
                        prev['kdj_k'] >= prev['kdj_d'] and
                        current['kdj_k'] > 80)
            
            if bb_top or kdj_death:
                signals.iloc[i] = -1
        
        return signals
    
    def strategy_momentum_volume(self, df):
        """动量+成交量策略"""
        signals = pd.Series(0, index=df.index)
        
        for i in range(20, len(df)):
            current = df.iloc[i]
            
            # 买入：正动量 + 成交量放大
            positive_momentum = (current['momentum_5'] > 0.02 and 
                               current['momentum_20'] > 0)
            volume_surge = current['vol_ratio'] > 1.5
            price_above_ma = current['close'] > current['ma20']
            
            if positive_momentum and volume_surge and price_above_ma:
                signals.iloc[i] = 1
            
            # 卖出：负动量 + 跌破支撑
            negative_momentum = current['momentum_5'] < -0.02
            break_support = current['close'] < current['ma20']
            
            if negative_momentum and break_support:
                signals.iloc[i] = -1
        
        return signals
    
    def run_strategy(self, df, strategy_name='ma_crossover'):
        """运行指定策略"""
        print(f"🎯 运行策略: {strategy_name}")
        
        strategy_map = {
            'ma_crossover': self.strategy_ma_crossover,
            'macd_rsi': self.strategy_macd_rsi,
            'bollinger_kdj': self.strategy_bollinger_kdj,
            'momentum_volume': self.strategy_momentum_volume
        }
        
        if strategy_name not in strategy_map:
            raise ValueError(f"未知策略: {strategy_name}")
        
        signals = strategy_map[strategy_name](df)
        df['signal'] = signals
        
        return df
    
    def execute_backtest_with_risk_management(self, df, max_position_pct=1.0, stop_loss_pct=0.05):
        """执行带风险管理的回测"""
        print("🚀 执行带风险管理的回测...")
        
        self.reset_portfolio()
        entry_price = 0
        
        for i, row in df.iterrows():
            current_price = row['close']
            signal = row['signal']
            
            # 风险管理：止损
            if self.position > 0 and entry_price > 0:
                if current_price <= entry_price * (1 - stop_loss_pct):
                    signal = -1  # 强制止损
                    print(f"⚠️ 止损触发: {row['date'].strftime('%Y-%m-%d')} 价格:{current_price:.3f}")
            
            # 计算当前组合价值
            portfolio_value = self.cash + self.position * current_price
            
            # 记录状态
            self.portfolio_values.append({
                'date': row['date'],
                'price': current_price,
                'cash': self.cash,
                'position': self.position,
                'portfolio_value': portfolio_value,
                'signal': signal,
                'returns': (portfolio_value / self.initial_capital - 1)
            })
            
            # 执行交易
            if signal == 1 and self.position == 0:  # 买入
                max_investment = self.cash * max_position_pct
                effective_price = current_price * (1 + self.commission + self.slippage)
                shares_to_buy = int(max_investment / effective_price)
                
                if shares_to_buy > 0:
                    cost = shares_to_buy * effective_price
                    self.cash -= cost
                    self.position = shares_to_buy
                    entry_price = current_price
                    
                    self.trades.append({
                        'date': row['date'],
                        'action': 'BUY',
                        'price': current_price,
                        'shares': shares_to_buy,
                        'cost': cost,
                        'portfolio_value': portfolio_value
                    })
            
            elif signal == -1 and self.position > 0:  # 卖出
                effective_price = current_price * (1 - self.commission - self.slippage)
                proceeds = self.position * effective_price
                
                # 计算盈亏
                profit = proceeds - (self.position * entry_price * (1 + self.commission + self.slippage))
                profit_pct = profit / (self.position * entry_price) if entry_price > 0 else 0
                
                self.cash += proceeds
                
                self.trades.append({
                    'date': row['date'],
                    'action': 'SELL',
                    'price': current_price,
                    'shares': self.position,
                    'proceeds': proceeds,
                    'profit': profit,
                    'profit_pct': profit_pct,
                    'portfolio_value': portfolio_value
                })
                
                self.position = 0
                entry_price = 0
        
        return pd.DataFrame(self.portfolio_values)
    
    def calculate_comprehensive_metrics(self, results_df, benchmark_df):
        """计算综合回测指标"""
        print("📊 计算综合回测指标...")
        
        # 策略收益率
        strategy_returns = results_df['returns'].diff().dropna()
        benchmark_returns = benchmark_df['close'].pct_change().dropna()
        
        # 对齐数据长度
        min_length = min(len(strategy_returns), len(benchmark_returns))
        strategy_returns = strategy_returns.iloc[-min_length:]
        benchmark_returns = benchmark_returns.iloc[-min_length:]
        
        # 基本收益指标
        total_return = results_df['returns'].iloc[-1]
        benchmark_total_return = (benchmark_df['close'].iloc[-1] / benchmark_df['close'].iloc[0]) - 1
        
        # 年化收益率
        trading_days = len(results_df)
        years = trading_days / 252
        annual_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
        benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1 if years > 0 else 0
        
        # 波动率
        volatility = strategy_returns.std() * np.sqrt(252)
        benchmark_volatility = benchmark_returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 最大回撤
        cumulative = results_df['portfolio_value'] / self.initial_capital
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdowns.min()
        
        # 卡尔马比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        # 胜率和盈亏比
        if self.trades:
            profitable_trades = [t for t in self.trades if t['action'] == 'SELL' and t.get('profit', 0) > 0]
            losing_trades = [t for t in self.trades if t['action'] == 'SELL' and t.get('profit', 0) < 0]
            
            win_rate = len(profitable_trades) / len([t for t in self.trades if t['action'] == 'SELL']) if len([t for t in self.trades if t['action'] == 'SELL']) > 0 else 0
            
            avg_win = np.mean([t['profit'] for t in profitable_trades]) if profitable_trades else 0
            avg_loss = np.mean([abs(t['profit']) for t in losing_trades]) if losing_trades else 0
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
        else:
            win_rate = 0
            profit_loss_ratio = 0
        
        # 信息比率
        excess_returns = strategy_returns - benchmark_returns
        information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'information_ratio': information_ratio,
            'total_trades': len(self.trades),
            'benchmark_total_return': benchmark_total_return,
            'benchmark_annual_return': benchmark_annual_return,
            'excess_return': total_return - benchmark_total_return
        }
        
        return metrics
    
    def generate_detailed_report(self, metrics, strategy_name):
        """生成详细报告"""
        report = {
            'strategy_name': strategy_name,
            'backtest_date': datetime.now().isoformat(),
            'initial_capital': self.initial_capital,
            'final_value': self.portfolio_values[-1]['portfolio_value'] if self.portfolio_values else self.initial_capital,
            'metrics': metrics,
            'trades_summary': {
                'total_trades': len(self.trades),
                'buy_trades': len([t for t in self.trades if t['action'] == 'BUY']),
                'sell_trades': len([t for t in self.trades if t['action'] == 'SELL']),
                'profitable_trades': len([t for t in self.trades if t['action'] == 'SELL' and t.get('profit', 0) > 0]),
                'losing_trades': len([t for t in self.trades if t['action'] == 'SELL' and t.get('profit', 0) < 0])
            }
        }
        
        return report
    
    def print_detailed_results(self, metrics, strategy_name):
        """打印详细结果"""
        print("\n" + "="*80)
        print(f"📈 增强版回测结果报告 - 策略: {strategy_name}")
        print("="*80)
        
        print(f"💰 资金情况:")
        print(f"   初始资金: {self.initial_capital:,.2f}")
        print(f"   最终价值: {self.portfolio_values[-1]['portfolio_value']:,.2f}")
        print(f"   绝对收益: {self.portfolio_values[-1]['portfolio_value'] - self.initial_capital:,.2f}")
        
        print(f"\n📊 收益指标:")
        print(f"   总收益率: {metrics['total_return']:.2%}")
        print(f"   年化收益率: {metrics['annual_return']:.2%}")
        print(f"   基准收益率: {metrics['benchmark_total_return']:.2%}")
        print(f"   超额收益: {metrics['excess_return']:.2%}")
        print(f"   信息比率: {metrics['information_ratio']:.3f}")
        
        print(f"\n📈 风险指标:")
        print(f"   年化波动率: {metrics['volatility']:.2%}")
        print(f"   夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"   最大回撤: {metrics['max_drawdown']:.2%}")
        print(f"   卡尔马比率: {metrics['calmar_ratio']:.3f}")
        
        print(f"\n💼 交易统计:")
        print(f"   总交易次数: {metrics['total_trades']}")
        print(f"   交易胜率: {metrics['win_rate']:.2%}")
        print(f"   盈亏比: {metrics['profit_loss_ratio']:.2f}")
        
        if self.trades:
            print(f"\n📋 交易明细 (最近10笔):")
            for trade in self.trades[-10:]:
                action_symbol = "📈" if trade['action'] == 'BUY' else "📉"
                profit_info = ""
                if trade['action'] == 'SELL' and 'profit' in trade:
                    profit_symbol = "💚" if trade['profit'] > 0 else "❤️"
                    profit_info = f" {profit_symbol} {trade['profit']:.2f} ({trade['profit_pct']:.2%})"
                
                print(f"   {trade['date'].strftime('%Y-%m-%d')} {action_symbol} {trade['action']} "
                      f"价格:{trade['price']:.3f} 数量:{trade.get('shares', 0):,}{profit_info}")

def run_multiple_strategies(symbol):
    """运行多种策略对比"""
    print("🎯 开始多策略回测对比")
    print("="*80)
    
    strategies = ['ma_crossover', 'macd_rsi', 'bollinger_kdj', 'momentum_volume']
    results_summary = []
    
    for strategy_name in strategies:
        print(f"\n🔄 测试策略: {strategy_name}")
        print("-" * 60)
        
        try:
            # 创建回测实例
            backtest = EnhancedBacktest(symbol, initial_capital=100000)
            
            # 获取真实数据
            df = backtest.get_real_data(symbol, freq='D')
            
            # 计算指标
            df = backtest.calculate_advanced_indicators(df)
            
            # 运行策略
            df = backtest.run_strategy(df, strategy_name)
            
            # 执行回测
            results_df = backtest.execute_backtest_with_risk_management(df, max_position_pct=0.95, stop_loss_pct=0.08)
            
            # 计算指标
            metrics = backtest.calculate_comprehensive_metrics(results_df, df)
            
            # 打印结果
            backtest.print_detailed_results(metrics, strategy_name)
            
            # 生成报告（不单独保存每个策略的详细文件）
            report = backtest.generate_detailed_report(metrics, strategy_name)
            
            # 记录摘要
            results_summary.append({
                'strategy': strategy_name,
                'total_return': metrics['total_return'],
                'annual_return': metrics['annual_return'],
                'sharpe_ratio': metrics['sharpe_ratio'],
                'max_drawdown': metrics['max_drawdown'],
                'win_rate': metrics['win_rate'],
                'total_trades': metrics['total_trades']
            })
            
        except Exception as e:
            print(f"❌ 策略 {strategy_name} 执行失败: {e}")
            continue
    
    # 策略对比总结
    if results_summary:
        print("\n" + "="*80)
        print("📊 策略对比总结")
        print("="*80)
        
        summary_df = pd.DataFrame(results_summary)
        print(summary_df.round(4))
        
        # 找出最佳策略
        best_sharpe = summary_df.loc[summary_df['sharpe_ratio'].idxmax()]
        best_return = summary_df.loc[summary_df['total_return'].idxmax()]
        
        print(f"\n🏆 最佳夏普比率策略: {best_sharpe['strategy']} (夏普比率: {best_sharpe['sharpe_ratio']:.3f})")
        print(f"🏆 最佳收益率策略: {best_return['strategy']} (总收益率: {best_return['total_return']:.2%})")
        
        # 生成综合报告
        comprehensive_report = {
            'backtest_date': datetime.now().isoformat(),
            'symbol': symbol,
            'summary': summary_df.to_dict('records'),
            'best_strategies': {
                'best_sharpe': best_sharpe.to_dict(),
                'best_return': best_return.to_dict()
            }
        }
        
        # 只保存一个综合报告文件
        with open('backtest_comprehensive_report.json', 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 综合报告已保存: backtest_comprehensive_report.json")

def main():
    """主函数"""
    print("🎯 开始增强版股票回测系统")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        run_multiple_strategies('518880')
        print(f"\n✅ 所有策略回测完成! 用时: {datetime.now().strftime('%H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 回测系统执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
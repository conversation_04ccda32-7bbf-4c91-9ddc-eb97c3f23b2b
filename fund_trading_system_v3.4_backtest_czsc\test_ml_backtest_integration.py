#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML增强回测集成测试脚本
验证机器学习回测系统的完整功能
"""

import sys
import os
import traceback
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_ml_backtest_imports():
    """测试ML回测模块导入"""
    print("🔍 测试ML回测模块导入...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        print("   ✅ MLEnhancedBacktestEngine 导入成功")
        
        from backtest.enhanced_feature_engineer import EnhancedFeatureEngineer
        print("   ✅ EnhancedFeatureEngineer 导入成功")
        
        from backtest.ml_model_manager import MLModelManager
        print("   ✅ MLModelManager 导入成功")
        
        from backtest.ml_backtest_analyzer import MLBacktestAnalyzer
        print("   ✅ MLBacktestAnalyzer 导入成功")
        
        from config.ml_backtest_config import MLBacktestConfig
        print("   ✅ MLBacktestConfig 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False


def test_ml_config_functionality():
    """测试ML配置功能"""
    print("\n🔧 测试ML配置功能...")
    
    try:
        from config.ml_backtest_config import MLBacktestConfig
        
        # 测试默认配置
        config = MLBacktestConfig.create_default_config()
        print("   ✅ 默认配置创建成功")
        
        # 测试保守配置
        conservative_config = MLBacktestConfig.create_conservative_config()
        print("   ✅ 保守配置创建成功")
        
        # 测试激进配置
        aggressive_config = MLBacktestConfig.create_aggressive_config()
        print("   ✅ 激进配置创建成功")
        
        # 测试配置验证
        validation = config.validate_config()
        print(f"   ✅ 配置验证: {'通过' if validation['valid'] else '失败'}")
        
        # 测试配置转换
        advanced_config = config.get_advanced_backtest_config()
        print("   ✅ 高级回测配置转换成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置测试失败: {e}")
        return False


def test_feature_engineering():
    """测试特征工程功能"""
    print("\n🔬 测试特征工程功能...")
    
    try:
        import pandas as pd
        import numpy as np
        from backtest.enhanced_feature_engineer import EnhancedFeatureEngineer
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        data = pd.DataFrame({
            'dt': dates,
            'open': np.random.uniform(95, 105, 100),
            'high': np.random.uniform(100, 110, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(95, 105, 100),
            'volume': np.random.randint(100000, 1000000, 100)
        })
        
        # 创建特征工程器
        feature_engineer = EnhancedFeatureEngineer()
        
        # 提取特征
        features_df = feature_engineer.extract_all_features(data)
        print(f"   ✅ 特征提取成功: {len(data.columns)} -> {len(features_df.columns)} 特征")
        
        # 特征选择
        target = features_df['close'].pct_change().fillna(0)
        selected_features = feature_engineer.select_features(
            features_df, target, method='correlation', n_features=10
        )
        print(f"   ✅ 特征选择成功: 选择了 {len(selected_features)} 个特征")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 特征工程测试失败: {e}")
        traceback.print_exc()
        return False


def test_ml_model_manager():
    """测试ML模型管理器"""
    print("\n🤖 测试ML模型管理器...")
    
    try:
        import numpy as np
        from backtest.ml_model_manager import MLModelManager
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.randn(200, 10)
        y = np.random.randn(200)
        
        # 创建模型管理器
        model_manager = MLModelManager()
        
        # 训练模型
        results = model_manager.train_all_models(X, y)
        print(f"   ✅ 模型训练完成: {len(results)} 个模型")
        
        # 检查最佳模型
        if model_manager.best_model:
            print(f"   ✅ 最佳模型: {model_manager.best_model}")
        
        # 测试预测
        if model_manager.best_model and model_manager.best_model in model_manager.models:
            predictions = model_manager.predict(X[:10])
            print(f"   ✅ 预测成功: {len(predictions)} 个预测值")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模型管理器测试失败: {e}")
        traceback.print_exc()
        return False


def test_ml_enhanced_backtest_engine():
    """测试ML增强回测引擎"""
    print("\n🚀 测试ML增强回测引擎...")
    
    try:
        import pandas as pd
        import numpy as np
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=200, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(199):
            change = np.random.normal(0, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        data = pd.DataFrame({
            'dt': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 200)
        })
        
        # 创建ML回测引擎
        ml_config = {
            'model_type': 'random_forest',
            'sequence_length': 20,
            'min_train_samples': 100,
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True
            }
        }
        
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=ml_config
        )
        
        # 运行ML回测
        results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)
        print("   ✅ ML回测执行成功")
        
        # 检查结果
        if 'total_return' in results:
            print(f"   ✅ 总收益率: {results['total_return']:.2%}")
        
        if 'ml_metrics' in results:
            ml_metrics = results['ml_metrics']
            print(f"   ✅ ML指标: {ml_metrics.get('total_predictions', 0)} 个预测")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ML回测引擎测试失败: {e}")
        traceback.print_exc()
        return False


def test_advanced_backtest_system():
    """测试高级回测系统"""
    print("\n🏗️ 测试高级回测系统...")
    
    try:
        import pandas as pd
        import numpy as np
        from backtest.advanced_backtest_system import AdvancedBacktestSystem
        from config.ml_backtest_config import MLBacktestConfig
        
        # 创建配置
        ml_config = MLBacktestConfig.create_default_config()
        backtest_config = ml_config.get_advanced_backtest_config()
        
        # 创建高级回测系统
        backtest_system = AdvancedBacktestSystem(config=backtest_config)
        print("   ✅ 高级回测系统初始化成功")
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=150, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(149):
            change = np.random.normal(0.001, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        data = pd.DataFrame({
            'dt': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 150)
        })
        
        # 运行ML增强回测
        ml_results = backtest_system.run_ml_enhanced_backtest(data, train_model=True)
        print("   ✅ ML增强回测完成")
        
        # 检查结果
        if 'total_return' in ml_results:
            print(f"   ✅ 回测收益: {ml_results['total_return']:.2%}")
        
        if 'analysis' in ml_results:
            print("   ✅ 结果分析完成")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 高级回测系统测试失败: {e}")
        traceback.print_exc()
        return False


def test_system_integration():
    """测试系统集成"""
    print("\n🔗 测试系统集成...")
    
    try:
        from system.enhanced_trading_system import EnhancedFundTradingSystemV3
        
        # 创建交易系统
        trading_system = EnhancedFundTradingSystemV3()
        print("   ✅ 交易系统初始化成功")
        
        # 检查ML回测可用性
        if hasattr(trading_system, 'ml_backtest_available'):
            print(f"   ✅ ML回测可用性: {trading_system.ml_backtest_available}")
        
        # 如果ML回测可用，测试运行
        if getattr(trading_system, 'ml_backtest_available', False):
            # 运行简单的ML回测
            results = trading_system.run_ml_enhanced_backtest(
                fund_codes=['518880'], days=100, config_type='default'
            )
            
            if 'error' not in results:
                print("   ✅ ML回测集成测试成功")
            else:
                print(f"   ⚠️ ML回测返回错误: {results.get('error')}")
        else:
            print("   ⚠️ ML回测功能不可用，跳过集成测试")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 系统集成测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🎯 ML增强回测系统集成测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("ML回测模块导入", test_ml_backtest_imports),
        ("ML配置功能", test_ml_config_functionality),
        ("特征工程", test_feature_engineering),
        ("ML模型管理器", test_ml_model_manager),
        ("ML增强回测引擎", test_ml_enhanced_backtest_engine),
        ("高级回测系统", test_advanced_backtest_system),
        ("系统集成", test_system_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 ML增强回测系统测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed >= total * 0.8:
        print("\n🎉 ML增强回测系统集成测试基本通过！")
        print("\n💡 系统特性:")
        print("   ✅ ML增强回测引擎")
        print("   ✅ 自动特征工程")
        print("   ✅ 多模型训练和比较")
        print("   ✅ 智能信号生成")
        print("   ✅ 详细性能分析")
        print("   ✅ 传统vs ML比较")
        
        print("\n🚀 使用建议:")
        print("   - 运行 examples/ml_backtest_demo.py 查看完整演示")
        print("   - 使用不同配置类型优化性能")
        print("   - 调整模型参数以适应特定市场")
    else:
        print("\n⚠️ 部分测试失败，请检查依赖和配置")
        print("💡 可能的解决方案:")
        print("   - 安装缺失的依赖包 (sklearn, torch, tensorflow)")
        print("   - 检查数据格式和路径")
        print("   - 验证配置文件设置")
    
    return passed >= total * 0.8


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
简化版回测集成测试
验证回测功能的基本集成
"""

import sys
import os
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_backtest_engine_basic():
    """测试基础回测引擎功能"""
    print("🔧 测试基础回测引擎...")
    
    try:
        # 直接导入回测引擎
        sys.path.insert(0, current_dir)
        from backtest.backtest_engine import BacktestEngine
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        print("   ✅ BacktestEngine 创建成功")
        
        # 测试基本属性
        assert engine.initial_capital == 100000
        assert hasattr(engine, 'transaction_cost')
        assert hasattr(engine, 'backtest_results')
        print("   ✅ BacktestEngine 属性验证通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 回测引擎测试失败: {e}")
        traceback.print_exc()
        return False

def test_backtest_config():
    """测试回测配置"""
    print("⚙️ 测试回测配置...")
    
    try:
        from config.backtest_integration_config import get_backtest_config, BacktestMode
        
        # 测试默认配置
        config = get_backtest_config('default')
        print("   ✅ 默认配置创建成功")
        
        # 测试保守配置
        conservative_config = get_backtest_config('conservative')
        print("   ✅ 保守配置创建成功")
        
        # 测试配置参数
        assert config.should_enable_backtest() == True
        print("   ✅ 配置参数验证通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 回测配置测试失败: {e}")
        traceback.print_exc()
        return False

def test_signal_validator_creation():
    """测试信号验证器创建"""
    print("🔍 测试信号验证器创建...")
    
    try:
        from analyzers.backtest_signal_validator import BacktestSignalValidator
        
        # 创建验证器
        validator = BacktestSignalValidator()
        print("   ✅ BacktestSignalValidator 创建成功")
        
        # 检查基本属性
        assert hasattr(validator, 'backtest_engine')
        assert hasattr(validator, 'config_manager')
        print("   ✅ 验证器属性检查通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 信号验证器测试失败: {e}")
        traceback.print_exc()
        return False

def test_mock_signal_validation():
    """测试模拟信号验证"""
    print("📊 测试模拟信号验证...")
    
    try:
        from analyzers.backtest_signal_validator import BacktestSignalValidator
        
        validator = BacktestSignalValidator()
        
        # 创建测试信号
        test_signal = {
            'fund_code': '513500',
            'decision': 'buy',
            'confidence': 0.7,
            'strength': 0.6,
            'signals': ['测试信号']
        }
        
        # 执行验证（可能会因为数据获取失败而返回默认结果）
        result = validator.validate_signal('513500', test_signal)
        
        # 验证结果结构
        assert hasattr(result, 'signal_name')
        assert hasattr(result, 'quality_score')
        assert hasattr(result, 'recommendation')
        print(f"   ✅ 信号验证完成: 质量评分={result.quality_score:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模拟信号验证失败: {e}")
        traceback.print_exc()
        return False

def test_main_system_import():
    """测试主系统导入"""
    print("🚀 测试主系统导入...")
    
    try:
        # 测试能否成功导入主系统
        from system.enhanced_trading_system import EnhancedFundTradingSystemV3
        print("   ✅ 主系统导入成功")
        
        # 创建系统实例
        system = EnhancedFundTradingSystemV3()
        print("   ✅ 主系统创建成功")
        
        # 检查协调器是否包含回测验证器
        if hasattr(system.coordinator, 'backtest_validator'):
            print("   ✅ 回测验证器已集成到协调器")
            return True
        else:
            print("   ⚠️ 回测验证器未集成到协调器")
            return False
        
    except Exception as e:
        print(f"   ❌ 主系统导入失败: {e}")
        traceback.print_exc()
        return False

def run_simple_integration_tests():
    """运行简化版集成测试"""
    print("🚀 开始简化版回测集成测试")
    print("=" * 50)
    
    tests = [
        ("回测引擎基础测试", test_backtest_engine_basic),
        ("回测配置测试", test_backtest_config),
        ("信号验证器创建测试", test_signal_validator_creation),
        ("模拟信号验证测试", test_mock_signal_validation),
        ("主系统导入测试", test_main_system_import),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"💥 {test_name} - 异常: {str(e)}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = len([r for r in results if r[1]])
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} - {test_name}")
    
    print(f"\n🏁 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 3:  # 至少3个测试通过就认为基本集成成功
        print("🎉 回测功能基本集成成功！")
        print("💡 系统现在具备以下能力:")
        print("   • 回测引擎可以正常工作")
        print("   • 配置管理系统可用")
        print("   • 信号验证器可以创建")
        if passed == total:
            print("   • 完全集成到主交易系统")
        return True
    else:
        print("⚠️ 回测集成存在问题，需要进一步调试")
        return False

if __name__ == '__main__':
    """简化版回测集成测试主程序"""
    success = run_simple_integration_tests()
    
    if success:
        print(f"\n🚀 回测集成基本验证完成！")
        print("💰 可以尝试运行 main.py 测试完整功能")
        print("📝 如果遇到问题，请检查具体的错误信息")
    else:
        print(f"\n🔧 需要修复基础集成问题")
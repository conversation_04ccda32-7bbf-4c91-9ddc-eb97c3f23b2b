#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试LLM分析问题
检查数据传递和响应情况
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def debug_llm_data_preparation():
    """调试LLM数据准备过程"""
    print("🔍 调试LLM数据准备过程...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        
        coordinator = MultiAgentCoordinatorV3()
        fund_code = '518880'
        
        print(f"📊 分析基金: {fund_code}")
        
        # 获取传统智能体数据
        print("\n1️⃣ 获取传统智能体数据...")
        technical_data = coordinator.technical_agent.process({'fund_code': fund_code})
        gua_data = coordinator.gua_agent.process({'fund_code': fund_code})
        flow_data = coordinator.flow_agent.process({'fund_code': fund_code})
        
        print(f"✅ 技术分析数据: {len(str(technical_data))} 字符")
        print(f"✅ 卦象分析数据: {len(str(gua_data))} 字符")
        print(f"✅ 资金流向数据: {len(str(flow_data))} 字符")
        
        # 整合数据
        print("\n2️⃣ 整合数据...")
        integrated_data = coordinator._integrate_agent_data(fund_code, technical_data, gua_data, flow_data)
        print(f"✅ 整合数据: {len(str(integrated_data))} 字符")
        
        # 获取CZSC结构数据
        print("\n3️⃣ 获取CZSC结构数据...")
        czsc_structure_data = coordinator._extract_czsc_structure_data(fund_code, integrated_data)
        print(f"✅ CZSC结构数据: {len(str(czsc_structure_data))} 字符")
        
        # 准备LLM分析数据
        print("\n4️⃣ 准备LLM分析数据...")
        market_data = {
            'fund_code': fund_code,
            'price_data': integrated_data.get('price_data', {}),
            'technical_analysis': integrated_data.get('technical_analysis', {}),
            'gua_data': integrated_data.get('gua_data', {}),
            'flow_data': integrated_data.get('flow_data', {}),
            'volume_analysis': integrated_data.get('volume_analysis', {}),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        if czsc_structure_data:
            market_data['czsc_structure'] = czsc_structure_data
        
        print(f"✅ LLM输入数据: {len(str(market_data))} 字符")
        
        # 显示数据结构
        print("\n📊 数据结构详情:")
        for key, value in market_data.items():
            if isinstance(value, dict):
                print(f"   {key}: {len(value)} 个字段")
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, (dict, list)):
                        print(f"     - {sub_key}: {len(sub_value)} 项")
                    else:
                        print(f"     - {sub_key}: {type(sub_value).__name__}")
            else:
                print(f"   {key}: {type(value).__name__}")
        
        return market_data
        
    except Exception as e:
        print(f"❌ 数据准备调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_llm_prompt_generation():
    """调试LLM提示词生成"""
    print("\n🔍 调试LLM提示词生成...")
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        analyzer = LLMMarketAnalyzer()
        
        # 获取市场数据
        market_data = debug_llm_data_preparation()
        if not market_data:
            return False
        
        # 生成提示词
        print("\n5️⃣ 生成分析提示词...")
        prompt = analyzer._build_analysis_prompt(market_data, '518880')
        
        print(f"✅ 提示词长度: {len(prompt)} 字符")
        print(f"📝 提示词内容预览:")
        print("="*80)
        print(prompt[:1000] + "..." if len(prompt) > 1000 else prompt)
        print("="*80)
        
        # 检查提示词中的关键信息
        key_sections = [
            "技术指标分析",
            "CZSC缠论",
            "卦象分析", 
            "资金流向",
            "分型(FX)",
            "笔(BI)",
            "线段(XD)"
        ]
        
        print("\n📊 提示词内容分析:")
        for section in key_sections:
            if section in prompt:
                print(f"   ✅ 包含 {section}")
            else:
                print(f"   ❌ 缺少 {section}")
        
        return prompt
        
    except Exception as e:
        print(f"❌ 提示词生成调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_llm_api_call():
    """调试LLM API调用"""
    print("\n🔍 调试LLM API调用...")
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        analyzer = LLMMarketAnalyzer()
        
        if not analyzer.client:
            print("❌ LLM客户端未初始化")
            return False
        
        # 获取市场数据
        market_data = debug_llm_data_preparation()
        if not market_data:
            return False
        
        print("\n6️⃣ 执行LLM分析...")
        
        # 直接调用分析方法
        result = analyzer.analyze_market_narrative(market_data, '518880')
        
        print(f"✅ LLM分析完成")
        print(f"📊 响应数据: {len(str(result))} 字符")
        
        # 显示分析结果
        print("\n📊 LLM分析结果:")
        print("="*80)
        
        if 'error' in result:
            print(f"❌ 分析错误: {result['error']}")
            if 'raw_response' in result:
                print(f"📝 原始响应: {result['raw_response'][:500]}...")
        else:
            print(f"🤖 市场情绪: {result.get('market_sentiment', 'N/A')}")
            print(f"🎯 置信度: {result.get('confidence_level', 0):.2f}")
            
            market_drivers = result.get('market_drivers', [])
            if market_drivers:
                print(f"📈 市场驱动因素:")
                for i, driver in enumerate(market_drivers[:3], 1):
                    print(f"   {i}. {driver}")
            
            strategy = result.get('strategy_suggestion', '')
            if strategy:
                print(f"💡 策略建议: {strategy[:200]}...")
            
            key_insights = result.get('key_insights', '')
            if key_insights:
                print(f"🔍 核心洞察: {key_insights[:200]}...")
        
        print("="*80)
        
        return result
        
    except Exception as e:
        print(f"❌ LLM API调用调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_llm_response_parsing():
    """调试LLM响应解析"""
    print("\n🔍 调试LLM响应解析...")
    
    # 模拟一个LLM响应
    mock_response = """{
    "market_drivers": ["CZSC缠论技术指标显示MA5(7.4138)高于MA20(7.3899)形成短期多头排列", "MACD负值收窄(-0.0034>-0.0050)显示下跌动能衰竭"],
    "risk_points": ["成交量萎缩显示市场参与度不高", "技术指标虽然改善但力度有限"],
    "opportunities": ["短期技术面有所改善", "CZSC结构显示可能的反弹机会"],
    "strategy_suggestion": "基于CZSC缠论分析，建议谨慎乐观，可适量关注但需控制仓位",
    "confidence_level": 0.65,
    "market_sentiment": "谨慎",
    "key_insights": "当前处于技术面修复阶段，但需要成交量配合确认"
}"""
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        analyzer = LLMMarketAnalyzer()
        
        print("7️⃣ 测试响应解析...")
        
        # 测试解析
        parsed_result = analyzer._parse_llm_response(mock_response, {})
        
        print(f"✅ 解析成功")
        print(f"📊 解析结果:")
        
        for key, value in parsed_result.items():
            if key == 'raw_response':
                continue
            if isinstance(value, list):
                print(f"   {key}: {len(value)} 项")
                for item in value[:2]:
                    print(f"     - {item}")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 响应解析调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🎯 LLM分析调试")
    print("="*80)
    print(f"⏰ 调试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    debug_steps = [
        ("数据准备调试", debug_llm_data_preparation),
        ("提示词生成调试", debug_llm_prompt_generation),
        ("API调用调试", debug_llm_api_call),
        ("响应解析调试", debug_llm_response_parsing),
    ]
    
    results = []
    
    for step_name, debug_func in debug_steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = debug_func()
            success = result is not None and result is not False
            results.append((step_name, success))
            
            if not success:
                print(f"⚠️ {step_name}未完全成功，但继续下一步...")
                
        except Exception as e:
            print(f"❌ {step_name}执行失败: {e}")
            results.append((step_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 LLM分析调试结果汇总")
    print("="*80)
    
    for step_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {step_name}: {status}")
    
    print(f"\n⏰ 调试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析可能的问题
    print("\n💡 可能的问题分析:")
    print("1. 如果数据准备失败 -> 检查智能体数据获取")
    print("2. 如果提示词生成失败 -> 检查数据结构匹配")
    print("3. 如果API调用失败 -> 检查网络和API配置")
    print("4. 如果响应解析失败 -> 检查JSON格式和字段匹配")
    print("\n🔧 优化建议:")
    print("- 增加提示词中的具体数据量")
    print("- 调整temperature参数获得更详细的响应")
    print("- 增加max_tokens限制以获得更长的分析")

if __name__ == "__main__":
    main()
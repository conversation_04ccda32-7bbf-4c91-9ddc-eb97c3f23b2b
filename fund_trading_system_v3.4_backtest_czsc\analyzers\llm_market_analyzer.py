"""
LLM驱动的市场分析器
基于大语言模型进行市场叙事和情绪分析
"""

import logging
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import json
from dotenv import load_dotenv
import os
load_dotenv("file.env")

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入kimi AI客户端
from pathlib import Path
from openai import OpenAI

from core.data_structures import DimensionEvaluationResult


class LLMMarketAnalyzer:
    """
    @class LLMMarketAnalyzer
    @brief LLM驱动的市场分析器
    @details 使用大语言模型分析市场叙事和情绪，提供智能决策建议
    """
    
    def __init__(self, config=None):
        self.name = "LLMMarketAnalyzer"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 导入配置管理
        try:
            from config.ai_config import get_ai_config
            self.ai_config = config or get_ai_config()
        except ImportError:
            self.logger.warning("无法导入AI配置，使用默认设置")
            self.ai_config = None
        
        # 初始化LLM客户端
        self.client = self._initialize_llm_client()
    
    def _initialize_llm_client(self):
        """初始化LLM客户端"""
        try:
            if self.ai_config:
                # 使用配置管理
                provider_config = self.ai_config.get_llm_config("moonshot")
                if provider_config and provider_config.get("enabled", False):
                    api_key = provider_config.get("api_key", "")
                    base_url = provider_config.get("base_url", "https://api.moonshot.cn/v1")
                    
                    if api_key:
                        client = OpenAI(api_key=api_key, base_url=base_url)
                        self.logger.info("LLM客户端初始化成功（使用配置管理）")
                        return client
                    else:
                        self.logger.warning("API密钥未配置")
                else:
                    self.logger.warning("Moonshot提供商未启用或配置不存在")
            
            # 兜底：尝试使用环境变量
            api_key = os.getenv("MOONSHOT_API_KEY")
            if api_key:
                client = OpenAI(
                    api_key=api_key,
                    base_url="https://api.moonshot.cn/v1"
                )
                self.logger.info("LLM客户端初始化成功（使用环境变量）")
                return client
            else:
                self.logger.warning("未找到MOONSHOT_API_KEY环境变量")
            
            return None
            
        except Exception as e:
            self.logger.error(f"LLM客户端初始化失败: {str(e)}")
            return None
    
    def analyze_market_narrative(self, market_data: Dict[str, Any], 
                               fund_code: str = None, max_retries: int = 2) -> Dict[str, Any]:
        """
        @brief 分析市场叙事和情绪
        @param market_data: 市场数据，包含技术指标、资金流向等
        @param fund_code: 基金代码
        @param max_retries: 最大重试次数
        @return: 市场分析结果
        """
        if not self.client:
            return self._get_fallback_analysis(market_data)
        
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                # 构建分析提示词
                prompt = self._build_analysis_prompt(market_data, fund_code)
                
                # 调用LLM进行分析
                messages = [
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ]
                
                # 根据重试次数调整参数
                temperature = 0.3 if attempt == 0 else 0.1  # 重试时降低随机性
                
                completion = self.client.chat.completions.create(
                    model="kimi-k2-0711-preview",
                    messages=messages,
                    temperature=temperature,
                    max_tokens=3000,  # 增加最大token数以获得更详细的分析
                )
                
                # 解析LLM响应
                llm_response = completion.choices[0].message.content
                analysis_result = self._parse_llm_response(llm_response, market_data)
                
                # 检查是否有解析错误
                if 'error' not in analysis_result or analysis_result.get('confidence_level', 0) > 0.3:
                    self.logger.info(f"LLM市场分析完成，基金代码: {fund_code}, 尝试次数: {attempt + 1}")
                    return analysis_result
                else:
                    # 如果解析有错误但还有重试机会，继续重试
                    if attempt < max_retries:
                        self.logger.warning(f"LLM分析结果质量较低，准备重试 (尝试 {attempt + 1}/{max_retries + 1})")
                        last_error = analysis_result.get('error', '解析质量较低')
                        time.sleep(1)  # 短暂延迟后重试
                        continue
                    else:
                        return analysis_result
                
            except json.JSONDecodeError as e:
                last_error = f"JSON解析失败: {str(e)}"
                self.logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(1)
                    continue
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(2)  # API错误时延迟更长时间
                    continue
        
        # 所有重试都失败了
        self.logger.error(f"LLM市场分析失败，已重试{max_retries}次: {last_error}")
        return self._get_fallback_analysis(market_data, error=last_error)
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的量化投资分析师，擅长分析基于CZSC缠论技术分析的市场数据并提供投资建议。

你将接收到以下类型的分析数据：
1. CZSC缠论技术指标分析 - 包括移动平均线、震荡指标、波动性指标等
2. CZSC缠论卦象分析 - 基于价格行为的卦象模式识别
3. 资金流向分析 - 基于实时交易数据的流动性分析
4. 六大维度评估 - 多维度综合评估结果

请基于这些CZSC缠论分析数据，从以下角度进行分析：
1. 市场主要驱动因素分析（明确提及CZSC技术指标的作用）
2. 潜在风险点识别（结合卦象分析结果）
3. 投资机会评估（基于缠论信号）
4. 操作策略建议（融合传统技术分析和缠论理念）

要求：
- 分析要客观理性，基于CZSC缠论数据说话
- 在分析中明确提及使用了CZSC缠论技术分析
- 风险提示要明确具体，结合卦象分析
- 建议要可操作性强，体现缠论思维
- 回答要结构化，便于程序解析

请严格按照以下JSON格式返回分析结果，注意字符串内容不要包含双引号：
{
    "market_drivers": ["基于CZSC技术指标的驱动因素1", "驱动因素2"],
    "risk_points": ["结合卦象分析的风险点1", "风险点2"],
    "opportunities": ["基于缠论信号的机会1", "机会2"],
    "strategy_suggestion": "融合CZSC缠论理念的具体操作建议",
    "confidence_level": 0.8,
    "market_sentiment": "积极/中性/谨慎",
    "key_insights": "基于CZSC缠论分析的核心洞察"
}

重要提醒：
1. 所有字符串内容请使用单引号或避免使用引号
2. 数组和字符串之间必须用逗号分隔
3. 最后一个字段后不要添加逗号
4. confidence_level必须是0-1之间的数字"""
    
    def _build_analysis_prompt(self, market_data: Dict[str, Any], fund_code: str = None) -> str:
        """构建分析提示词"""
        prompt = f"""请分析以下基金的市场情况：

基金代码: {fund_code or '未指定'}
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # 检查数据可用性
        has_real_data = False
        
        # 添加技术分析数据
        technical_analysis = market_data.get('technical_analysis', {})
        if technical_analysis and 'error' not in technical_analysis:
            indicators = technical_analysis.get('indicators', {})
            if indicators:
                prompt += "技术指标分析 (基于CZSC缠论技术分析):\n"
                
                # 分类显示技术指标
                ma_indicators = {k: v for k, v in indicators.items() if 'ma' in k.lower()}
                oscillator_indicators = {k: v for k, v in indicators.items() if k.lower() in ['rsi', 'macd', 'macd_signal']}
                volatility_indicators = {k: v for k, v in indicators.items() if 'bb' in k.lower() or 'atr' in k.lower()}
                volume_indicators = {k: v for k, v in indicators.items() if 'volume' in k.lower()}
                
                if ma_indicators:
                    prompt += "  移动平均线指标:\n"
                    for indicator, value in ma_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator.upper()}: {value:.4f}\n"
                            has_real_data = True
                
                if oscillator_indicators:
                    prompt += "  震荡指标:\n"
                    for indicator, value in oscillator_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator.upper()}: {value:.4f}\n"
                            has_real_data = True
                
                if volatility_indicators:
                    prompt += "  波动性指标:\n"
                    for indicator, value in volatility_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator}: {value:.4f}\n"
                            has_real_data = True
                
                if volume_indicators:
                    prompt += "  成交量指标:\n"
                    for indicator, value in volume_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator}: {value:.4f}\n"
                            has_real_data = True
                
                prompt += "\n"
        
        # 添加卦象分析数据 (基于CZSC缠论)
        gua_data = market_data.get('gua_data', {})
        if gua_data and 'error' not in gua_data:
            gua_score = gua_data.get('gua_score', 0)
            main_gua = gua_data.get('main_gua', '')
            gua_pattern = gua_data.get('gua_pattern', '')
            
            prompt += f"CZSC缠论卦象分析:\n"
            prompt += f"- 卦象评分: {gua_score:.3f}\n"
            if main_gua:
                prompt += f"- 主卦: {main_gua}\n"
            if gua_pattern:
                prompt += f"- 卦象模式: {gua_pattern}\n"
            prompt += f"- 是否选择卦: {gua_data.get('is_select_gua', False)}\n"
            prompt += f"- 买入信号: {gua_data.get('is_buy_gua', False)}\n"
            prompt += f"- 卖出信号: {gua_data.get('is_sell_gua', False)}\n"
            
            # 添加卦象解释
            gua_interpretation = gua_data.get('gua_interpretation', '')
            if gua_interpretation:
                prompt += f"- 卦象解释: {gua_interpretation}\n"
            
            prompt += "\n"
            has_real_data = True
        
        # 添加资金流向数据
        flow_data = market_data.get('flow_data', {})
        if flow_data and 'error' not in flow_data:
            liquidity_score = flow_data.get('liquidity_score', 0)
            if liquidity_score > 0:
                prompt += f"资金流向分析:\n"
                prompt += f"- 流动性评分: {liquidity_score:.3f}\n"
                prompt += f"- 高流动性: {flow_data.get('high_liquidity', False)}\n\n"
                has_real_data = True
        
        # 添加价格数据（如果有的话）
        price_data = market_data.get('price_data', {})
        if price_data and any(v not in [None, 'N/A', ''] for v in price_data.values()):
            prompt += f"价格数据:\n"
            current_price = price_data.get('current_price')
            change_pct = price_data.get('change_pct')
            volume = price_data.get('volume')
            
            if current_price not in [None, 'N/A', '']:
                prompt += f"- 当前价格: {current_price}\n"
                has_real_data = True
            if change_pct not in [None, 'N/A', '']:
                prompt += f"- 涨跌幅: {change_pct}%\n"
                has_real_data = True
            if volume not in [None, 'N/A', '']:
                prompt += f"- 成交量: {volume}\n"
                has_real_data = True
            prompt += "\n"
        
        # 添加维度评估结果
        if 'evaluations' in market_data:
            prompt += "六大维度评估结果:\n"
            for dim_name, eval_result in market_data['evaluations'].items():
                if isinstance(eval_result, DimensionEvaluationResult):
                    prompt += f"- {dim_name}: 评分={eval_result.score:.3f}, 信号={eval_result.signal}, 置信度={eval_result.confidence:.3f}\n"
                    if eval_result.details:
                        prompt += f"  详情: {eval_result.details}\n"
                    has_real_data = True
            prompt += "\n"
        
        # 添加市场分类结果
        if 'classification' in market_data:
            classification = market_data['classification']
            prompt += f"当前市场分类: {classification.get('primary_classification', '未分类')}\n"
            prompt += f"分类置信度: {classification.get('classification_confidence', 0):.3f}\n"
            prompt += f"市场特征: {', '.join(classification.get('market_characteristics', []))}\n\n"
            has_real_data = True
        
        # 添加CZSC结构分析数据
        czsc_structure = market_data.get('czsc_structure', {})
        if czsc_structure:
            prompt += "CZSC缠论结构分析:\n"
            
            # 添加分型(FX)数据
            fx_list = czsc_structure.get('fx_list', [])
            if fx_list:
                prompt += "  分型(FX)数据:\n"
                for i, fx in enumerate(fx_list[-5:], 1):  # 显示最近5个分型
                    fx_type = "顶分型" if fx['fx_mark'] == 'g' else "底分型"
                    prompt += f"  - 分型{i}: {fx_type}, 价格={fx['fx_price']:.4f}, 时间={fx['dt']}\n"
                    has_real_data = True
            
            # 添加笔(BI)数据
            bi_list = czsc_structure.get('bi_list', [])
            if bi_list:
                prompt += "  笔(BI)数据:\n"
                for i, bi in enumerate(bi_list[-5:], 1):  # 显示最近5个笔
                    prompt += f"  - 笔{i}: {bi['direction']}, 价格={bi['bi_price']:.4f}, 时间={bi['dt']}\n"
                    has_real_data = True
            
            # 添加线段(XD)数据
            xd_list = czsc_structure.get('xd_list', [])
            if xd_list:
                prompt += "  线段(XD)数据:\n"
                for i, xd in enumerate(xd_list[-3:], 1):  # 显示最近3个线段
                    prompt += f"  - 线段{i}: {xd['direction']}, 价格={xd['xd_price']:.4f}, 时间={xd['dt']}\n"
                    has_real_data = True
            
            # 添加结构分析
            structure_analysis = czsc_structure.get('structure_analysis', {})
            if structure_analysis:
                prompt += "  结构分析:\n"
                prompt += f"  - 当前趋势: {structure_analysis.get('current_trend', '未知')}\n"
                prompt += f"  - 结构强度: {structure_analysis.get('structure_strength', 0.5):.2f}\n"
                
                # 关键价位
                key_levels = structure_analysis.get('key_levels', [])
                if key_levels:
                    prompt += "  - 关键价位:\n"
                    for level in key_levels[-3:]:  # 显示最近3个关键价位
                        prompt += f"    * {level['type']}: {level['price']:.4f}\n"
                
                # 结构信号
                structure_signals = structure_analysis.get('structure_signals', [])
                if structure_signals:
                    prompt += f"  - 结构信号: {', '.join(structure_signals)}\n"
                
                has_real_data = True
            
            prompt += "\n"
        
        # 根据数据可用性调整分析要求
        if has_real_data:
            if czsc_structure:
                prompt += """请基于以上CZSC缠论技术分析数据进行深度综合分析，特别关注：

【缠论结构分析要点】：
1. 分型(FX)和笔(BI)的结构关系 - 分析最近分型的高低点变化趋势
2. 当前市场所处的缠论结构位置 - 判断是否处于关键转折点
3. 基于笔和线段的趋势判断 - 分析笔的方向性和延续性
4. 结合传统技术指标和缠论结构的综合建议

【深度分析要求】：
- 详细解释MA5和MA20的多头/空头排列对趋势的影响
- 分析MACD指标的金叉/死叉信号强度和可靠性
- 结合RSI指标判断当前是否存在超买超卖情况
- 评估成交量配合情况，判断价格走势的可持续性
- 基于卦象分析结果评估市场情绪和投资者行为
- 综合资金流向数据判断主力资金的进出情况

【投资策略建议】：
请提供具体的操作建议，包括：
- 建议的买入/卖出时机和价位
- 仓位管理建议（轻仓/重仓/满仓）
- 止损止盈位设置建议
- 持有期预期和风险控制措施

请给出详细专业的缠论分析和具体可操作的投资建议，分析内容要丰富全面。"""
            else:
                prompt += """请基于以上技术分析数据进行深度分析，包括：

【技术指标分析】：
- 移动平均线系统的多空排列分析
- MACD指标的趋势确认和背离分析
- RSI等震荡指标的超买超卖判断
- 成交量与价格的配合关系分析

【市场情绪分析】：
- 基于卦象分析的市场情绪判断
- 资金流向对价格走势的影响
- 投资者行为模式分析

【投资建议】：
- 具体的操作策略和时机选择
- 风险控制和仓位管理建议
- 预期收益和持有期建议

请提供详细全面的分析和具体可操作的投资建议。"""
        else:
            prompt += f"""注意：当前缺乏实时市场数据，这可能是由于数据源暂时不可用。

请基于{fund_code}的基本情况进行分析：
1. 这是一只ETF基金，请分析其一般投资特点
2. 在数据缺失的情况下，给出谨慎的投资建议
3. 建议投资者等待更多数据后再做决策

请提供保守的分析意见。"""
        
        return prompt
    
    def _parse_llm_response(self, llm_response: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 记录原始响应用于调试
            self.logger.debug(f"LLM原始响应: {llm_response[:500]}...")
            
            # 尝试解析JSON格式的响应
            if '{' in llm_response and '}' in llm_response:
                json_start = llm_response.find('{')
                json_end = llm_response.rfind('}') + 1
                json_str = llm_response[json_start:json_end]
                
                self.logger.debug(f"提取的JSON字符串: {json_str[:200]}...")
                
                # 尝试修复常见的JSON格式问题
                json_str = self._fix_json_format(json_str)
                
                parsed_result = json.loads(json_str)
                self.logger.debug("JSON解析成功")
            else:
                # 如果不是JSON格式，创建结构化结果
                self.logger.warning("LLM响应不包含JSON格式，使用文本解析")
                parsed_result = self._parse_text_response(llm_response)
            
            # 验证必需字段
            parsed_result = self._validate_and_fix_response(parsed_result)
            
            # 添加元数据
            result = {
                "analysis_type": "llm_market_analysis",
                "analysis_time": datetime.now().isoformat(),
                "llm_model": "kimi-k2-0711-preview",
                "raw_response": llm_response,
                **parsed_result
            }
            
            return result
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {str(e)}")
            self.logger.error(f"问题位置: 行{e.lineno}, 列{e.colno}")
            self.logger.error(f"错误内容: {e.doc[max(0, e.pos-50):e.pos+50] if hasattr(e, 'doc') and e.doc else 'N/A'}")
            return self._create_error_response(f"JSON解析失败: {str(e)}", llm_response)
            
        except Exception as e:
            self.logger.error(f"解析LLM响应失败: {str(e)}")
            return self._create_error_response(f"解析失败: {str(e)}", llm_response)
    
    def _parse_text_response(self, text_response: str) -> Dict[str, Any]:
        """解析非JSON格式的文本响应"""
        try:
            # 尝试从文本中提取关键信息
            lines = text_response.split('\n')
            
            result = {
                "market_drivers": [],
                "risk_points": [],
                "opportunities": [],
                "strategy_suggestion": "",
                "confidence_level": 0.6,
                "market_sentiment": "中性",
                "key_insights": ""
            }
            
            current_section = None
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 识别不同的分析部分
                if "驱动因素" in line or "market_drivers" in line.lower():
                    current_section = "market_drivers"
                elif "风险" in line or "risk" in line.lower():
                    current_section = "risk_points"
                elif "机会" in line or "opportunity" in line.lower():
                    current_section = "opportunities"
                elif "建议" in line or "strategy" in line.lower():
                    current_section = "strategy_suggestion"
                elif line.startswith('-') or line.startswith('•') or line.startswith('*'):
                    # 列表项
                    item = line[1:].strip()
                    if current_section in ["market_drivers", "risk_points", "opportunities"]:
                        result[current_section].append(item)
                elif current_section == "strategy_suggestion":
                    result["strategy_suggestion"] += line + " "
            
            # 如果没有提取到具体内容，使用整个响应作为洞察
            if not any(result[key] for key in ["market_drivers", "risk_points", "opportunities"]):
                result["key_insights"] = text_response[:300] + "..." if len(text_response) > 300 else text_response
            
            return result
            
        except Exception as e:
            self.logger.warning(f"文本解析失败: {e}")
            return {
                "market_drivers": ["文本解析结果"],
                "risk_points": ["需要进一步分析"],
                "opportunities": ["待识别"],
                "strategy_suggestion": text_response[:200] + "..." if len(text_response) > 200 else text_response,
                "confidence_level": 0.5,
                "market_sentiment": "中性",
                "key_insights": "基于文本分析"
            }
    
    def _validate_and_fix_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """验证并修复响应数据"""
        required_fields = {
            "market_drivers": [],
            "risk_points": [],
            "opportunities": [],
            "strategy_suggestion": "",
            "confidence_level": 0.5,
            "market_sentiment": "中性",
            "key_insights": ""
        }
        
        # 确保所有必需字段存在
        for field, default_value in required_fields.items():
            if field not in response:
                response[field] = default_value
                self.logger.debug(f"添加缺失字段: {field}")
        
        # 验证数据类型
        if not isinstance(response.get("market_drivers"), list):
            response["market_drivers"] = [str(response.get("market_drivers", ""))]
        
        if not isinstance(response.get("risk_points"), list):
            response["risk_points"] = [str(response.get("risk_points", ""))]
            
        if not isinstance(response.get("opportunities"), list):
            response["opportunities"] = [str(response.get("opportunities", ""))]
        
        # 验证置信度范围
        confidence = response.get("confidence_level", 0.5)
        if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
            response["confidence_level"] = 0.5
            self.logger.debug("修复置信度值")
        
        # 验证市场情绪
        valid_sentiments = ["积极", "中性", "谨慎", "positive", "neutral", "cautious"]
        if response.get("market_sentiment") not in valid_sentiments:
            response["market_sentiment"] = "中性"
            self.logger.debug("修复市场情绪值")
        
        return response
    
    def _create_error_response(self, error_msg: str, raw_response: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "analysis_type": "llm_market_analysis",
            "analysis_time": datetime.now().isoformat(),
            "error": error_msg,
            "raw_response": raw_response,
            "market_drivers": ["解析错误"],
            "risk_points": ["响应解析失败"],
            "opportunities": [],
            "strategy_suggestion": "建议使用传统分析方法",
            "confidence_level": 0.1,
            "market_sentiment": "未知",
            "key_insights": "LLM响应解析出错"
        }
    
    def _get_fallback_analysis(self, market_data: Dict[str, Any], error: str = None) -> Dict[str, Any]:
        """获取兜底分析结果"""
        return {
            "analysis_type": "fallback_analysis",
            "analysis_time": datetime.now().isoformat(),
            "error": error,
            "market_drivers": ["技术指标驱动"],
            "risk_points": ["LLM服务不可用"],
            "opportunities": ["基于传统指标分析"],
            "strategy_suggestion": "建议使用传统技术分析方法进行决策",
            "confidence_level": 0.3,
            "market_sentiment": "中性",
            "key_insights": "LLM分析服务暂时不可用，建议依赖传统分析方法"
        }
    
    def generate_decision_explanation(self, decision_data: Dict[str, Any]) -> str:
        """
        @brief 生成决策解释
        @param decision_data: 决策相关数据
        @return: 决策解释文本
        """
        if not self.client:
            return self._get_fallback_explanation(decision_data)
        
        try:
            prompt = f"""请为以下投资决策生成清晰的解释：

决策类型: {decision_data.get('decision_type', '未知')}
基金代码: {decision_data.get('fund_code', '未指定')}
决策时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

决策依据:
{json.dumps(decision_data, ensure_ascii=False, indent=2)}

请用通俗易懂的语言解释这个决策的原因，包括：
1. 为什么做出这个决策
2. 主要考虑因素
3. 预期效果
4. 风险提示

要求简洁明了，不超过200字。"""

            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的投资顾问，擅长用简洁明了的语言解释投资决策。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            completion = self.client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=messages,
                temperature=0.2,
            )
            
            explanation = completion.choices[0].message.content
            self.logger.info("决策解释生成成功")
            return explanation
            
        except Exception as e:
            self.logger.error(f"决策解释生成失败: {str(e)}")
            return self._get_fallback_explanation(decision_data, error=str(e))
    
    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        try:
            # 首先尝试直接解析，如果成功就不需要修复
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError as e:
            self.logger.debug(f"JSON解析失败，尝试修复: {e}")
            
        try:
            import re
            
            # 保存原始字符串用于回退
            original_json_str = json_str
            
            # 1. 清理和标准化格式
            # 移除多余的空白字符，但保留结构
            json_str = re.sub(r'\s+', ' ', json_str.strip())
            
            # 2. 修复数组内的多余逗号 (如 ["item1", "item2",])
            json_str = re.sub(r',\s*]', ']', json_str)
            
            # 3. 修复对象内的多余逗号 (如 {"key": "value",})
            json_str = re.sub(r',\s*}', '}', json_str)
            
            # 4. 修复缺少逗号的问题
            # 在 ] "key" 之间添加逗号
            json_str = re.sub(r']\s*"([^"]+)"\s*:', r'], "\1":', json_str)
            
            # 在 } "key" 之间添加逗号
            json_str = re.sub(r'}\s*"([^"]+)"\s*:', r'}, "\1":', json_str)
            
            # 在 "value" "key" 之间添加逗号
            json_str = re.sub(r'"\s*"([^"]+)"\s*:', r'", "\1":', json_str)
            
            # 在数字后面缺少逗号的情况
            json_str = re.sub(r'(\d+\.?\d*)\s*"([^"]+)"\s*:', r'\1, "\2":', json_str)
            
            # 在布尔值后面缺少逗号的情况
            json_str = re.sub(r'(true|false)\s*"([^"]+)"\s*:', r'\1, "\2":', json_str)
            
            # 5. 格式化JSON字符串，使其更易读
            json_str = json_str.replace('{', '{\n    ')
            json_str = json_str.replace('}', '\n}')
            json_str = json_str.replace(',', ',\n    ')
            json_str = json_str.replace('[', '[\n        ')
            json_str = json_str.replace(']', '\n    ]')
            
            # 6. 清理多余的换行和空格
            lines = json_str.split('\n')
            cleaned_lines = []
            for line in lines:
                cleaned_line = line.strip()
                if cleaned_line:
                    cleaned_lines.append('    ' + cleaned_line if cleaned_line not in ['{', '}'] else cleaned_line)
            
            json_str = '\n'.join(cleaned_lines)
            
            # 7. 最后一次清理多余逗号
            json_str = re.sub(r',\s*\n\s*}', '\n}', json_str)
            json_str = re.sub(r',\s*\n\s*]', '\n    ]', json_str)
            
            # 8. 验证修复结果
            json.loads(json_str)
            self.logger.debug("JSON格式修复成功")
            return json_str
            
        except Exception as e:
            self.logger.warning(f"JSON格式修复失败: {e}")
            
            # 如果复杂修复失败，尝试简单修复
            try:
                simple_fixed = original_json_str.replace(',}', '}').replace(',]', ']')
                # 简单的逗号修复
                import re
                simple_fixed = re.sub(r'"\s+"', '", "', simple_fixed)
                json.loads(simple_fixed)
                self.logger.debug("简单JSON修复成功")
                return simple_fixed
            except:
                pass
            
            # 如果所有修复都失败，返回原始字符串
            return original_json_str
    
    def _get_fallback_explanation(self, decision_data: Dict[str, Any], error: str = None) -> str:
        """获取兜底决策解释"""
        decision_type = decision_data.get('decision_type', '未知决策')
        fund_code = decision_data.get('fund_code', '未指定基金')
        
        explanation = f"基于技术分析对{fund_code}做出{decision_type}决策。"
        
        if error:
            explanation += f" (注：AI解释服务暂时不可用: {error})"
        
        return explanation
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试显示系统修复
验证显示系统是否能正确显示分析结果
"""

import sys
import os
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_display_system():
    """测试显示系统"""
    print("🔍 测试显示系统...")
    
    try:
        from system.enhanced_display_system import EnhancedDisplaySystem
        
        display = EnhancedDisplaySystem()
        print("✅ 显示系统初始化成功")
        
        # 创建模拟分析结果
        mock_analysis_result = {
            'fund_code': '518880',
            'analysis_time': datetime.now().isoformat(),
            'traditional_agents': {
                'technical_analysis': {
                    'decision': 'BUY',
                    'buy_signal': True,
                    'technical_indicators': {
                        'ma5': 7.4138,
                        'ma20': 7.3899,
                        'macd': -0.0034,
                        'macd_signal': -0.0050,
                        'macd_hist': 0.0016,
                        'rsi': 45.2,
                        'bb_upper': 7.45,
                        'bb_lower': 7.35,
                        'bb_middle': 7.40,
                        'close': 7.397,
                        'kdj_k': 52.3,
                        'kdj_d': 48.7,
                        'kdj_j': 59.5,
                        'volume_ratio': 1.2
                    }
                },
                'gua_analysis': {
                    'is_buy_gua': False,
                    'is_sell_gua': False,
                    'is_select_gua': True,
                    'gua_score': 0.65,
                    'main_gua': '乾卦'
                },
                'flow_analysis': {
                    'high_liquidity': True,
                    'low_liquidity': False,
                    'capital_flow': '净流入',
                    'price_data': {
                        'price': 7.397,
                        'change_rate': -0.07,
                        'volume': 39821900,
                        'amount': 294567890
                    }
                }
            },
            'llm_analysis': {
                'market_sentiment': '谨慎',
                'confidence_level': 0.72,
                'market_drivers': 'CZSC缠论技术指标显示MA5(7.4138)高于MA20(7.3899)形成短期多头排列，配合MACD负值收窄(-0.0034>-0.0050)显示下跌动能衰竭'
            },
            'enhanced_decision': {
                'decision': 'hold',
                'strength': 0.65,
                'confidence': 0.88,
                'reasoning': '综合技术分析和市场情绪，建议谨慎持有'
            },
            'risk_control': {
                'risk_level': 'low',
                'position_limit': 0.8,
                'risk_alerts': []
            },
            'final_decision': 'hold',
            'final_confidence': 0.88
        }
        
        print("\n📊 测试显示效果:")
        print("="*80)
        
        # 测试综合分析显示
        display.display_comprehensive_analysis(mock_analysis_result)
        
        print("\n✅ 显示系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 显示系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_with_display():
    """测试协调器与显示系统集成"""
    print("\n🔍 测试协调器与显示系统集成...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        from system.enhanced_display_system import EnhancedDisplaySystem
        
        # 创建协调器和显示系统
        coordinator = MultiAgentCoordinatorV3()
        display = EnhancedDisplaySystem()
        
        # 执行分析
        fund_code = '518880'
        print(f"📊 分析基金: {fund_code}")
        
        analysis_result = coordinator.coordinate_analysis(fund_code)
        
        if analysis_result and 'error' not in analysis_result:
            print("✅ 协调器分析成功")
            
            # 使用显示系统展示结果
            print("\n📊 使用修复后的显示系统:")
            display.display_comprehensive_analysis(analysis_result)
            
            return True
        else:
            print(f"❌ 协调器分析失败: {analysis_result}")
            return False
        
    except Exception as e:
        print(f"❌ 协调器与显示系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 显示系统修复测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    tests = [
        ("显示系统基本功能测试", test_display_system),
        ("协调器与显示系统集成测试", test_coordinator_with_display),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 显示系统修复测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 显示系统修复成功！")
        print("\n💡 修复总结:")
        print("   ✅ 基础信息显示已修复")
        print("   ✅ 智能体分析结果显示已修复")
        print("   ✅ 技术指标详情显示已修复")
        print("   ✅ 价格信息正确显示")
        print("   ✅ 数据结构匹配问题已解决")
    else:
        print("⚠️ 部分功能仍需优化")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
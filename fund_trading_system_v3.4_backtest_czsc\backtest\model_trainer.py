#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于PyTorch的模型训练器
替代TensorFlow版本
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import optuna
import warnings
import logging
warnings.filterwarnings('ignore')


class ModelTrainer:
    """
    基于PyTorch的模型训练验证模块
    负责模型训练、交叉验证、超参数优化
    """
    
    def __init__(self, validation_split: float = 0.2, early_stopping_patience: int = 10,
                 random_state: int = 42):
        """
        初始化模型训练器
        
        Args:
            validation_split: 验证集比例
            early_stopping_patience: 早停耐心值
            random_state: 随机种子
        """
        self.validation_split = validation_split
        self.early_stopping_patience = early_stopping_patience
        self.random_state = random_state
        self.training_history = {}
        self.cv_results = {}
        self.best_params = {}
        self.training_log = []
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置随机种子
        np.random.seed(random_state)
        torch.manual_seed(random_state)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(random_state)
    
    def train_with_validation(self, model: nn.Module, X_train: np.ndarray, y_train: np.ndarray,
                            X_val: np.ndarray, y_val: np.ndarray,
                            epochs: int = 100, batch_size: int = 32,
                            optimizer: Optional[optim.Optimizer] = None,
                            criterion: Optional[nn.Module] = None,
                            scheduler: Optional[optim.lr_scheduler._LRScheduler] = None,
                            verbose: int = 1) -> Dict[str, List[float]]:
        """
        带验证集的模型训练
        
        Args:
            model: PyTorch模型
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签
            epochs: 训练轮数
            batch_size: 批次大小
            optimizer: 优化器
            criterion: 损失函数
            scheduler: 学习率调度器
            verbose: 详细程度
            
        Returns:
            训练历史
        """
        # 默认优化器和损失函数
        if optimizer is None:
            optimizer = optim.Adam(model.parameters(), lr=0.001)
        if criterion is None:
            criterion = nn.MSELoss()
        
        # 创建数据加载器
        train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
        val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_mae': [],
            'val_mae': []
        }
        
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        
        model.to(self.device)
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_mae = 0.0
            
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                train_mae += torch.mean(torch.abs(outputs - batch_y)).item()
            
            train_loss /= len(train_loader)
            train_mae /= len(train_loader)
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_mae = 0.0
            
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_X)
                    loss = criterion(outputs, batch_y)
                    
                    val_loss += loss.item()
                    val_mae += torch.mean(torch.abs(outputs - batch_y)).item()
            
            val_loss /= len(val_loader)
            val_mae /= len(val_loader)
            
            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['train_mae'].append(train_mae)
            history['val_mae'].append(val_mae)
            
            # 学习率调度
            if scheduler is not None:
                scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= self.early_stopping_patience:
                    if verbose > 0:
                        self.logger.info(f"早停触发，在第{epoch+1}轮停止训练")
                    break
            
            # 打印进度
            if verbose > 0 and (epoch + 1) % 10 == 0:
                self.logger.info(f"Epoch {epoch+1}/{epochs} - "
                               f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                               f"Train MAE: {train_mae:.6f}, Val MAE: {val_mae:.6f}")
        
        # 恢复最佳模型
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        self.training_history = history
        return history
    
    def cross_validate(self, model_builder, X: np.ndarray, y: np.ndarray,
                      cv_folds: int = 5, epochs: int = 100, batch_size: int = 32,
                      model_params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        时间序列交叉验证
        
        Args:
            model_builder: 模型构建函数
            X: 特征数据
            y: 标签数据
            cv_folds: 交叉验证折数
            epochs: 训练轮数
            batch_size: 批次大小
            model_params: 模型参数
            
        Returns:
            交叉验证结果
        """
        if model_params is None:
            model_params = {}
        
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        cv_scores = {
            'train_mse': [],
            'val_mse': [],
            'train_mae': [],
            'val_mae': [],
            'train_r2': [],
            'val_r2': []
        }
        
        fold = 0
        for train_idx, val_idx in tscv.split(X):
            fold += 1
            self.logger.info(f"交叉验证 Fold {fold}/{cv_folds}")
            
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # 构建模型
            model = model_builder(**model_params)
            
            # 训练模型
            history = self.train_with_validation(
                model, X_train, y_train, X_val, y_val,
                epochs=epochs, batch_size=batch_size, verbose=0
            )
            
            # 评估模型
            model.eval()
            with torch.no_grad():
                # 训练集预测
                X_train_tensor = torch.FloatTensor(X_train).to(self.device)
                train_pred = model(X_train_tensor).cpu().numpy()
                
                # 验证集预测
                X_val_tensor = torch.FloatTensor(X_val).to(self.device)
                val_pred = model(X_val_tensor).cpu().numpy()
            
            # 计算指标
            train_mse = mean_squared_error(y_train, train_pred)
            val_mse = mean_squared_error(y_val, val_pred)
            train_mae = mean_absolute_error(y_train, train_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            train_r2 = r2_score(y_train, train_pred)
            val_r2 = r2_score(y_val, val_pred)
            
            cv_scores['train_mse'].append(train_mse)
            cv_scores['val_mse'].append(val_mse)
            cv_scores['train_mae'].append(train_mae)
            cv_scores['val_mae'].append(val_mae)
            cv_scores['train_r2'].append(train_r2)
            cv_scores['val_r2'].append(val_r2)
        
        # 计算平均值和标准差
        cv_results = {}
        for metric, scores in cv_scores.items():
            cv_results[f'{metric}_mean'] = np.mean(scores)
            cv_results[f'{metric}_std'] = np.std(scores)
        
        self.cv_results = cv_results
        return cv_results
    
    def hyperparameter_optimization(self, model_builder, X: np.ndarray, y: np.ndarray,
                                  param_space: Dict[str, Any], n_trials: int = 100,
                                  cv_folds: int = 3, epochs: int = 50) -> Dict[str, Any]:
        """
        超参数优化
        
        Args:
            model_builder: 模型构建函数
            X: 特征数据
            y: 标签数据
            param_space: 参数空间定义
            n_trials: 优化试验次数
            cv_folds: 交叉验证折数
            epochs: 训练轮数
            
        Returns:
            最佳参数
        """
        def objective(trial):
            # 从参数空间中采样参数
            params = {}
            for param_name, param_config in param_space.items():
                if param_config['type'] == 'int':
                    params[param_name] = trial.suggest_int(
                        param_name, param_config['low'], param_config['high']
                    )
                elif param_config['type'] == 'float':
                    params[param_name] = trial.suggest_float(
                        param_name, param_config['low'], param_config['high']
                    )
                elif param_config['type'] == 'categorical':
                    params[param_name] = trial.suggest_categorical(
                        param_name, param_config['choices']
                    )
            
            # 交叉验证
            cv_results = self.cross_validate(
                model_builder, X, y, cv_folds=cv_folds, 
                epochs=epochs, model_params=params
            )
            
            # 返回验证集MSE的平均值（最小化目标）
            return cv_results['val_mse_mean']
        
        # 创建优化研究
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)
        
        self.best_params = study.best_params
        
        return {
            'best_params': study.best_params,
            'best_value': study.best_value,
            'study': study
        }
    
    def evaluate_model(self, model: nn.Module, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试标签
            
        Returns:
            评估指标
        """
        model.eval()
        model.to(self.device)
        
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test).to(self.device)
            predictions = model(X_test_tensor).cpu().numpy()
        
        # 计算各种指标
        mse = mean_squared_error(y_test, predictions)
        mae = mean_absolute_error(y_test, predictions)
        r2 = r2_score(y_test, predictions)
        rmse = np.sqrt(mse)
        
        # 计算方向准确率（对于回归任务）
        if len(y_test) > 1:
            actual_direction = np.sign(np.diff(y_test.flatten()))
            pred_direction = np.sign(np.diff(predictions.flatten()))
            direction_accuracy = np.mean(actual_direction == pred_direction)
        else:
            direction_accuracy = 0.0
        
        metrics = {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'direction_accuracy': direction_accuracy
        }
        
        return metrics
    
    def save_model(self, model: nn.Module, filepath: str):
        """
        保存模型
        
        Args:
            model: 要保存的模型
            filepath: 保存路径
        """
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_class': model.__class__.__name__,
            'training_history': self.training_history,
            'best_params': self.best_params
        }, filepath)
        
        self.logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, model: nn.Module, filepath: str) -> nn.Module:
        """
        加载模型
        
        Args:
            model: 模型实例
            filepath: 模型文件路径
            
        Returns:
            加载的模型
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        if 'training_history' in checkpoint:
            self.training_history = checkpoint['training_history']
        if 'best_params' in checkpoint:
            self.best_params = checkpoint['best_params']
        
        self.logger.info(f"模型已从 {filepath} 加载")
        return model
    
    def get_training_summary(self) -> Dict[str, Any]:
        """
        获取训练摘要
        
        Returns:
            训练摘要信息
        """
        summary = {
            'training_completed': len(self.training_history) > 0,
            'best_params': self.best_params,
            'cv_results': self.cv_results,
            'device': str(self.device)
        }
        
        if self.training_history:
            summary['final_train_loss'] = self.training_history['train_loss'][-1]
            summary['final_val_loss'] = self.training_history['val_loss'][-1]
            summary['best_val_loss'] = min(self.training_history['val_loss'])
            summary['epochs_trained'] = len(self.training_history['train_loss'])
        
        return summary


# ModelTrainer类已经定义在上面
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版回测验证器
不依赖TensorFlow，提供基本的信号验证功能
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.enhanced_data_fetcher import get_kline


@dataclass
class SimpleSignalBacktestResult:
    """简化版信号回测结果"""
    signal_name: str
    original_strength: float
    optimized_strength: float
    backtest_performance: Dict[str, float]
    quality_score: float
    confidence_adjustment: float
    risk_metrics: Dict[str, float]
    recommendation: str  # 'enhance', 'maintain', 'reduce', 'reject'


class SimpleBacktestValidator:
    """
    简化版回测信号验证器
    不依赖TensorFlow，使用基本统计方法验证信号
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("简化版回测验证器初始化成功")
        
        # 基本配置
        self.lookback_days = 30  # 回看天数
        self.min_data_points = 20  # 最少数据点
        
    def validate_signal(self, fund_code: str, signal_data: Dict[str, Any]) -> SimpleSignalBacktestResult:
        """
        验证信号质量
        
        Args:
            fund_code: 基金代码
            signal_data: 信号数据
            
        Returns:
            SimpleSignalBacktestResult: 验证结果
        """
        try:
            self.logger.info(f"开始验证信号 - 基金: {fund_code}")
            
            # 获取历史数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=self.lookback_days)).strftime('%Y-%m-%d')
            
            df = get_kline(fund_code, freq='D', start_date=start_date, end_date=end_date)
            
            if df is None or len(df) < self.min_data_points:
                return self._create_default_result(signal_data, "insufficient_data")
            
            # 计算基本技术指标
            df = self._calculate_basic_indicators(df)
            
            # 分析信号质量
            signal_analysis = self._analyze_signal_quality(df, signal_data)
            
            # 计算风险指标
            risk_metrics = self._calculate_risk_metrics(df)
            
            # 生成建议
            recommendation = self._generate_recommendation(signal_analysis, risk_metrics)
            
            # 计算质量评分
            quality_score = self._calculate_quality_score(signal_analysis, risk_metrics)
            
            # 计算置信度调整
            confidence_adjustment = self._calculate_confidence_adjustment(quality_score, signal_analysis)
            
            result = SimpleSignalBacktestResult(
                signal_name=signal_data.get('decision', 'unknown'),
                original_strength=signal_data.get('strength', 0.5),
                optimized_strength=max(0.1, min(0.9, signal_data.get('strength', 0.5) + confidence_adjustment)),
                backtest_performance=signal_analysis,
                quality_score=quality_score,
                confidence_adjustment=confidence_adjustment,
                risk_metrics=risk_metrics,
                recommendation=recommendation
            )
            
            self.logger.info(f"信号验证完成 - {fund_code}: 质量评分={quality_score:.3f}, 建议={recommendation}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"信号验证失败 {fund_code}: {str(e)}")
            return self._create_default_result(signal_data, f"error: {str(e)}")
    
    def _calculate_basic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基本技术指标"""
        # 移动平均
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        
        # 价格变化
        df['price_change'] = df['close'].pct_change()
        df['price_change_5d'] = df['close'].pct_change(5)
        
        # 波动率
        df['volatility'] = df['price_change'].rolling(window=10).std()
        
        # 成交量变化
        df['volume_change'] = df['vol'].pct_change()
        df['volume_ma'] = df['vol'].rolling(window=10).mean()
        df['volume_ratio'] = df['vol'] / df['volume_ma']
        
        return df
    
    def _analyze_signal_quality(self, df: pd.DataFrame, signal_data: Dict[str, Any]) -> Dict[str, float]:
        """分析信号质量"""
        analysis = {}
        
        # 趋势一致性
        recent_trend = df['close'].iloc[-5:].pct_change().mean()
        ma_trend = (df['ma5'].iloc[-1] - df['ma20'].iloc[-1]) / df['ma20'].iloc[-1]
        
        decision = signal_data.get('decision', 'hold')
        
        if decision == 'buy':
            trend_consistency = max(0, min(1, (recent_trend + ma_trend) * 10))
        elif decision == 'sell':
            trend_consistency = max(0, min(1, -(recent_trend + ma_trend) * 10))
        else:
            trend_consistency = 0.5
        
        analysis['trend_consistency'] = trend_consistency
        
        # 成交量确认
        recent_volume_ratio = df['volume_ratio'].iloc[-3:].mean()
        volume_confirmation = min(1.0, recent_volume_ratio / 1.5) if recent_volume_ratio > 1 else 0.3
        analysis['volume_confirmation'] = volume_confirmation
        
        # 价格动量
        momentum = abs(df['price_change_5d'].iloc[-1]) if not pd.isna(df['price_change_5d'].iloc[-1]) else 0
        momentum_score = min(1.0, momentum * 20)  # 放大动量信号
        analysis['momentum_score'] = momentum_score
        
        # 波动率分析
        current_volatility = df['volatility'].iloc[-1] if not pd.isna(df['volatility'].iloc[-1]) else 0
        avg_volatility = df['volatility'].mean()
        volatility_ratio = current_volatility / avg_volatility if avg_volatility > 0 else 1
        volatility_score = 1 - min(1.0, max(0, (volatility_ratio - 1) / 2))  # 波动率过高降分
        analysis['volatility_score'] = volatility_score
        
        return analysis
    
    def _calculate_risk_metrics(self, df: pd.DataFrame) -> Dict[str, float]:
        """计算风险指标"""
        risk_metrics = {}
        
        # 最大回撤
        cumulative_returns = (1 + df['price_change'].fillna(0)).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        risk_metrics['max_drawdown'] = max_drawdown
        
        # 波动率
        volatility = df['price_change'].std() * np.sqrt(252)  # 年化波动率
        risk_metrics['volatility'] = volatility
        
        # 夏普比率（简化版）
        avg_return = df['price_change'].mean() * 252  # 年化收益
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0
        risk_metrics['sharpe_ratio'] = sharpe_ratio
        
        # VaR (Value at Risk) 95%
        var_95 = np.percentile(df['price_change'].dropna(), 5)
        risk_metrics['var_95'] = abs(var_95)
        
        return risk_metrics
    
    def _generate_recommendation(self, signal_analysis: Dict[str, float], 
                               risk_metrics: Dict[str, float]) -> str:
        """生成建议"""
        # 综合评分
        trend_score = signal_analysis.get('trend_consistency', 0.5)
        volume_score = signal_analysis.get('volume_confirmation', 0.5)
        momentum_score = signal_analysis.get('momentum_score', 0.5)
        volatility_score = signal_analysis.get('volatility_score', 0.5)
        
        # 风险评分
        max_drawdown = risk_metrics.get('max_drawdown', 0.1)
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
        
        # 综合评分
        signal_score = (trend_score + volume_score + momentum_score + volatility_score) / 4
        risk_score = max(0, min(1, (sharpe_ratio + 1) / 2)) * (1 - min(1, max_drawdown * 2))
        
        overall_score = (signal_score * 0.7 + risk_score * 0.3)
        
        if overall_score >= 0.75:
            return 'enhance'
        elif overall_score >= 0.6:
            return 'maintain'
        elif overall_score >= 0.4:
            return 'reduce'
        else:
            return 'reject'
    
    def _calculate_quality_score(self, signal_analysis: Dict[str, float], 
                               risk_metrics: Dict[str, float]) -> float:
        """计算质量评分"""
        # 信号质量权重
        weights = {
            'trend_consistency': 0.3,
            'volume_confirmation': 0.2,
            'momentum_score': 0.2,
            'volatility_score': 0.15
        }
        
        signal_score = sum(signal_analysis.get(key, 0.5) * weight 
                          for key, weight in weights.items())
        
        # 风险调整
        max_drawdown = risk_metrics.get('max_drawdown', 0.1)
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
        
        risk_adjustment = max(0.1, 1 - max_drawdown) * max(0.1, min(1, (sharpe_ratio + 1) / 2))
        
        quality_score = signal_score * risk_adjustment * 0.15 + signal_score * 0.85
        
        return max(0.0, min(1.0, quality_score))
    
    def _calculate_confidence_adjustment(self, quality_score: float, 
                                       signal_analysis: Dict[str, float]) -> float:
        """计算置信度调整"""
        # 基于质量评分的调整
        base_adjustment = (quality_score - 0.5) * 0.3  # -0.15 到 +0.15
        
        # 基于趋势一致性的额外调整
        trend_consistency = signal_analysis.get('trend_consistency', 0.5)
        trend_adjustment = (trend_consistency - 0.5) * 0.2  # -0.1 到 +0.1
        
        total_adjustment = base_adjustment + trend_adjustment
        
        # 限制调整范围
        return max(-0.3, min(0.3, total_adjustment))
    
    def _create_default_result(self, signal_data: Dict[str, Any], reason: str) -> SimpleSignalBacktestResult:
        """创建默认结果"""
        return SimpleSignalBacktestResult(
            signal_name=signal_data.get('decision', 'unknown'),
            original_strength=signal_data.get('strength', 0.5),
            optimized_strength=signal_data.get('strength', 0.5),
            backtest_performance={'error': reason},
            quality_score=0.5,
            confidence_adjustment=0.0,
            risk_metrics={'error': reason},
            recommendation='maintain'
        )
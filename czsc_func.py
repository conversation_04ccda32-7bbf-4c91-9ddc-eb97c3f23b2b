import pandas as pd
import time
# import numba as nb
import QUANTAXIS as QA
from QUANTAXIS import QA_indicator_BOLL
from datetime import datetime, timedelta
# from chan import KlineAnalyze, SolidAnalyze, ma, macd
# from czsc import KlineAnalyze
from simpleczsc import KlineAnalyze
import tushare as ts
# from QA_moneyflow import realtime_moneyflow
# import multiprocessing
# import talib as ta
from QUANTAXIS.QAIndicator.base import *
from dataNew import DataApiNew
pro = DataApiNew()
# pro = ts.pro_api()
# data = QA.QAFetch.QATdx.QA_fetch_get_stock_list('stock')
trade_list = QA.QA_fetch_trade_date()


def _get_start_date(end_date, freq): # 日期参数原来组合 (20,70,150,300,300,300,500), (8,40,120,250,500,500,500)
    end_date = datetime.strptime(end_date, '%Y-%m-%d')
    if freq == '1min':
        start_date = end_date - timedelta(days=13)   # 7*240=1680
    elif freq == '5min':
        start_date = end_date - timedelta(days=48)  # 49/7*5*48=1680
    elif freq == '15min':
        start_date = end_date - timedelta(days=150)  # 147/7*5*16=1680
    elif freq == '30min':
        start_date = end_date - timedelta(days=315)  # 294/7*5*8=1680
    elif freq == '60min':
        start_date = end_date - timedelta(days=628)  # 420/7*5*4=1200
    elif freq == 'D':
        start_date = end_date - timedelta(days=2500)
    elif freq == 'W':
        start_date = end_date - timedelta(weeks=501)
    else:
        raise ValueError("'freq' value error, current value is %s, "
                         "optional valid values are ['1min', '5min', '30min', "
                         "'D', 'W']" % freq)
    return start_date.strftime('%Y-%m-%d')


end_date = datetime.now().strftime('%Y-%m-%d')
# start_date = '2020-02-01'
last_trade_day = trade_list[trade_list.index(end_date) - 1]


# def get_kline(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_kline(code, end_date=end_date, freq='30min'):
    # 统一 k 线数据格式为 6 列，分别是 ["symbol", "dt", "open", "close", "high", "low", "vr"]
    start_date = _get_start_date(end_date, freq)
    try:
        if "min" in freq:
    #         df = QA.QA_fetch_stock_min(code, start_date, end_date, 'pd', freq)
            df = QA.QAFetch.QATdx.QA_fetch_get_stock_min(code, start_date, end_date,freq)
            df.rename(columns={'code': "symbol", "datetime": "dt"}, inplace=True)
        else:
    #         df = QA.QA_fetch_stock_day(code, start_date, end_date, 'pd',freq)
    #         df['vol'] = df['volume']
            df = QA.QAFetch.QATdx.QA_fetch_get_stock_day(code, start_date, end_date)
            df.rename(columns={'code': "symbol", "date": "dt"}, inplace=True)
    except:
        print(f'{code}网络故障，没有取到数据')

    k = df[['symbol', 'dt', 'open', 'close', 'high', 'low', 'vol']]
    k.reset_index(drop=True, inplace=True)
    return k

    # 日线合成周线代码
    # def get_week_k(df):  # get_kline
    #     df.index = pd.to_datetime(df['dt'])
    #     df_week = df.resample('w').agg({'open': 'first', 'high':'max', 'low':'min', 'close':'last', 'vol':'sum'})
    #     return df_week

# def get_klines(code, start_date=start_date, end_date=end_date, freqs='1min,5min,30min,D'):
def get_klines(code, end_date=end_date, freqs='1min,5min,15min,30min,D,W'):
    """获取不同级别K线"""
    freq_map = {"1min": "1分钟", "5min": "5分钟", "15min":"15分钟", "30min": "30分钟", "D": "日线", "W": "周线"}
    klines = dict()
    freqs = freqs.split(",")
    for freq in freqs:
#         df = get_kline(code, start_date, end_date, freq=freq)
        df = get_kline(code, end_date, freq=freq)
        klines[freq_map[freq]] = df
    return klines


def get_vol_kline_analyze(code, end_date=end_date, freq='30min'):
    kline = get_kline(code, end_date, freq)
    kline['open'] = kline['vol']
    kline['close'] = kline['vol']
    kline['high'] = kline['vol']
    kline['low'] = kline['vol']
    ka = KlineAnalyze(kline)
    return ka


# def get_kline_analyze(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_kline_analyze(code, end_date=end_date, freq='30min'):
    #         df = get_kline(code, start_date, end_date, freq=freq)
    kline = get_kline(code, end_date, freq)
    ka = KlineAnalyze(kline)
    return ka

def KDJ(DataFrame, N=18, M1=3, M2=3):
    C = DataFrame['close']
    H = DataFrame['high']
    L = DataFrame['low']

    RSV = ((C - LLV(L, N)) / (HHV(H, N) - LLV(L, N)) * 100)
    K = SMA(RSV, M1)
    D = SMA(K, M2)
    J = 3 * K - 2 * D
    DICT = {'RSV':RSV, 'KDJ_K': K, 'KDJ_D': D, 'KDJ_J': J}
    return pd.DataFrame(DICT)


def get_kline_tick(code, start_time=last_trade_day, end_time=end_date):  # 15s 兼容 stock
    try:
        _df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction(code, start_time, start_time)
        df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(code)
        df = _df.append(df)
        # if df['price'].iloc[0] > 1000:
        #     df['price'] = df['price']/10
        df['new_order'] = df['order'].groupby(df['datetime']).rank()
        df.index = df.index + timedelta(seconds=3) * ((df['new_order'] - 1) % 20)
        df_new = df['price'].resample('15s').ohlc()
        df_new['vol'] = df['vol'].resample('15s').sum()
        df_new = df_new.dropna()
        df_new['symbol'] = code
        df_new['dt'] = df_new.index
        df_new.reset_index(drop=True, inplace=True)
        return df_new
    except:
        print(f'{code}网络故障，没有取到数据')


def get_kline_fund_tick(code, start_time=last_trade_day, end_time=end_date):  # 15s 兼容 stock
    try:
        _df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction(code, start_time, start_time)
        df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(code)
        df = _df.append(df)
        if df['price'].iloc[0] > 10:
            df['price'] = df['price']/10
        df['new_order'] = df['order'].groupby(df['datetime']).rank()
        df.index = df.index + timedelta(seconds=3) * ((df['new_order'] - 1) % 20)
        df_new = df['price'].resample('15s').ohlc()
        df_new['vol'] = df['vol'].resample('15s').sum()
        df_new = df_new.dropna()
        df_new['symbol'] = code
        df_new['dt'] = df_new.index
        df_new.reset_index(drop=True, inplace=True)
        return df_new
    except:
        print(f'{code}网络故障，没有取到数据')


def get_czsc_pd(code, end_date=end_date, freq='30min'):
#     ka = get_kline_analyze(code, start_date, end_date, freq)
    ka = get_kline_analyze(code, end_date, freq)
    df = ka.to_df(ma_params=(5,10,20),use_macd=True)
    df['vol_20'] = df['vol'].rolling(20).mean()
    df['vol_ratio'] = df['vol']/df['vol_20']
    # 方案一
    boll = QA_indicator_BOLL(df, N=26)
    kdj = KDJ(df)
    df = pd.concat([df, boll, kdj], axis=1)
    df.rename(columns={'symbol':'code'},inplace=True)
    return df


def get_czsc_pd_tick(code):
    # ka = get_kline_analyze(code, end_date, freq)
    ka = KlineAnalyze(get_kline_tick(code))
    df = ka.to_df(ma_params=(5,10,20),use_macd=True)
    df['vol_20'] = df['vol'].rolling(20).mean()
    df['vol_ratio'] = df['vol']/df['vol_20']
    # 方案一
    boll = QA_indicator_BOLL(df, N=26)
    kdj = KDJ(df)
    df = pd.concat([df, boll, kdj], axis=1)
    df.rename(columns={'symbol':'code'},inplace=True)
    return df


def get_czsc_pd_fund_tick(code):
    # ka = get_kline_analyze(code, end_date, freq)
    ka = KlineAnalyze(get_kline_fund_tick(code))
    df = ka.to_df(ma_params=(5,10,20),use_macd=True)
    df['vol_20'] = df['vol'].rolling(20).mean()
    df['vol_ratio'] = df['vol']/df['vol_20']
    # 方案一
    boll = QA_indicator_BOLL(df, N=26)
    kdj = KDJ(df)
    df = pd.concat([df, boll, kdj], axis=1)
    df.rename(columns={'symbol':'code'},inplace=True)
    return df


# def get_bi(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_bi(code, end_date=end_date, freq='30min'):
    #     ka = get_kline_analyze(code, start_date, end_date, freq)
    ka = get_kline_analyze(code, end_date, freq)
    return ka.bi_list


# def get_bi_pd(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_bi_pd(code, end_date=end_date, freq='30min'):
    #     bi = get_bi(code, start_date, end_date, freq)
    bi = get_bi(code, end_date, freq)
    df = pd.DataFrame(bi)
    return df


# def get_zs(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_zs(code, end_date=end_date, freq='30min'):
    #     ka = get_kline_analyze(code, start_date, end_date, freq)
    ka = get_kline_analyze(code, end_date, freq)
    return ka.zs


# def get_xd(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_xd(code, end_date=end_date, freq='30min'):
    #     ka = get_kline_analyze(code, start_date, end_date, freq)
    ka = get_kline_analyze(code, end_date, freq)
    return ka.xd_list


# def get_xd_pd(code, start_date=start_date, end_date=end_date, freq='30min'):
def get_xd_pd(code, end_date=end_date, freq='30min'):
    #     xd = get_xd(code, start_date, end_date, freq)
    xd = get_xd(code, end_date, freq)
    df = pd.DataFrame(xd)
    return df


# def get_chan_pd(code, start_date=start_date, end_date=end_date, freq='30min'):
# def get_chan_pd(code, end_date=end_date, freq='30min'):
# #     ka = get_kline_analyze(code, start_date, end_date, freq)
#     ka = get_kline_analyze(code, end_date, freq)
#     df = pd.DataFrame(ka.kline)
#     df = df[['dt','symbol','open','high','low','close','vol','fx','fx_mark','bi','xd']]
#     df.rename(columns={'symbol':'code'},inplace=True)
#     ma(df)
#     macd(df)
#     boll(df)
#     return df

def ruler_of_wave(code, end_date=end_date, freq='30min'):
    # 从线段的顶底点出发判断行情接下去的发展
    try:
        ka = get_kline_analyze(code, end_date, freq)
        xd = ka.xd_list
        bi = ka.bi_list
        #     xd = get_xd(code, end_date, freq)
        #     bi = get_bi(code, end_date, freq)
        # df_xd = pd.DataFrame(xd)
        # df_bi = pd.DataFrame(bi)
        if xd:
            # 计算波浪尺的最低点和随后的高低点，构成一个波浪上攻的结构
            # if df_xd.iloc[-1].fx_mark == 'd':
            if xd[-1]['fx_mark'] == 'd':
                # 上涨且回调不破前新高或涨幅起点
                if bi[-1]['bi'] < xd[-1]['xd'] or ka.latest_price < xd[-1]['xd']:
                    print(f'{code}--目前线段上涨笔下跌中，不操作')
                    return None

                print(f'{code}--目前线段上涨笔上涨中，值得观察')
                return code
                # first_value = df_xd.iloc[-1].xd
                # xd_time = df_xd.iloc[-1]['dt']
                # bi_min_row = df_bi.loc[df_bi['dt'] == xd_time]
                # bi_min_row_number = int(bi_min_row.index.values)
                # if bi_min_row_number < len(df_bi) - 2:
                #     second_value = df_bi.iloc[bi_min_row_number + 1].bi
                #     third_value = df_bi.iloc[bi_min_row_number + 2].bi
                # else:
                #     second_value = df_bi.iloc[bi_min_row_number - 1].bi
                #     third_value = df_bi.iloc[bi_min_row_number - 2].bi
                #     # 计算波浪尺的具体价位
                # first_wave_long = second_value - first_value
                # price0382 = round(first_wave_long * 0.382 + third_value, 2)
                # price0618 = round(first_wave_long * 0.618 + third_value, 2)
                # price0100 = round(first_wave_long * 1.000 + third_value, 2)
                # price1618 = round(first_wave_long * 1.618 + third_value, 2)
                # print('=' * 30)
                # print('第一浪的三个价位', first_value, second_value, third_value)
                # print('波浪尺由小到大的价位', price0382, price0618, price0100, price1618)
                # print(f'{code}的卖出价是{price1618}')
                #
                # # 比较现价和波浪尺的价格，判断卖出还是持有
                # df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(code)
                # if df.price.max() > price1618:
                #     print(f'{code}价位已到，可以卖出')
                # else:
                #     print(f'{code}价位未到，继续持有')

            else:
                # 上涨且回调不破前新高或涨幅起点
                if bi[-1]['bi'] > xd[-1]['xd'] or ka.latest_price > xd[-1]['xd']:
                    print(f'{code}--目前线段下跌但笔上涨中，值得关注')
                    return code

                print(f'{code}--目前线段下跌笔下跌中，不操作')
                return None
                # print('=' * 30)
                # print(f"分型是{df_xd.iloc[-1].fx_mark}")
                # print(f"{code}--目前处于下跌状态，不操作")
        else:
            # print('=' * 30)
            print(f'{code}--没有形成线段，目前处于不可判断阶段')
    except:
        print('没有取到数据')
        return None

def ruler_all_wave(stock, end_date=end_date):
    print('=' * 30)
    res1 = ruler_of_wave(stock,end_date=end_date,freq='1min')
    res5 = ruler_of_wave(stock,end_date=end_date,freq='5min')
    res15 = ruler_of_wave(stock,end_date=end_date,freq='15min')
    res30 = ruler_of_wave(stock,end_date=end_date,freq='30min')
    res60 = ruler_of_wave(stock,end_date=end_date,freq='60min')
    resD = ruler_of_wave(stock,end_date=end_date,freq='D')
    print('=' * 30)
    res_sum = [res1, res5, res15, res30, res60, resD]
    return [1 if i else 0 for i in res_sum]


# def get_name(stock, data=data):
#
#     return data[data.code == stock].name.values

def realtime_moneyflow(stock):
    try:
        data = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(stock)
        data['amount'] = data['price'] * data['vol']
        # the buy or sell towards 0--buy 1--sell 2--none
        money_buy = data[data.buyorsell == 0]['amount'].sum()
        money_sell = data[data.buyorsell == 1]['amount'].sum()
        net_money = money_buy - money_sell
    except:
        net_money = 0
    return net_money


def get_realtime_quote(code):
    """
    获取实时行情（价格、盘口、成交量等），支持基金和股票
    参数：
        code: str，证券代码
    返回：
        dict，包含 price、bid1、ask1、volume、amount、change_rate、timestamp 等字段
    """
    try:
        t = QA.QAFetch.QATdx.QA_fetch_get_stock_realtime(code)
        if t is None or t.empty:
            return {'code': code, 'error': 'No realtime data'}
        # 价格、盘口
        price = float(t['price'].iloc[0])
        bid1 = float(t['bid1'].iloc[0])
        ask1 = float(t['ask1'].iloc[0])
        volume = float(t['vol'].iloc[0])
        amount = float(t['amount'].iloc[0]) if 'amount' in t.columns else 0.0
        last_close = float(t['last_close'].iloc[0]) if 'last_close' in t.columns else price
        change_rate = ((price - last_close) / last_close * 100) if last_close else 0.0
        # 价格调整（如基金价格大于9，除以10）
        if price > 9:
            price = price / 10
            bid1 = bid1 / 10
            ask1 = ask1 / 10
        return {
            'code': code,
            'price': round(price, 3),
            'bid1': round(bid1, 3),
            'ask1': round(ask1, 3),
            'volume': int(volume),
            'amount': float(amount),
            'change_rate': round(change_rate, 2),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {'code': code, 'error': str(e)}


# 列表中逗号后需要空格
gua_dict = {'[0, 1, 1, 1, 1, 1]':'天风姤卦','[1, 0, 0, 0, 0, 1]':'山雷颐卦','[0, 1, 0, 0, 0, 0]':'地水师卦',
            '[1, 1, 0, 1, 1, 1]':'天泽履卦','[1, 1, 1, 1, 1, 1]':'乾卦','[0, 0, 0, 0, 0, 1]':'山地剥卦',
            '[0, 1, 1, 1, 1, 0]':'泽风大过','[0, 0, 1, 0, 0, 1]':'艮卦','[1, 1, 1, 0, 0, 1]':'山天大畜',
            '[1, 0, 1, 0, 0, 1]':'山火贲卦','[0, 0, 1, 1, 1, 0]':'泽山咸卦','[1, 1, 1, 0, 1, 1]':'风天小畜',
            '[0, 1, 1, 1, 0, 0]':'雷风恒卦','[1, 1, 0, 0, 1, 0]':'水泽节卦','[1, 1, 0, 1, 1, 0]':'兑为泽卦',
            '[0, 0, 1, 1, 0, 1]':'火山旅卦','[0, 0, 1, 0, 1, 0]':'水山蹇卦','[0, 0, 0, 1, 1, 1]':'天地否卦',
            '[0, 1, 1, 0, 0, 0]':'地风升卦','[1, 0, 1, 0, 0, 0]':'地火明夷','[0, 0, 0, 0, 0, 0]':'坤卦',
            '[0, 1, 0, 1, 1, 1]':'天水讼卦','[1, 0, 1, 1, 1, 1]':'天火同人','[0, 0, 0, 1, 1, 0]':'泽地萃卦',
            '[1, 1, 1, 1, 0, 0]':'雷天大壮','[1, 0, 0, 0, 0, 0]':'地雷复卦','[1, 0, 0, 1, 0, 1]':'火雷噬磕',
            '[0, 1, 0, 0, 1, 1]':'风水涣卦','[1, 0, 1, 0, 1, 1]':'风火家人','[0, 0, 0, 1, 0, 0]':'雷地豫卦',
            '[0, 0, 1, 1, 0, 0]':'雷山小过','[1, 0, 1, 1, 0, 0]':'雷火丰卦','[1, 1, 0, 0, 0, 1]':'山泽损卦',
            '[0, 1, 1, 0, 0, 1]':'山风蛊卦','[0, 1, 1, 0, 1, 1]':'巽为风卦','[1, 1, 0, 0, 0, 0]':'地泽临卦',
            '[1, 0, 0, 1, 1, 1]':'天雷无妄','[0, 0, 0, 0, 1, 1]':'风地观卦','[1, 1, 1, 1, 1, 0]':'泽天夬卦',
            '[0, 0, 0, 0, 1, 0]':'水地比卦','[0, 0, 1, 1, 1, 1]':'天山遁卦','[0, 0, 1, 0, 1, 1]':'风山渐卦',
            '[0, 1, 0, 0, 1, 0]':'坎为水卦','[0, 1, 0, 1, 0, 1]':'火水未济','[0, 1, 0, 1, 0, 0]':'雷水解卦',
            '[0, 1, 1, 0, 1, 0]':'水风井卦','[1, 1, 1, 0, 1, 0]':'水天需卦','[1, 1, 1, 1, 0, 1]':'火天大有',
            '[1, 0, 0, 0, 1, 1]':'风雷益卦','[1, 0, 0, 0, 1, 0]':'水雷屯卦','[0, 1, 0, 0, 0, 1]':'山水蒙卦',
            '[1, 1, 0, 0, 1, 1]':'风泽中孚','[1, 1, 0, 1, 0, 1]':'火泽睽卦','[1, 1, 0, 1, 0, 0]':'雷泽归妹',
            '[1, 0, 1, 1, 1, 0]':'泽火革卦','[1, 0, 1, 0, 1, 0]':'水火既济','[1, 0, 0, 1, 0, 0]':'震为雷卦',
            '[1, 0, 0, 1, 1, 0]':'泽雷随卦','[0, 0, 1, 0, 0, 0]':'地山谦卦','[0, 1, 0, 1, 1, 0]':'泽水困卦',
            '[1, 1, 1, 0, 0, 0]':'地天泰卦','[0, 1, 1, 1, 0, 1]':'火风鼎卦','[1, 0, 1, 1, 0, 1]':'离为火卦',
            '[0, 0, 0, 1, 0, 1]':'火地晋卦'}

gua_analyze_dict = {'天风姤卦':'得此卦者，阴长阳衰，诸事不顺，宜谨慎行事，更应注意桃色纠纷',
                    '山雷颐卦':'得此卦者，对于言语及饮食，均须谨慎。宜守正道，谨言慎行，心怀阴谋者会招灾祸。',
                    '地水师卦':'得此卦者，困难重重，忧心劳众，宜包容别人，艰苦努力，摒除一切困难。',
                    '天泽履卦':'得此卦者，困难时期，多坎坷不顺，万事不宜急进，须循序渐进，谨慎行事。',
                    '乾卦':'得此卦者，天行刚健，自强不息，名利双收之象，宜把握机会，争取成果。',
                    '山地剥卦':'得此卦者，时运不佳，多有损失，前进有阻，宜顺时而止，安份自守。',
                    '泽风大过':'得此卦者，身心不安，事不如意，却又强意而行，大有后悔之时，谨防官非及水险',
                    '艮卦':'得此卦者，前路受阻，不宜妄进，宜守待机。',
                    '山天大畜':'得此卦者，宜坚守正道，脚踏实地，务实行事，方可成就大业。切勿骄傲自满，目空一切。',
                    '山火贲卦':'得此卦者，表面风光，内在空虚，多虚少实。宜充实自己，稳重行事，量力而为。',
                    '泽山咸卦':'得此卦者，宜谦虚待人，则会吉祥如意，谋事可成。但勿为不正当感情而意乱情迷。',
                    '风天小畜':'得此卦者，力量薄弱，运势反覆，宜蓄养实力，静待时机，急进则有险，凡事须耐心推进。',
                    '雷风恒卦':'小吉：得此卦者，须立身正道，坚守不易，持续努力，必能亨通。缺少毅力，朝三暮四者则不会成功。',
                    '水泽节卦':'小凶：得此卦者，宜安分守己，切忌贪心不足，诸事必须节制，不宜过份，更要戒酒色。',
                    '兑为泽卦':'吉：得此卦者，多喜庆之事，人情和合，但应坚守正道，否则犯灾。',
                    '火山旅卦':'凶：得此卦者，事多变动，如在异乡，小事可成，大事难成，宜谨守常规。',
                    '水山蹇卦':'凶：得此卦者，身心忧苦，举步维艰，宜守正道，不可妄动，涉险境者会有灾难。',
                    '天地否卦':'得此卦者，万物闭塞之象，上下不合，诸事不顺，凡事宜忍，须待时运好转而有为。',
                    '地风升卦':'得此卦者，运气升腾，诸事皆积极向上发展，谋事有成，名利双收。',
                    '地火明夷':'得此卦者，时运不佳，事事劳苦，宜坚守正道，忍耐自重，等待时机。',
                    '坤卦':'得此卦者，宜顺从运势，以静制动，不宜独立谋事，顺从他人，一起合作，可成大事。',
                    '天水讼卦':'得此卦者，身心不安，事多不顺，与他人多争诉之事，宜修身养性，谨慎处事。',
                    '天火同人':'得此卦者，吉祥如意，与人合作共事更佳，上下同心，谋事有成。',
                    '泽地萃卦':'得此卦者，运气大好，能得到贵人的帮助，获利丰厚，无往不利。',
                    '雷天大壮':'得此卦者，运势过于强盛，宜心平气和，谨慎行事，否则必生过失。',
                    '地雷复卦':'得此卦者，时运好转，顺势而为，谋事可成，但不宜过于急进。',
                    '火雷噬磕':'得此卦者，事不遂心，纷争难免，诸事被阻，宜坚守常规，不为利诱，可保平安。',
                    '风水涣卦':'得此卦者，初有不顺，但终可解困，凡事宜小心则百事亨通，忌任性放纵。',
                    '风火家人':'得此卦者，与人合作共事者会有利，且多有喜事之象，家庭和睦者，能同心协力，发展事业。',
                    '雷地豫卦':'得此卦者，顺天应时，事事吉祥，可得长辈之助，但须防色难，切不可沉迷于声色欢场之中。',
                    '雷山小过':'得此卦者，诸事不利，宜行小事，不宜成大事，更防因自身的过失惹来是非争讼。',
                    '雷火丰卦':'得此卦者，运势正强，谋事可成，名利双收。但不宜过贪，要知足常乐，谨防乐极生悲，损财甚至火险。',
                    '山泽损卦':'得此卦者，损己利人，虽然开始会有所不顺，但付出总会有所回报，因祸得福之象。',
                    '山风蛊卦':'凶：得此卦者，艰难迷惑之时，事事不如意;宜大胆革新，奋发图强，艰苦努力，可转危为安。',
                    '巽为风卦':'得此卦者，运势起伏不定，宜随机应变，谦虚行事，则可得意外之收获。',
                    '地泽临卦':'得此卦者，好运来到，诸事如意，人情和合，但行事不宜过于急进。',
                    '天雷无妄':'得此卦者，顺其自然，守正道者，诸事皆宜。但行为不检者，必招灾祸。',
                    '风地观卦':'得此卦者，处身于变化之中，心神不宁，宜多观察入微，待机行事，切勿妄进。',
                    '泽天夬卦':'得此卦者，大运将过，困难将至，宜提高警惕，谨言慎行。',
                    '水地比卦':'得此卦者，可获朋友之助，众人之力，谋事有成，荣显之极。',
                    '天山遁卦':'得此卦者，宜退不宜进。退守可以保身，若轻举妄动则会招灾。宜谨言慎行，待机行事。',
                    '风山渐卦':'得此卦者，逐步开运，凡事宜循序渐进，则谋事可成，不宜急进，性急则败。',
                    '坎为水卦':'得此卦者，运气不佳，多难危险，事多困阻，宜谨言慎行，退守保安。',
                    '火水未济':'得此卦者，运势不通，诸事不能如愿，宜由小及大，稳步进取，要耐心去突破难关，则终可成功。',
                    '雷水解卦':'得此卦者，能解脱先前之困难，宜把握良机，求谋事业，出外谋事者更佳。',
                    '离为火卦':'得此卦者，宜谦虚谨慎，稳步进取，则前途光明。急进及意气用事者必有所损失。',
                    '地天泰卦':'得此卦者，否极泰来，鸿运当头，诸事皆顺，但须防乐极生悲。',
                    '火泽睽卦':'凶：得此卦者，运气不佳，水火不容，相互矛盾，诸事难成。',
                    '水风井卦':'得此卦者，宜修身养性，顺其自然，泰然处之，静有利，动则凶。',
                    '水天需卦':'得此卦者，时机尚未成熟，需要耐心等待，急进反会见凶。',
                    '火天大有':'得此卦者，正当好运，事事吉祥，大有收获，但需防物极必反，盛极转衰。',
                    '风雷益卦':'得此卦者，正当好运，奋发图进，得人帮助，能获名利',
                    '水雷屯卦':'得此卦者，身处困境，宜守不宜进，须多加辛苦努力，排除困难，方可通达，有初难后解之象。',
                    '山水蒙卦':'得此卦者，智慧犹如童蒙，不辨是非，迷失方向；若能顺贤师良友之教，启其聪明则亨通。',
                    '风泽中孚':'得此卦者，正直诚信者吉利，会得到朋友的帮助，谋事可成；心存邪念者则凶。',
                    '雷泽归妹':'得此卦者，困难之时，做事有违常理，灾祸不断。宜明察事理，修身养性，断绝妄念。',
                    '泽火革卦':'得此卦者，凡事均在变动之中，宜去旧立新，以应革新之象，则会吉祥。',
                    '水火既济':'得此卦者，事业有成，成功之象，但谨防盛极必衰，宜退守为吉，再进则凶。',
                    '震为雷卦':'得此卦者，奋发振作，大可有为，但表面风光，内恐有难，宜谨言慎行，以免损失。',
                    '泽雷随卦':'小吉：得此卦者，宜随大势，其事可成。凡事与他人多沟通交流，可名利双收。切不可坚持己见，专横者事不成。',
                    '地山谦卦':'得此卦者，吉利平安，步步高升。谦虚忍让者前途大好，骄横者必招败。谦受益，满招损。',
                    '泽水困卦':'得此卦者，陷入困境，事事不如意，宜坚守正道，等待时机。',
                    '火风鼎卦':'得此卦者，时运正佳，能得到朋友的帮助，取得不错的成就。与人合伙共事更佳。',
                    '火地晋卦':'得此卦者，如旭日东升，气运旺盛，收入颇丰，谋事可成，百事如意。'
                    }

gua_psychology_dict = {
    '山水蒙卦':'义欠仁道德-高欲-明欠聪智慧，合作-自持-现实；愚昧节义，凶狠不仁的憨厚蒙昧型人，特征呆滞，冷酷，这是一种外表憨蒙，内心有强烈优越感自尊心的人'
}

def judge_gua_by_ruler_of_wave(res):
    return gua_dict.get(str(res))

def gua_analyze(gua_name):
    return gua_analyze_dict.get(gua_name)


# def run_test(stock):
#     # df = get_czsc_pd(code=stock)
#     # print(df.tail())
#     # print('==' * 50)
#     res = ruler_all_wave(stock)
#     print(res)
#     print('==' * 50)
#     gua = judge_gua_by_ruler_of_wave(res)
#     name = get_name(stock)
#     if gua:
#         print(f'{name}今天的卦象：' + gua)
#     print(gua_analyze(gua))


def trade_days_list(start_date, end_date):
    df_day = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date)
    trade_days = df_day[df_day.is_open == 1].cal_date.to_list()
    return [x[:4]+'-'+x[4:6]+'-'+x[6:] for x in trade_days]

def gua_backtest(stock,start_date, end_date):
    day_result = []
    trade_days = trade_days_list(start_date, end_date)
    for day in trade_days:
        res = judge_gua_by_ruler_of_wave(ruler_all_wave(stock, day))
        new_dict = {day: res}
        day_result.append(new_dict)
    return day_result


def test(df):
    df['gua'] = df.code.apply(lambda x: ruler_all_wave(x))
    return df


if __name__ == '__main__':

    # df = get_chan_pd(code='600267', start_date="2020-03-01", end_date="2020-04-17", freq='30min')
    # sa = get_solid_analyze(code='600267', start_date="2020-03-01", end_date="2020-04-17")
    # sa = get_solid_analyze(code='600267')
    # b3, _ = sa.is_third_buy('5分钟')
    #     bs_list = bs_point_min('600267',freq='30min')
    #     print(bs_list)


    # df = get_czsc_pd(code='002475')
    # print(df.tail())
    # print('=='*50)
    # res = ruler_all_wave('002475')
    # print(res)
    # print('=='*50)
    # gua = judge_gua_by_ruler_of_wave(res)
    # if gua:
    #     print('立讯精密今天的卦象：'+gua)
    # print(gua_analyze(judge_gua_by_ruler_of_wave(res)))
    # stocks = ['600176', '601319','600048','002475','000998','002142']
    # proce1 = multiprocessing.Process(target=run_test, args=('600048',))
    # proce2 = multiprocessing.Process(target=run_test, args=('600176',))
    # stocks = ['300059','600584']
    # for stock in stocks:
    #     run_test(stock)
    # proce1.start()
    # proce2.start()
    today = datetime.now().strftime('%Y%m%d')
    open_time = datetime.strptime(today + ' 09:35:00', '%Y%m%d %H:%M:%S')
    close_time = datetime.strptime(today + ' 15:05:00', '%Y%m%d %H:%M:%S')

    # block = QA.QAFetch.QATdx.QA_fetch_get_stock_block()
    # df_feature = block[block.blockname == '中证200']
    while datetime.now() >= open_time and datetime.now() <= close_time:
    # for i in range(1):
        start = time.time()

        df = ts.get_today_all()
        df.sort_values(by='changepercent', ascending=False, inplace=True)
        df.reset_index(drop=True, inplace=True)
        # df = df.drop(index=(df.loc[(df['code'] == '688160')].index))
        # df = df.drop(index=(df.loc[(df['code'] == '300884')].index))
        # df = df.drop(index=(df.loc[(df['code'] == '605007')].index))
        # df = df.drop(index=(df.loc[(df['code'] == '688057')].index))
        df_feature = df[0:100]
        df_feature_1 = df[100:200]
        # df['mf'] = df.code.apply(lambda x: realtime_moneyflow(x))
        # df_mf = df.sort_values(by='mf')
        # df_feature = df.tail(200)

        # test = multiprocessing.Process(test, args=(df_feature,))
        # test_1 = multiprocessing.Process(test, args=(df_feature_1,))

        df_feature['gua'] = df_feature.code.apply(lambda x: ruler_all_wave(x))
        df_feature['gua_name'] = df_feature.gua.apply(lambda x: judge_gua_by_ruler_of_wave(x))
        df_feature['gua_analyze'] = df_feature.gua_name.apply(lambda x: gua_analyze(x))
        print(df_feature[['code', 'name', 'gua', 'gua_name', 'gua_analyze']])
        print('='*50)
        print('离为火卦')
        print(df_feature[df_feature.gua_name == '离为火卦'].name)
        print('='*50)
        print('兑为泽卦')
        print(df_feature[df_feature.gua_name == '兑为泽卦'].name)
        print('=' * 50)
        print('山天大畜')
        print(df_feature[df_feature.gua_name == '山天大畜'].name)
        end = time.time()
        wasted_time = end - start
        print('花费的时间是{}'.format(wasted_time))
        time.sleep(60)

        all_count = {}
        for i in gua_dict.values():
            gua_count = len(df_feature[df_feature.gua_name == i].code.to_list())
            all_count[i] = gua_count
        df_csv = pd.DataFrame(all_count, index=[end_date])
        df_csv.to_csv('gua_test.csv', encoding='gbk', mode='a',header=False)




























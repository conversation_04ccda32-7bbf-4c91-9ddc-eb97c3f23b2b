#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多策略回测系统
整合enhanced_backtest.py的优秀功能到主系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入增强版回测功能
sys.path.append(os.path.dirname(os.path.dirname(current_dir)))
try:
    from enhanced_backtest import EnhancedBacktest
    ENHANCED_BACKTEST_AVAILABLE = True
except ImportError:
    ENHANCED_BACKTEST_AVAILABLE = False

from core.enhanced_data_fetcher import get_kline
from system.enhanced_display_system import EnhancedDisplaySystem


class MultiStrategyBacktestSystem:
    """多策略回测系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.display_system = EnhancedDisplaySystem()
        
        # 可用策略列表
        self.available_strategies = [
            'ma_crossover',      # 移动平均交叉
            'macd_rsi',         # MACD+RSI组合
            'bollinger_kdj',    # 布林带+KDJ
            'momentum_volume'   # 动量+成交量
        ]
        
        # 策略描述
        self.strategy_descriptions = {
            'ma_crossover': '移动平均交叉策略 - 基于MA5和MA20的金叉死叉',
            'macd_rsi': 'MACD+RSI组合策略 - 趋势与超买超卖结合',
            'bollinger_kdj': '布林带+KDJ策略 - 波动率与随机指标结合',
            'momentum_volume': '动量+成交量策略 - 价格动量与成交量确认'
        }
    
    def run_single_strategy_backtest(self, fund_code: str, strategy_name: str, 
                                   max_position_pct: float = 0.8, 
                                   stop_loss_pct: float = 0.05) -> Dict[str, Any]:
        """运行单个策略的回测"""
        print(f"🎯 开始{strategy_name}策略回测 - {fund_code}")
        
        try:
            if not ENHANCED_BACKTEST_AVAILABLE:
                # 如果增强版回测不可用，使用简化版本
                return self._run_simplified_backtest(fund_code, strategy_name)
            
            # 创建增强版回测实例
            backtest = EnhancedBacktest(fund_code, initial_capital=self.initial_capital)
            
            # 获取数据
            df = backtest.get_real_data(fund_code, freq='D')
            if df is None or df.empty:
                raise ValueError(f"无法获取{fund_code}的数据")
            
            # 计算技术指标
            df = backtest.calculate_advanced_indicators(df)
            
            # 运行策略
            df = backtest.run_strategy(df, strategy_name)
            
            # 执行回测
            results_df = backtest.execute_backtest_with_risk_management(
                df, max_position_pct=max_position_pct, stop_loss_pct=stop_loss_pct
            )
            
            # 计算指标
            metrics = backtest.calculate_comprehensive_metrics(results_df, df)
            
            # 生成报告
            report = backtest.generate_detailed_report(metrics, strategy_name)
            
            return {
                'fund_code': fund_code,
                'strategy_name': strategy_name,
                'status': 'success',
                'metrics': metrics,
                'report': report,
                'trades': backtest.trades,
                'portfolio_values': backtest.portfolio_values
            }
            
        except Exception as e:
            print(f"❌ {strategy_name}策略回测失败: {e}")
            return {
                'fund_code': fund_code,
                'strategy_name': strategy_name,
                'status': 'failed',
                'error': str(e)
            }
    
    def _run_simplified_backtest(self, fund_code: str, strategy_name: str) -> Dict[str, Any]:
        """简化版回测（当增强版不可用时）"""
        try:
            # 获取数据
            df = get_kline(fund_code, freq='D')
            if df is None or df.empty:
                raise ValueError(f"无法获取{fund_code}的数据")
            
            # 简单的买入持有策略作为基准
            initial_price = df.iloc[0]['close']
            final_price = df.iloc[-1]['close']
            total_return = (final_price - initial_price) / initial_price
            
            # 简化的指标
            metrics = {
                'total_return': total_return,
                'annual_return': total_return * (252 / len(df)),
                'volatility': df['close'].pct_change().std() * np.sqrt(252),
                'sharpe_ratio': 0.0,  # 简化计算
                'max_drawdown': 0.0,  # 简化计算
                'total_trades': 1,
                'win_rate': 1.0 if total_return > 0 else 0.0
            }
            
            return {
                'fund_code': fund_code,
                'strategy_name': f'{strategy_name}_simplified',
                'status': 'success',
                'metrics': metrics,
                'note': 'simplified_backtest'
            }
            
        except Exception as e:
            return {
                'fund_code': fund_code,
                'strategy_name': strategy_name,
                'status': 'failed',
                'error': str(e)
            }
    
    def run_multi_strategy_comparison(self, fund_code: str, 
                                    strategies: Optional[List[str]] = None,
                                    max_position_pct: float = 0.8,
                                    stop_loss_pct: float = 0.05) -> Dict[str, Any]:
        """运行多策略对比"""
        if strategies is None:
            strategies = self.available_strategies
        
        print(f"🎯 开始多策略对比回测 - {fund_code}")
        print(f"📊 测试策略: {', '.join(strategies)}")
        print("="*80)
        
        results = []
        
        for strategy in strategies:
            if strategy not in self.available_strategies:
                print(f"⚠️ 跳过未知策略: {strategy}")
                continue
            
            print(f"\n🔄 测试策略: {strategy}")
            print(f"📝 策略描述: {self.strategy_descriptions.get(strategy, '无描述')}")
            print("-" * 60)
            
            result = self.run_single_strategy_backtest(
                fund_code, strategy, max_position_pct, stop_loss_pct
            )
            results.append(result)
            
            # 显示单个策略结果
            if result['status'] == 'success':
                metrics = result['metrics']
                print(f"✅ {strategy}策略完成:")
                print(f"   总收益率: {metrics.get('total_return', 0):.2%}")
                print(f"   年化收益: {metrics.get('annual_return', 0):.2%}")
                print(f"   夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
                print(f"   最大回撤: {metrics.get('max_drawdown', 0):.2%}")
                print(f"   交易次数: {metrics.get('total_trades', 0)}")
                print(f"   胜率: {metrics.get('win_rate', 0):.2%}")
            else:
                print(f"❌ {strategy}策略失败: {result.get('error', '未知错误')}")
        
        # 生成对比报告
        comparison_report = self._generate_comparison_report(fund_code, results)
        
        # 显示对比结果
        self._display_comparison_results(comparison_report)
        
        return comparison_report
    
    def _generate_comparison_report(self, fund_code: str, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成对比报告"""
        successful_results = [r for r in results if r['status'] == 'success']
        
        if not successful_results:
            return {
                'fund_code': fund_code,
                'status': 'failed',
                'error': 'No successful strategy results'
            }
        
        # 提取对比数据
        comparison_data = []
        for result in successful_results:
            metrics = result['metrics']
            comparison_data.append({
                'strategy_name': result['strategy_name'],
                'total_return': metrics.get('total_return', 0),
                'annual_return': metrics.get('annual_return', 0),
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'max_drawdown': metrics.get('max_drawdown', 0),
                'win_rate': metrics.get('win_rate', 0),
                'total_trades': metrics.get('total_trades', 0),
                'volatility': metrics.get('volatility', 0)
            })
        
        # 找出最佳策略
        best_sharpe = max(comparison_data, key=lambda x: x['sharpe_ratio'])
        best_return = max(comparison_data, key=lambda x: x['total_return'])
        best_win_rate = max(comparison_data, key=lambda x: x['win_rate'])
        
        return {
            'fund_code': fund_code,
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'strategies_tested': len(comparison_data),
            'comparison_data': comparison_data,
            'best_strategies': {
                'best_sharpe': best_sharpe,
                'best_return': best_return,
                'best_win_rate': best_win_rate
            },
            'detailed_results': successful_results
        }
    
    def _display_comparison_results(self, comparison_report: Dict[str, Any]) -> None:
        """显示对比结果"""
        if comparison_report['status'] != 'success':
            print(f"❌ 对比报告生成失败: {comparison_report.get('error', '未知错误')}")
            return
        
        print("\n" + "="*80)
        print(f"📊 多策略回测对比报告 - {comparison_report['fund_code']}")
        print("="*80)
        
        # 显示策略对比表格
        comparison_data = comparison_report['comparison_data']
        if comparison_data:
            # 创建DataFrame用于美观显示
            df = pd.DataFrame(comparison_data)
            
            # 格式化数值列
            df['总收益率'] = df['total_return'].apply(lambda x: f"{x:.2%}")
            df['年化收益'] = df['annual_return'].apply(lambda x: f"{x:.2%}")
            df['夏普比率'] = df['sharpe_ratio'].apply(lambda x: f"{x:.3f}")
            df['最大回撤'] = df['max_drawdown'].apply(lambda x: f"{x:.2%}")
            df['胜率'] = df['win_rate'].apply(lambda x: f"{x:.2%}")
            df['交易次数'] = df['total_trades']
            
            # 选择要显示的列
            display_df = df[['strategy_name', '总收益率', '年化收益', '夏普比率', '最大回撤', '胜率', '交易次数']]
            display_df.columns = ['策略名称', '总收益率', '年化收益', '夏普比率', '最大回撤', '胜率', '交易次数']
            
            print(display_df.to_string(index=False))
        
        # 显示最佳策略
        best_strategies = comparison_report['best_strategies']
        print(f"\n🏆 最佳策略排行:")
        print(f"   🥇 最佳夏普比率: {best_strategies['best_sharpe']['strategy_name']} ({best_strategies['best_sharpe']['sharpe_ratio']:.3f})")
        print(f"   🥈 最佳收益率: {best_strategies['best_return']['strategy_name']} ({best_strategies['best_return']['total_return']:.2%})")
        print(f"   🥉 最佳胜率: {best_strategies['best_win_rate']['strategy_name']} ({best_strategies['best_win_rate']['win_rate']:.2%})")
        
        # 策略建议
        print(f"\n💡 策略建议:")
        best_overall = best_strategies['best_sharpe']  # 以夏普比率为主要评判标准
        print(f"   推荐策略: {best_overall['strategy_name']}")
        print(f"   推荐理由: 夏普比率最高({best_overall['sharpe_ratio']:.3f})，风险调整后收益最佳")
        
        if best_overall['max_drawdown'] < -0.1:
            print(f"   ⚠️ 注意: 该策略最大回撤较大({best_overall['max_drawdown']:.2%})，建议控制仓位")
        
        if best_overall['total_trades'] > 50:
            print(f"   ⚠️ 注意: 该策略交易频繁({best_overall['total_trades']}次)，注意交易成本")
    
    def save_comparison_report(self, comparison_report: Dict[str, Any], 
                             filename: Optional[str] = None) -> str:
        """保存对比报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            fund_code = comparison_report.get('fund_code', 'unknown')
            filename = f'multi_strategy_backtest_{fund_code}_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 多策略回测报告已保存: {filename}")
        return filename
    
    def run_portfolio_backtest(self, fund_codes: List[str], 
                             strategy_name: str = 'ma_crossover') -> Dict[str, Any]:
        """运行投资组合回测"""
        print(f"🎯 开始投资组合回测")
        print(f"📊 基金列表: {', '.join(fund_codes)}")
        print(f"🎯 使用策略: {strategy_name}")
        print("="*80)
        
        portfolio_results = {}
        
        for fund_code in fund_codes:
            print(f"\n🔄 回测基金: {fund_code}")
            result = self.run_single_strategy_backtest(fund_code, strategy_name)
            portfolio_results[fund_code] = result
            
            if result['status'] == 'success':
                metrics = result['metrics']
                print(f"✅ {fund_code}: 收益率{metrics.get('total_return', 0):.2%}, 夏普{metrics.get('sharpe_ratio', 0):.3f}")
            else:
                print(f"❌ {fund_code}: 回测失败")
        
        # 计算投资组合整体表现
        portfolio_summary = self._calculate_portfolio_performance(portfolio_results)
        
        # 显示投资组合结果
        self._display_portfolio_results(portfolio_summary)
        
        return {
            'portfolio_summary': portfolio_summary,
            'individual_results': portfolio_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_portfolio_performance(self, portfolio_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """计算投资组合整体表现"""
        successful_results = {k: v for k, v in portfolio_results.items() if v['status'] == 'success'}
        
        if not successful_results:
            return {'status': 'failed', 'error': 'No successful fund results'}
        
        # 等权重投资组合
        total_return = np.mean([r['metrics']['total_return'] for r in successful_results.values()])
        annual_return = np.mean([r['metrics']['annual_return'] for r in successful_results.values()])
        sharpe_ratio = np.mean([r['metrics']['sharpe_ratio'] for r in successful_results.values()])
        max_drawdown = np.mean([r['metrics']['max_drawdown'] for r in successful_results.values()])
        win_rate = np.mean([r['metrics']['win_rate'] for r in successful_results.values()])
        
        return {
            'status': 'success',
            'funds_count': len(successful_results),
            'total_funds_tested': len(portfolio_results),
            'portfolio_metrics': {
                'total_return': total_return,
                'annual_return': annual_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate
            },
            'best_fund': max(successful_results.items(), key=lambda x: x[1]['metrics']['sharpe_ratio']),
            'worst_fund': min(successful_results.items(), key=lambda x: x[1]['metrics']['sharpe_ratio'])
        }
    
    def _display_portfolio_results(self, portfolio_summary: Dict[str, Any]) -> None:
        """显示投资组合结果"""
        if portfolio_summary['status'] != 'success':
            print(f"❌ 投资组合分析失败: {portfolio_summary.get('error', '未知错误')}")
            return
        
        print("\n" + "="*80)
        print("📊 投资组合回测结果")
        print("="*80)
        
        metrics = portfolio_summary['portfolio_metrics']
        print(f"💰 投资组合表现 (等权重):")
        print(f"   总收益率: {metrics['total_return']:.2%}")
        print(f"   年化收益: {metrics['annual_return']:.2%}")
        print(f"   夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"   最大回撤: {metrics['max_drawdown']:.2%}")
        print(f"   平均胜率: {metrics['win_rate']:.2%}")
        
        print(f"\n📊 基金统计:")
        print(f"   成功回测: {portfolio_summary['funds_count']}/{portfolio_summary['total_funds_tested']}")
        
        best_fund = portfolio_summary['best_fund']
        worst_fund = portfolio_summary['worst_fund']
        print(f"   最佳基金: {best_fund[0]} (夏普: {best_fund[1]['metrics']['sharpe_ratio']:.3f})")
        print(f"   最差基金: {worst_fund[0]} (夏普: {worst_fund[1]['metrics']['sharpe_ratio']:.3f})")


def main():
    """主函数 - 演示多策略回测系统"""
    print("🎯 多策略回测系统演示")
    print("="*80)
    
    # 创建回测系统
    backtest_system = MultiStrategyBacktestSystem(initial_capital=100000)
    
    # 测试基金
    test_fund = '518880'  # 黄金ETF
    
    try:
        # 1. 单策略回测
        print("📊 第一步: 单策略回测演示")
        single_result = backtest_system.run_single_strategy_backtest(test_fund, 'ma_crossover')
        
        # 2. 多策略对比
        print("\n📊 第二步: 多策略对比演示")
        comparison_result = backtest_system.run_multi_strategy_comparison(test_fund)
        
        # 保存报告
        if comparison_result['status'] == 'success':
            backtest_system.save_comparison_report(comparison_result)
        
        # 3. 投资组合回测
        print("\n📊 第三步: 投资组合回测演示")
        portfolio_funds = ['518880', '159567', '513500']
        portfolio_result = backtest_system.run_portfolio_backtest(portfolio_funds, 'ma_crossover')
        
        print("\n🎉 多策略回测系统演示完成！")
        
    except Exception as e:
        print(f"❌ 演示执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
import pandas as pd
import numpy as np
from typing import Tuple, List, Dict, Optional
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
warnings.filterwarnings('ignore')


class TimeSeriesProcessor:
    """
    时间序列处理模块
    负责时间窗口创建、序列切分、数据泄露防护
    """
    
    def __init__(self, sequence_length: int = 30, test_size: float = 0.2, 
                 validation_size: float = 0.1, scaler_type: str = 'standard'):
        """
        初始化时间序列处理器
        
        Args:
            sequence_length: 序列长度（时间窗口大小）
            test_size: 测试集比例
            validation_size: 验证集比例
            scaler_type: 标准化类型 ('standard', 'minmax', 'none')
        """
        self.sequence_length = sequence_length
        self.test_size = test_size
        self.validation_size = validation_size
        self.scaler_type = scaler_type
        self.feature_scaler = None
        self.target_scaler = None
        self.processing_log = []
        
    def create_sequences(self, X: np.ndarray, y: np.ndarray, 
                        sequence_length: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列序列数据
        
        Args:
            X: 特征数据 (samples, features)
            y: 目标数据 (samples,)
            sequence_length: 序列长度，如果为None则使用初始化时的值
            
        Returns:
            (序列化的X, 对应的y)
        """
        seq_len = sequence_length or self.sequence_length
        
        if len(X) < seq_len:
            raise ValueError(f"Data length {len(X)} is less than sequence length {seq_len}")
        
        # 创建序列
        X_sequences = []
        y_sequences = []
        
        for i in range(seq_len, len(X)):
            # 获取序列窗口
            X_seq = X[i-seq_len:i]
            y_seq = y[i]
            
            X_sequences.append(X_seq)
            y_sequences.append(y_seq)
        
        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)
        
        self.processing_log.append(f"Created {len(X_sequences)} sequences with length {seq_len}")
        
        return X_sequences, y_sequences
    
    def create_multi_step_sequences(self, X: np.ndarray, y: np.ndarray, 
                                  prediction_horizon: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建多步预测序列
        
        Args:
            X: 特征数据
            y: 目标数据
            prediction_horizon: 预测时间窗口
            
        Returns:
            (序列化的X, 多步预测的y)
        """
        X_sequences = []
        y_sequences = []
        
        for i in range(self.sequence_length, len(X) - prediction_horizon + 1):
            X_seq = X[i-self.sequence_length:i]
            y_seq = y[i:i+prediction_horizon]
            
            X_sequences.append(X_seq)
            y_sequences.append(y_seq)
        
        self.processing_log.append(f"Created multi-step sequences with horizon {prediction_horizon}")
        
        return np.array(X_sequences), np.array(y_sequences)
    
    def time_aware_split(self, X: np.ndarray, y: np.ndarray, 
                        datetime_index: Optional[pd.DatetimeIndex] = None) -> Tuple:
        """
        按时间顺序进行训练、验证、测试集分割
        
        Args:
            X: 特征数据
            y: 目标数据
            datetime_index: 时间索引
            
        Returns:
            (X_train, X_val, X_test, y_train, y_val, y_test, split_info)
        """
        total_samples = len(X)
        
        # 计算分割点
        test_start = int(total_samples * (1 - self.test_size))
        val_start = int(test_start * (1 - self.validation_size))
        
        # 按时间顺序分割
        X_train = X[:val_start]
        X_val = X[val_start:test_start]
        X_test = X[test_start:]
        
        y_train = y[:val_start]
        y_val = y[val_start:test_start]
        y_test = y[test_start:]
        
        # 记录分割信息
        split_info = {
            'total_samples': total_samples,
            'train_samples': len(X_train),
            'val_samples': len(X_val),
            'test_samples': len(X_test),
            'train_ratio': len(X_train) / total_samples,
            'val_ratio': len(X_val) / total_samples,
            'test_ratio': len(X_test) / total_samples
        }
        
        # 如果有时间索引，添加时间范围信息
        if datetime_index is not None:
            split_info.update({
                'train_period': (datetime_index[0], datetime_index[val_start-1]),
                'val_period': (datetime_index[val_start], datetime_index[test_start-1]),
                'test_period': (datetime_index[test_start], datetime_index[-1])
            })
        
        self.processing_log.append(f"Time-aware split: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test, split_info
    
    def prevent_data_leakage(self, df: pd.DataFrame, target_col: str, 
                           future_window: int = 1) -> pd.DataFrame:
        """
        防止数据泄露
        
        Args:
            df: 输入数据框
            target_col: 目标列名
            future_window: 未来信息窗口大小
            
        Returns:
            防泄露处理后的数据框
        """
        df_processed = df.copy()
        
        # 检查是否存在未来信息泄露
        leakage_columns = []
        
        for col in df_processed.columns:
            if col != target_col:
                # 检查特征与未来目标值的相关性
                if len(df_processed) > future_window:
                    current_feature = df_processed[col][:-future_window]
                    future_target = df_processed[target_col][future_window:]
                    
                    # 计算相关性
                    if len(current_feature.dropna()) > 10 and len(future_target.dropna()) > 10:
                        correlation = current_feature.corr(future_target)
                        if abs(correlation) > 0.8:  # 高相关性阈值
                            leakage_columns.append(col)
        
        # 移除可能存在泄露的列
        if leakage_columns:
            df_processed = df_processed.drop(columns=leakage_columns)
            self.processing_log.append(f"Removed potential leakage columns: {leakage_columns}")
        
        return df_processed
    
    def apply_scaling(self, X_train: np.ndarray, X_val: np.ndarray, X_test: np.ndarray,
                     y_train: np.ndarray, y_val: np.ndarray, y_test: np.ndarray) -> Tuple:
        """
        应用特征缩放
        
        Args:
            X_train, X_val, X_test: 特征数据
            y_train, y_val, y_test: 目标数据
            
        Returns:
            缩放后的数据
        """
        if self.scaler_type == 'none':
            return X_train, X_val, X_test, y_train, y_val, y_test
        
        # 选择缩放器
        if self.scaler_type == 'standard':
            self.feature_scaler = StandardScaler()
            self.target_scaler = StandardScaler()
        elif self.scaler_type == 'minmax':
            self.feature_scaler = MinMaxScaler()
            self.target_scaler = MinMaxScaler()
        
        # 对特征进行缩放
        if len(X_train.shape) == 3:  # 序列数据 (samples, timesteps, features)
            # 重塑为2D进行缩放
            original_shape = X_train.shape
            X_train_2d = X_train.reshape(-1, X_train.shape[-1])
            X_val_2d = X_val.reshape(-1, X_val.shape[-1])
            X_test_2d = X_test.reshape(-1, X_test.shape[-1])
            
            # 拟合并转换
            X_train_scaled_2d = self.feature_scaler.fit_transform(X_train_2d)
            X_val_scaled_2d = self.feature_scaler.transform(X_val_2d)
            X_test_scaled_2d = self.feature_scaler.transform(X_test_2d)
            
            # 重塑回原始形状
            X_train_scaled = X_train_scaled_2d.reshape(original_shape)
            X_val_scaled = X_val_scaled_2d.reshape(X_val.shape)
            X_test_scaled = X_test_scaled_2d.reshape(X_test.shape)
            
        else:  # 2D数据
            X_train_scaled = self.feature_scaler.fit_transform(X_train)
            X_val_scaled = self.feature_scaler.transform(X_val)
            X_test_scaled = self.feature_scaler.transform(X_test)
        
        # 对目标进行缩放
        y_train_scaled = self.target_scaler.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_val_scaled = self.target_scaler.transform(y_val.reshape(-1, 1)).flatten()
        y_test_scaled = self.target_scaler.transform(y_test.reshape(-1, 1)).flatten()
        
        self.processing_log.append(f"Applied {self.scaler_type} scaling")
        
        return X_train_scaled, X_val_scaled, X_test_scaled, y_train_scaled, y_val_scaled, y_test_scaled
    
    def inverse_transform_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """
        反变换预测结果
        
        Args:
            predictions: 缩放后的预测结果
            
        Returns:
            原始尺度的预测结果
        """
        if self.target_scaler is None:
            return predictions
        
        return self.target_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
    
    def create_rolling_windows(self, df: pd.DataFrame, window_size: int, 
                             step_size: int = 1) -> List[pd.DataFrame]:
        """
        创建滚动窗口
        
        Args:
            df: 输入数据框
            window_size: 窗口大小
            step_size: 步长
            
        Returns:
            滚动窗口列表
        """
        windows = []
        
        for i in range(0, len(df) - window_size + 1, step_size):
            window = df.iloc[i:i + window_size].copy()
            windows.append(window)
        
        self.processing_log.append(f"Created {len(windows)} rolling windows")
        
        return windows
    
    def validate_temporal_consistency(self, df: pd.DataFrame, time_col: str = 'dt') -> Dict:
        """
        验证时间一致性
        
        Args:
            df: 输入数据框
            time_col: 时间列名
            
        Returns:
            验证报告
        """
        validation_report = {}
        
        if time_col not in df.columns:
            validation_report['error'] = f"Time column '{time_col}' not found"
            return validation_report
        
        # 检查时间排序
        is_sorted = df[time_col].is_monotonic_increasing
        validation_report['is_time_sorted'] = is_sorted
        
        # 检查重复时间
        duplicates = df[time_col].duplicated().sum()
        validation_report['duplicate_timestamps'] = duplicates
        
        # 检查时间间隔
        time_diffs = df[time_col].diff().dropna()
        validation_report['time_intervals'] = {
            'min': time_diffs.min(),
            'max': time_diffs.max(),
            'mean': time_diffs.mean(),
            'std': time_diffs.std()
        }
        
        # 检查缺失时间点
        if len(time_diffs) > 1:
            expected_interval = time_diffs.mode().iloc[0] if len(time_diffs.mode()) > 0 else time_diffs.median()
            large_gaps = (time_diffs > expected_interval * 2).sum()
            validation_report['large_time_gaps'] = large_gaps
        
        self.processing_log.append("Temporal consistency validation completed")
        
        return validation_report
    
    def timeseries_processing_pipeline(self, df: pd.DataFrame, target_col: str,
                                     time_col: str = 'dt') -> Tuple[Dict, Dict]:
        """
        完整的时间序列处理流水线
        
        Args:
            df: 输入数据框
            target_col: 目标列名
            time_col: 时间列名
            
        Returns:
            (处理后的数据字典, 处理报告)
        """
        self.processing_log.clear()
        self.processing_log.append("Starting timeseries processing pipeline")
        
        # 1. 验证时间一致性
        validation_report = self.validate_temporal_consistency(df, time_col)
        
        # 2. 防止数据泄露
        df_clean = self.prevent_data_leakage(df, target_col)
        
        # 3. 准备特征和目标
        feature_cols = [col for col in df_clean.columns if col not in [target_col, time_col]]
        X = df_clean[feature_cols].values
        y = df_clean[target_col].values
        datetime_index = pd.to_datetime(df_clean[time_col]) if time_col in df_clean.columns else None
        
        # 4. 创建序列
        X_seq, y_seq = self.create_sequences(X, y)
        
        # 5. 时间序列分割
        X_train, X_val, X_test, y_train, y_val, y_test, split_info = self.time_aware_split(
            X_seq, y_seq, datetime_index)
        
        # 6. 应用缩放
        X_train_scaled, X_val_scaled, X_test_scaled, y_train_scaled, y_val_scaled, y_test_scaled = self.apply_scaling(
            X_train, X_val, X_test, y_train, y_val, y_test)
        
        # 组织处理后的数据
        processed_data = {
            'X_train': X_train_scaled,
            'X_val': X_val_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train_scaled,
            'y_val': y_val_scaled,
            'y_test': y_test_scaled,
            'feature_columns': feature_cols,
            'datetime_index': datetime_index
        }
        
        # 生成处理报告
        processing_report = {
            'sequence_length': self.sequence_length,
            'split_info': split_info,
            'validation_report': validation_report,
            'scaler_type': self.scaler_type,
            'processing_log': self.processing_log.copy()
        }
        
        self.processing_log.append("Timeseries processing pipeline completed")
        
        return processed_data, processing_report 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证tensorflow依赖已移除
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试导入...")
    
    try:
        # 测试网络架构导入
        from fund_trading_system_v3.4_backtest_czsc.backtest.network_architecture import NetworkArchitecture
        print("   ✅ NetworkArchitecture 导入成功")
        
        # 测试高级回测系统导入
        from fund_trading_system_v3.4_backtest_czsc.backtest.advanced_backtest_system import AdvancedBacktestSystem
        print("   ✅ AdvancedBacktestSystem 导入成功")
        
        # 测试ML增强回测引擎导入
        from fund_trading_system_v3.4_backtest_czsc.backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        print("   ✅ MLEnhancedBacktestEngine 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_network_architecture():
    """测试网络架构功能"""
    print("\n🔧 测试网络架构...")
    
    try:
        from fund_trading_system_v3.4_backtest_czsc.backtest.network_architecture import NetworkArchitecture
        
        # 创建网络架构
        arch = NetworkArchitecture(input_shape=(30, 10), output_dim=1)
        print("   ✅ NetworkArchitecture 创建成功")
        
        # 测试LSTM模型构建
        model = arch.build_lstm_model([64, 32])
        print(f"   ✅ LSTM模型构建成功: {type(model).__name__}")
        
        # 测试模型摘要
        summary = arch.get_model_summary(model)
        print(f"   ✅ 模型摘要: {summary['model_type']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 网络架构测试失败: {e}")
        return False

def test_ml_backtest_engine():
    """测试ML回测引擎"""
    print("\n🚀 测试ML回测引擎...")
    
    try:
        import pandas as pd
        import numpy as np
        from fund_trading_system_v3.4_backtest_czsc.backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        data = pd.DataFrame({
            'dt': dates,
            'open': np.random.uniform(95, 105, 100),
            'high': np.random.uniform(100, 110, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(95, 105, 100),
            'volume': np.random.randint(100000, 1000000, 100)
        })
        
        # 创建ML回测引擎
        ml_config = {
            'model_type': 'random_forest',
            'sequence_length': 10,
            'min_train_samples': 50,
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True
            }
        }
        
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=ml_config
        )
        print("   ✅ MLEnhancedBacktestEngine 创建成功")
        
        # 测试特征准备
        features = ml_engine.prepare_features(data)
        print(f"   ✅ 特征准备成功: {len(features.columns)} 个特征")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ML回测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 TensorFlow依赖移除验证测试")
    print("="*50)
    
    tests = [
        ("导入测试", test_imports),
        ("网络架构测试", test_network_architecture),
        ("ML回测引擎测试", test_ml_backtest_engine),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！TensorFlow依赖已成功移除！")
        print("\n💡 系统现在使用:")
        print("   ✅ PyTorch (如果可用)")
        print("   ✅ scikit-learn (作为备用)")
        print("   ❌ TensorFlow (已移除)")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

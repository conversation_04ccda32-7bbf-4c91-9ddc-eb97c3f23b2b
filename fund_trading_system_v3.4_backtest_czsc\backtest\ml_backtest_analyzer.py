import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# 尝试导入可视化库
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class MLBacktestAnalyzer:
    """
    机器学习回测结果分析器
    提供详细的ML模型性能分析和可视化
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化ML回测分析器
        
        Args:
            config: 分析配置
        """
        self.config = config or self._get_default_config()
        self.analysis_results = {}
        self.plots = {}
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'visualization': {
                'style': 'seaborn',
                'figsize': (12, 8),
                'save_plots': True,
                'plot_format': 'png'
            },
            'analysis': {
                'prediction_accuracy': True,
                'feature_importance': True,
                'model_comparison': True,
                'signal_analysis': True,
                'risk_metrics': True
            }
        }
    
    def analyze_ml_backtest_results(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析ML回测结果
        
        Args:
            backtest_results: ML回测结果
            
        Returns:
            分析结果字典
        """
        analysis = {}
        
        # 基础性能分析
        analysis['performance_summary'] = self._analyze_performance(backtest_results)
        
        # 预测准确度分析
        if 'prediction_history' in backtest_results:
            analysis['prediction_analysis'] = self._analyze_predictions(backtest_results['prediction_history'])
        
        # ML模型性能分析
        if 'ml_metrics' in backtest_results:
            analysis['model_performance'] = self._analyze_model_performance(backtest_results['ml_metrics'])
        
        # 信号质量分析
        analysis['signal_analysis'] = self._analyze_signals(backtest_results)
        
        # 风险指标分析
        analysis['risk_analysis'] = self._analyze_risk_metrics(backtest_results)
        
        # 特征重要性分析
        if 'feature_columns' in backtest_results:
            analysis['feature_analysis'] = self._analyze_features(backtest_results)
        
        self.analysis_results = analysis
        return analysis
    
    def _analyze_performance(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析基础性能指标"""
        performance = {}
        
        # 提取基础指标
        basic_metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 
                        'volatility', 'calmar_ratio', 'trade_count']
        
        for metric in basic_metrics:
            if metric in results:
                performance[metric] = results[metric]
        
        # 计算额外指标
        if 'final_value' in results and 'initial_capital' in results:
            performance['total_return_pct'] = (results['final_value'] / results['initial_capital'] - 1) * 100
        
        return performance
    
    def _analyze_predictions(self, prediction_history: List[Dict]) -> Dict[str, Any]:
        """分析预测准确度"""
        if not prediction_history:
            return {'error': 'No prediction history available'}
        
        predictions = [p['prediction'] for p in prediction_history]
        signals = [p['signal'] for p in prediction_history]
        
        analysis = {
            'total_predictions': len(predictions),
            'prediction_stats': {
                'mean': np.mean(predictions),
                'std': np.std(predictions),
                'min': np.min(predictions),
                'max': np.max(predictions),
                'median': np.median(predictions)
            },
            'signal_distribution': {
                'buy_signals': sum(1 for s in signals if s == 1),
                'sell_signals': sum(1 for s in signals if s == -1),
                'hold_signals': sum(1 for s in signals if s == 0)
            }
        }
        
        # 计算信号转换率
        total_signals = len([s for s in signals if s != 0])
        if total_signals > 0:
            analysis['signal_conversion_rate'] = total_signals / len(signals)
        else:
            analysis['signal_conversion_rate'] = 0
        
        # 预测分布分析
        analysis['prediction_distribution'] = {
            'positive_predictions': sum(1 for p in predictions if p > 0),
            'negative_predictions': sum(1 for p in predictions if p < 0),
            'neutral_predictions': sum(1 for p in predictions if p == 0)
        }
        
        return analysis
    
    def _analyze_model_performance(self, ml_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """分析ML模型性能"""
        analysis = {}
        
        # 模型性能指标
        if 'model_performance' in ml_metrics:
            model_perf = ml_metrics['model_performance']
            analysis['model_metrics'] = model_perf
            
            # 计算模型质量评分
            if 'main' in model_perf:
                main_perf = model_perf['main']
                if 'validation_mse' in main_perf and 'validation_mae' in main_perf:
                    # 简单的质量评分（越低越好）
                    quality_score = 1 / (1 + main_perf['validation_mse'])
                    analysis['model_quality_score'] = quality_score
        
        # 预测统计
        for key in ['total_predictions', 'prediction_mean', 'prediction_std']:
            if key in ml_metrics:
                analysis[key] = ml_metrics[key]
        
        return analysis
    
    def _analyze_signals(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易信号质量"""
        analysis = {}
        
        # 信号统计
        if 'ml_metrics' in results and 'signal_distribution' in results['ml_metrics']:
            signal_dist = results['ml_metrics']['signal_distribution']
            total_signals = sum(signal_dist.values())
            
            if total_signals > 0:
                analysis['signal_ratios'] = {
                    'buy_ratio': signal_dist['buy_signals'] / total_signals,
                    'sell_ratio': signal_dist['sell_signals'] / total_signals,
                    'hold_ratio': signal_dist['hold_signals'] / total_signals
                }
        
        # 交易效率
        if 'trade_count' in results and 'prediction_history' in results:
            total_predictions = len(results['prediction_history'])
            if total_predictions > 0:
                analysis['trading_efficiency'] = results['trade_count'] / total_predictions
        
        return analysis
    
    def _analyze_risk_metrics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析风险指标"""
        risk_analysis = {}
        
        # 基础风险指标
        risk_metrics = ['max_drawdown', 'volatility', 'var_95', 'downside_deviation']
        for metric in risk_metrics:
            if metric in results:
                risk_analysis[metric] = results[metric]
        
        # 风险调整收益
        if 'total_return' in results and 'volatility' in results:
            if results['volatility'] > 0:
                risk_analysis['risk_adjusted_return'] = results['total_return'] / results['volatility']
        
        # 最大回撤恢复时间（简化计算）
        if 'max_drawdown' in results:
            risk_analysis['drawdown_severity'] = 'High' if results['max_drawdown'] > 0.2 else 'Moderate' if results['max_drawdown'] > 0.1 else 'Low'
        
        return risk_analysis
    
    def _analyze_features(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析特征重要性"""
        feature_analysis = {}
        
        if 'feature_columns' in results:
            feature_analysis['total_features'] = len(results['feature_columns'])
            feature_analysis['feature_list'] = results['feature_columns']
        
        # 如果有模型性能数据，尝试提取特征重要性
        if 'ml_metrics' in results and 'model_performance' in results['ml_metrics']:
            model_perf = results['ml_metrics']['model_performance']
            if 'main' in model_perf and 'feature_count' in model_perf['main']:
                feature_analysis['features_used'] = model_perf['main']['feature_count']
        
        return feature_analysis
    
    def create_performance_report(self, analysis_results: Optional[Dict] = None) -> str:
        """
        创建性能报告
        
        Args:
            analysis_results: 分析结果，如果为None则使用内部结果
            
        Returns:
            格式化的报告字符串
        """
        if analysis_results is None:
            analysis_results = self.analysis_results
        
        if not analysis_results:
            return "No analysis results available"
        
        report = []
        report.append("=" * 80)
        report.append("ML增强回测性能报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 基础性能
        if 'performance_summary' in analysis_results:
            perf = analysis_results['performance_summary']
            report.append("📊 基础性能指标")
            report.append("-" * 40)
            
            if 'total_return' in perf:
                report.append(f"总收益率: {perf['total_return']:.2%}")
            if 'sharpe_ratio' in perf:
                report.append(f"夏普比率: {perf['sharpe_ratio']:.3f}")
            if 'max_drawdown' in perf:
                report.append(f"最大回撤: {perf['max_drawdown']:.2%}")
            if 'win_rate' in perf:
                report.append(f"胜率: {perf['win_rate']:.2%}")
            if 'trade_count' in perf:
                report.append(f"交易次数: {perf['trade_count']}")
            report.append("")
        
        # 预测分析
        if 'prediction_analysis' in analysis_results:
            pred = analysis_results['prediction_analysis']
            report.append("🤖 ML预测分析")
            report.append("-" * 40)
            
            if 'total_predictions' in pred:
                report.append(f"总预测次数: {pred['total_predictions']}")
            if 'signal_conversion_rate' in pred:
                report.append(f"信号转换率: {pred['signal_conversion_rate']:.2%}")
            
            if 'signal_distribution' in pred:
                sig_dist = pred['signal_distribution']
                report.append(f"买入信号: {sig_dist['buy_signals']}")
                report.append(f"卖出信号: {sig_dist['sell_signals']}")
                report.append(f"持有信号: {sig_dist['hold_signals']}")
            report.append("")
        
        # 模型性能
        if 'model_performance' in analysis_results:
            model = analysis_results['model_performance']
            report.append("🔬 模型性能")
            report.append("-" * 40)
            
            if 'model_quality_score' in model:
                report.append(f"模型质量评分: {model['model_quality_score']:.3f}")
            if 'total_predictions' in model:
                report.append(f"预测总数: {model['total_predictions']}")
            report.append("")
        
        # 风险分析
        if 'risk_analysis' in analysis_results:
            risk = analysis_results['risk_analysis']
            report.append("⚠️ 风险分析")
            report.append("-" * 40)
            
            if 'drawdown_severity' in risk:
                report.append(f"回撤严重程度: {risk['drawdown_severity']}")
            if 'risk_adjusted_return' in risk:
                report.append(f"风险调整收益: {risk['risk_adjusted_return']:.3f}")
            report.append("")
        
        # 特征分析
        if 'feature_analysis' in analysis_results:
            feat = analysis_results['feature_analysis']
            report.append("📈 特征分析")
            report.append("-" * 40)
            
            if 'total_features' in feat:
                report.append(f"总特征数: {feat['total_features']}")
            if 'features_used' in feat:
                report.append(f"使用特征数: {feat['features_used']}")
            report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_analysis_results(self, filepath: str, analysis_results: Optional[Dict] = None):
        """
        保存分析结果
        
        Args:
            filepath: 保存路径
            analysis_results: 分析结果
        """
        if analysis_results is None:
            analysis_results = self.analysis_results
        
        import json
        
        # 转换numpy数组为列表以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        converted_results = convert_numpy(analysis_results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, indent=2, ensure_ascii=False)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析总结"""
        if not self.analysis_results:
            return {'error': 'No analysis results available'}
        
        summary = {
            'analysis_completed': True,
            'sections_analyzed': list(self.analysis_results.keys()),
            'timestamp': datetime.now().isoformat()
        }
        
        # 提取关键指标
        if 'performance_summary' in self.analysis_results:
            perf = self.analysis_results['performance_summary']
            summary['key_metrics'] = {
                'total_return': perf.get('total_return', 0),
                'sharpe_ratio': perf.get('sharpe_ratio', 0),
                'max_drawdown': perf.get('max_drawdown', 0)
            }
        
        return summary

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版数据获取模块
整合enhanced_backtest.py的优秀数据获取功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 直接导入QUANTAXIS的TDX函数
from QUANTAXIS.QAFetch.QATdx import QA_fetch_get_stock_day, QA_fetch_get_stock_min


def get_kline(symbol, freq='D', start_date=None, end_date=None, max_retries=3):
    """
    使用原版QATDX获取K线数据 - 增强版本，带重试机制
    
    Args:
        symbol: 股票代码
        freq: 频率 ('D'=日线, '5min'=5分钟, '1min'=1分钟等)
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
        max_retries: 最大重试次数
    
    Returns:
        pd.DataFrame: K线数据，包含dt, open, high, low, close, vol列
    """
    # 设置默认日期范围
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    if start_date is None:
        # 默认获取2年数据，确保有足够的数据
        start_date = (datetime.now() - timedelta(days=2500)).strftime('%Y-%m-%d')
    
    last_error = None
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试获取{symbol}数据...")
                import time
                time.sleep(1)  # 重试前等待1秒
            
            print(f"📊 使用原版QATDX获取{symbol}数据，频率:{freq}，时间范围:{start_date}至{end_date}")
            
            # 使用原版QATDX的函数获取数据
            df = None
            if freq == 'D' or freq == 'day':
                # 获取日线数据
                df = QA_fetch_get_stock_day(symbol, start_date, end_date, frequence='day')
            elif freq in ['5min', '5m']:
                # 获取5分钟数据
                df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='5min')
            elif freq in ['1min', '1m']:
                # 获取1分钟数据
                df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='1min')
            elif freq in ['15min', '15m']:
                # 获取15分钟数据
                df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='15min')
            elif freq in ['30min', '30m']:
                # 获取30分钟数据
                df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='30min')
            elif freq in ['60min', '60m', '1h']:
                # 获取60分钟数据
                df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='60min')
            else:
                # 默认使用日线
                df = QA_fetch_get_stock_day(symbol, start_date, end_date, frequence='day')
            
            if df is None or df.empty:
                raise ValueError(f"获取{symbol}数据为空")
            
            # 重置索引，确保有干净的数据结构
            if 'date' in df.columns:
                df = df.reset_index(drop=True)
            else:
                df = df.reset_index()
            
            # 统一列名格式，适配原有代码
            # 优先使用datetime列（包含完整时间信息），其次使用date列
            if 'datetime' in df.columns:
                df.rename(columns={'datetime': 'dt'}, inplace=True)
            elif 'date' in df.columns:
                df.rename(columns={'date': 'dt'}, inplace=True)
            elif df.index.name == 'datetime':
                df['dt'] = df.index
                df = df.reset_index(drop=True)
            elif df.index.name == 'date':
                df['dt'] = df.index
                df = df.reset_index(drop=True)
            
            # 确保有volume列
            if 'vol' not in df.columns and 'volume' in df.columns:
                df.rename(columns={'volume': 'vol'}, inplace=True)
            elif 'vol' not in df.columns:
                df['vol'] = 100000  # 如果没有成交量数据，设为默认值
            
            # 确保日期格式正确
            if 'dt' in df.columns:
                df['dt'] = pd.to_datetime(df['dt'])
            else:
                # 如果没有dt列，尝试从索引获取
                if hasattr(df.index, 'to_pydatetime'):
                    df['dt'] = pd.to_datetime(df.index)
                else:
                    raise ValueError("无法找到日期列")
            
            # 按日期排序（确保时间升序）
            df = df.sort_values('dt', ascending=True).reset_index(drop=True)
            
            # 确保必要的列存在，包括symbol字段
            required_cols = ['dt', 'open', 'high', 'low', 'close', 'vol', 'symbol']
            for col in required_cols:
                if col not in df.columns:
                    if col == 'vol':
                        df[col] = 100000  # 默认成交量
                    elif col == 'symbol':
                        df[col] = symbol  # 添加symbol字段
                    else:
                        raise ValueError(f"缺少必要列: {col}")
            
            # 数据清洗：去除空值和异常值
            df = df.dropna(subset=['open', 'high', 'low', 'close'])
            df = df[df['open'] > 0]  # 去除价格为0的数据
            
            # 去除重复时间戳（保留第一条记录）
            original_len = len(df)
            df = df.drop_duplicates(subset=['dt'], keep='first')
            if len(df) < original_len:
                print(f"⚠️ 去除{original_len - len(df)}条重复时间数据，剩余{len(df)}条")
            
            if df.empty:
                raise ValueError(f"数据清洗后{symbol}数据为空")
            
            print(f"✅ 成功获取{len(df)}条{symbol}数据")
            print(f"📅 数据时间范围: {df['dt'].min()} 至 {df['dt'].max()}")
            
            return df[required_cols]
            
        except Exception as e:
            last_error = e
            print(f"⚠️ 第{attempt + 1}次获取{symbol}数据失败: {e}")
            if attempt == max_retries - 1:
                print(f"❌ 所有重试都失败，最终错误: {e}")
                import traceback
                traceback.print_exc()
    
    # 如果所有重试都失败，抛出最后一个错误
    raise Exception(f"无法获取股票{symbol}的数据，已重试{max_retries}次: {last_error}")


def get_realtime_quote(symbol, max_retries=2):
    """
    获取实时行情数据 - 增强版本，带重试机制和更好的兼容性
    """
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试获取{symbol}实时数据...")
                import time
                time.sleep(0.5)  # 重试前等待0.5秒
            
            # 获取最新的日线数据作为实时数据
            df = get_kline(symbol, freq='D', max_retries=1)  # 减少内部重试次数
            if df is None or df.empty:
                raise ValueError(f"无法获取{symbol}的K线数据")
            
            latest = df.iloc[-1]
            
            # 构建兼容的实时数据格式
            quote_data = {
                'symbol': symbol,
                'price': float(latest['close']),
                'open': float(latest['open']),
                'high': float(latest['high']),
                'low': float(latest['low']),
                'volume': float(latest['vol']),
                'datetime': latest['dt'],
                'timestamp': latest['dt'].isoformat() if hasattr(latest['dt'], 'isoformat') else str(latest['dt']),  # 添加timestamp字段
                'close': float(latest['close']),  # 添加close字段以保持兼容性
                'vol': float(latest['vol']),      # 添加vol字段以保持兼容性
                # 添加一些常用的计算字段
                'change': 0.0,  # 默认值，因为没有前一日数据对比
                'change_rate': 0.0,  # 默认值
                'amplitude': (float(latest['high']) - float(latest['low'])) / float(latest['low']) if float(latest['low']) > 0 else 0.0
            }
            
            # 如果有多天数据，计算涨跌幅
            if len(df) > 1:
                prev_close = float(df.iloc[-2]['close'])
                current_close = float(latest['close'])
                quote_data['change'] = current_close - prev_close
                quote_data['change_rate'] = (current_close - prev_close) / prev_close if prev_close > 0 else 0.0
            
            return quote_data
            
        except Exception as e:
            print(f"⚠️ 第{attempt + 1}次获取{symbol}实时数据失败: {e}")
            if attempt == max_retries - 1:
                print(f"❌ 获取{symbol}实时数据最终失败: {e}")
    
    # 返回一个默认的空数据结构，而不是None，以保持兼容性
    return {
        'symbol': symbol,
        'price': 0.0,
        'open': 0.0,
        'high': 0.0,
        'low': 0.0,
        'volume': 0.0,
        'close': 0.0,
        'vol': 0.0,
        'change': 0.0,
        'change_rate': 0.0,
        'amplitude': 0.0,
        'datetime': datetime.now(),
        'error': True  # 标记这是一个错误的数据
    }
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据结构问题
检查K线数据的时间排序和重复问题
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def debug_kline_data_structure():
    """调试K线数据结构"""
    print("🔍 调试K线数据结构...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        
        # 获取30分钟数据
        fund_code = '518880'
        print(f"📊 获取{fund_code}的30分钟数据...")
        
        df = get_kline(fund_code, freq='30min')
        
        if df is None or df.empty:
            print("❌ 数据获取失败")
            return
        
        print(f"✅ 获取{len(df)}条数据")
        print(f"📋 数据列: {list(df.columns)}")
        print(f"📅 时间范围: {df['dt'].min()} 至 {df['dt'].max()}")
        
        # 检查时间排序
        print("\n🔍 检查时间排序...")
        is_sorted = df['dt'].is_monotonic_increasing
        print(f"时间是否单调递增: {is_sorted}")
        
        if not is_sorted:
            print("⚠️ 时间排序有问题，查找问题位置...")
            for i in range(1, len(df)):
                if df.iloc[i]['dt'] <= df.iloc[i-1]['dt']:
                    print(f"❌ 时间倒序位置 {i}: {df.iloc[i-1]['dt']} >= {df.iloc[i]['dt']}")
                    print(f"   前一条: {df.iloc[i-1].to_dict()}")
                    print(f"   当前条: {df.iloc[i].to_dict()}")
                    break
        
        # 检查重复时间
        print("\n🔍 检查重复时间...")
        duplicated_times = df[df['dt'].duplicated()]
        if not duplicated_times.empty:
            print(f"❌ 发现{len(duplicated_times)}条重复时间数据:")
            print(duplicated_times[['dt', 'open', 'close']].head())
        else:
            print("✅ 没有重复时间")
        
        # 显示前几条和后几条数据
        print("\n📊 前5条数据:")
        print(df.head()[['dt', 'open', 'high', 'low', 'close', 'vol', 'symbol']])
        
        print("\n📊 后5条数据:")
        print(df.tail()[['dt', 'open', 'high', 'low', 'close', 'vol', 'symbol']])
        
        # 检查数据类型
        print("\n🔍 数据类型检查:")
        print(df.dtypes)
        
        # 检查是否有NaN值
        print("\n🔍 NaN值检查:")
        nan_counts = df.isnull().sum()
        print(nan_counts[nan_counts > 0])
        
        return df
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_czsc_with_cleaned_data():
    """使用清理后的数据测试CZSC"""
    print("\n🔍 使用清理后的数据测试CZSC...")
    
    try:
        df = debug_kline_data_structure()
        if df is None:
            return False
        
        # 清理数据
        print("\n🧹 清理数据...")
        
        # 1. 去除重复时间
        df_clean = df.drop_duplicates(subset=['dt'], keep='first')
        print(f"去除重复后: {len(df_clean)}条数据")
        
        # 2. 确保时间排序
        df_clean = df_clean.sort_values('dt', ascending=True).reset_index(drop=True)
        print("✅ 重新排序完成")
        
        # 3. 检查排序结果
        is_sorted = df_clean['dt'].is_monotonic_increasing
        print(f"排序后时间是否单调递增: {is_sorted}")
        
        if not is_sorted:
            print("❌ 排序后仍有问题")
            return False
        
        # 4. 转换为CZSC需要的格式
        print("\n🔧 转换为CZSC格式...")
        kline_list = []
        for _, row in df_clean.iterrows():
            kline_dict = {
                'symbol': str(row['symbol']),
                'dt': row['dt'],
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'vol': float(row['vol'])
            }
            kline_list.append(kline_dict)
        
        print(f"✅ 转换完成，共{len(kline_list)}条数据")
        
        # 5. 验证前几条数据的时间顺序
        print("\n🔍 验证前5条数据时间顺序:")
        for i in range(min(5, len(kline_list))):
            print(f"  {i}: {kline_list[i]['dt']} - {kline_list[i]['close']}")
        
        # 6. 测试CZSC分析器
        print("\n🔧 测试CZSC分析器...")
        from simpleczsc import KlineAnalyze
        
        ka = KlineAnalyze(kline_list)
        
        print("✅ CZSC分析器创建成功!")
        print(f"📊 分析结果:")
        print(f"   - symbol: {ka.symbol}")
        print(f"   - 开始时间: {ka.start_dt}")
        print(f"   - 结束时间: {ka.end_dt}")
        print(f"   - 最新价格: {ka.latest_price}")
        
        # 检查分型和笔数据
        if hasattr(ka, 'fx_list') and ka.fx_list:
            print(f"   - 分型数量: {len(ka.fx_list)}")
            if ka.fx_list:
                print(f"   - 最新分型: {ka.fx_list[-1]}")
        
        if hasattr(ka, 'bi_list') and ka.bi_list:
            print(f"   - 笔数量: {len(ka.bi_list)}")
            if ka.bi_list:
                print(f"   - 最新笔: {ka.bi_list[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ CZSC测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 数据结构调试")
    print("="*60)
    
    # 调试数据结构
    debug_kline_data_structure()
    
    # 测试清理后的CZSC
    success = test_czsc_with_cleaned_data()
    
    print("\n" + "="*60)
    if success:
        print("🎉 数据结构问题已解决！")
    else:
        print("⚠️ 仍有问题需要解决")
    
    return success

if __name__ == "__main__":
    main()
"""
AI配置管理
管理LLM API密钥和相关配置
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
load_dotenv('file.env')

class AIConfig:
    """AI配置管理类"""
    
    def __init__(self, config_file: str = None):
        self.logger = logging.getLogger(self.__class__.__name__)

        # 默认配置文件路径
        if config_file is None:
            config_dir = Path(__file__).parent
            config_file = config_dir / "ai_settings.json"
        
        self.config_file = Path(config_file)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        KIMI_API_KEY = os.getenv("MOONSHOT_API_KEY")
        """加载配置文件"""
        default_config = {
            "llm_providers": {
                "moonshot": {
                    "api_key":KIMI_API_KEY,
                    "base_url": "https://api.moonshot.cn/v1",
                    "model": "kimi-k2-0711-preview",
                    "enabled": True
                },
                "openai": {
                    "api_key": "$OPENAI_API_KEY",
                    "base_url": "https://api.openai.com/v1",
                    "model": "gpt-4",
                    "enabled": False
                }
            },
            "analysis_settings": {
                "default_temperature": 0.3,
                "max_tokens": 2000,
                "timeout": 30,
                "retry_attempts": 3
            },
            "features": {
                "market_analysis": True,
                "decision_explanation": True,
                "natural_language_query": True,
                "risk_assessment": True
            },
            "fallback_mode": {
                "enabled": True,
                "use_traditional_analysis": True
            }
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    config = {**default_config, **loaded_config}
                    self.logger.info(f"配置文件加载成功: {self.config_file}")
                    return config
            else:
                # 创建默认配置文件
                self._save_config(default_config)
                self.logger.info(f"创建默认配置文件: {self.config_file}")
                return default_config
                
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {str(e)}")
            return default_config
    
    def _save_config(self, config: Dict[str, Any] = None):
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"配置文件保存失败: {str(e)}")
    
    def get_llm_config(self, provider: str = "moonshot") -> Dict[str, Any]:
        """获取LLM提供商配置"""
        providers = self.config.get("llm_providers", {})
        
        if provider not in providers:
            self.logger.warning(f"未找到提供商配置: {provider}")
            return {}
        
        provider_config = providers[provider].copy()
        
        # 处理环境变量
        api_key = provider_config.get("api_key", "")
        if api_key.startswith("$"):
            env_var = api_key[1:]  # 移除$符号
            provider_config["api_key"] = os.getenv(env_var, "")
            
            if not provider_config["api_key"]:
                self.logger.warning(f"环境变量 {env_var} 未设置")
        
        return provider_config
    
    def get_analysis_settings(self) -> Dict[str, Any]:
        """获取分析设置"""
        return self.config.get("analysis_settings", {})
    
    def get_feature_config(self, feature: str) -> bool:
        """获取功能开关配置"""
        features = self.config.get("features", {})
        return features.get(feature, True)
    
    def is_fallback_enabled(self) -> bool:
        """检查是否启用兜底模式"""
        fallback = self.config.get("fallback_mode", {})
        return fallback.get("enabled", True)
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        try:
            # 深度合并配置
            self._deep_merge(self.config, updates)
            self._save_config()
            self.logger.info("配置更新成功")
            
        except Exception as e:
            self.logger.error(f"配置更新失败: {str(e)}")
    
    def _deep_merge(self, base: Dict, updates: Dict):
        """深度合并字典"""
        for key, value in updates.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查LLM提供商配置
        providers = self.config.get("llm_providers", {})
        enabled_providers = [name for name, config in providers.items() if config.get("enabled", False)]
        
        if not enabled_providers:
            validation_result["warnings"].append("没有启用的LLM提供商")
        
        for provider_name, provider_config in providers.items():
            if provider_config.get("enabled", False):
                api_key = provider_config.get("api_key", "")
                if not api_key or api_key.startswith("$"):
                    # 检查环境变量
                    if api_key.startswith("$"):
                        env_var = api_key[1:]
                        if not os.getenv(env_var):
                            validation_result["errors"].append(f"{provider_name}: 环境变量 {env_var} 未设置")
                    else:
                        validation_result["errors"].append(f"{provider_name}: API密钥未配置")
        
        # 检查分析设置
        analysis_settings = self.config.get("analysis_settings", {})
        temperature = analysis_settings.get("default_temperature", 0.3)
        if not 0 <= temperature <= 2:
            validation_result["warnings"].append("temperature 应该在 0-2 之间")
        
        if validation_result["errors"]:
            validation_result["valid"] = False
        
        return validation_result
    
    def get_active_provider(self) -> Optional[str]:
        """获取当前活跃的LLM提供商"""
        providers = self.config.get("llm_providers", {})
        
        for provider_name, provider_config in providers.items():
            if provider_config.get("enabled", False):
                # 检查API密钥是否可用
                api_key = provider_config.get("api_key", "")
                if api_key.startswith("$"):
                    env_var = api_key[1:]
                    if os.getenv(env_var):
                        return provider_name
                elif api_key:
                    return provider_name
        
        return None
    
    def create_sample_config(self):
        """创建示例配置文件"""
        sample_config = {
            "llm_providers": {
                "moonshot": {
                    "api_key": "sk-your-moonshot-api-key-here",
                    "base_url": "https://api.moonshot.cn/v1",
                    "model": "kimi-k2-0711-preview",
                    "enabled": True
                }
            },
            "analysis_settings": {
                "default_temperature": 0.3,
                "max_tokens": 2000,
                "timeout": 30
            },
            "features": {
                "market_analysis": True,
                "decision_explanation": True,
                "natural_language_query": True
            }
        }
        
        sample_file = self.config_file.parent / "ai_settings_sample.json"
        
        try:
            with open(sample_file, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            
            print(f"示例配置文件已创建: {sample_file}")
            print("请复制并重命名为 ai_settings.json，然后填入您的API密钥")
            
        except Exception as e:
            self.logger.error(f"创建示例配置文件失败: {str(e)}")


# 全局配置实例
ai_config = AIConfig()


def get_ai_config() -> AIConfig:
    """获取AI配置实例"""
    return ai_config


if __name__ == "__main__":
    # 测试配置管理
    config = AIConfig()
    
    print("当前配置:")
    print(json.dumps(config.config, indent=2, ensure_ascii=False))
    
    print("\n配置验证结果:")
    validation = config.validate_config()
    print(json.dumps(validation, indent=2, ensure_ascii=False))
    
    print(f"\n活跃的LLM提供商: {config.get_active_provider()}")
    
    # 创建示例配置
    config.create_sample_config()
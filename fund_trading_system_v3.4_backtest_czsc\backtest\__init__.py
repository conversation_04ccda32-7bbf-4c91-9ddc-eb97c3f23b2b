"""
高级回测系统模块
包含数据预处理、特征工程、时间序列处理、网络架构、模型训练、回测引擎和风险管理模块
"""

__version__ = "1.0.0"
__author__ = "Advanced Backtest System"

# 核心模块（无额外依赖）
from .backtest_engine import BacktestEngine
from .risk_manager import RiskManager

# 可选模块（需要额外依赖）
try:
    from .data_preprocessor import DataPreprocessor
    from .feature_engineer import FeatureEngineer
    from .timeseries_processor import TimeSeriesProcessor
    DATA_PROCESSING_AVAILABLE = True
except ImportError:
    DataPreprocessor = None
    FeatureEngineer = None
    TimeSeriesProcessor = None
    DATA_PROCESSING_AVAILABLE = False

try:
    from .network_architecture import NetworkArchitecture
    from .model_trainer import ModelTrainer
    ML_AVAILABLE = True
except ImportError:
    NetworkArchitecture = None
    ModelTrainer = None
    ML_AVAILABLE = False

try:
    from .advanced_backtest_system import AdvancedBacktestSystem
    ADVANCED_AVAILABLE = True
except ImportError:
    AdvancedBacktestSystem = None
    ADVANCED_AVAILABLE = False

# ML增强模块
try:
    from .ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
    from .enhanced_feature_engineer import EnhancedFeatureEngineer
    from .ml_model_manager import MLModelManager
    from .ml_backtest_analyzer import MLBacktestAnalyzer
    ML_ENHANCED_AVAILABLE = True
except ImportError:
    MLEnhancedBacktestEngine = None
    EnhancedFeatureEngineer = None
    MLModelManager = None
    MLBacktestAnalyzer = None
    ML_ENHANCED_AVAILABLE = False

__all__ = [
    'BacktestEngine',
    'RiskManager',
    'DATA_PROCESSING_AVAILABLE',
    'ML_AVAILABLE',
    'ADVANCED_AVAILABLE',
    'ML_ENHANCED_AVAILABLE'
]

# 添加可用的模块到 __all__
if DATA_PROCESSING_AVAILABLE:
    __all__.extend(['DataPreprocessor', 'FeatureEngineer', 'TimeSeriesProcessor'])

if ML_AVAILABLE:
    __all__.extend(['NetworkArchitecture', 'ModelTrainer'])

if ADVANCED_AVAILABLE:
    __all__.append('AdvancedBacktestSystem')

if ML_ENHANCED_AVAILABLE:
    __all__.extend(['MLEnhancedBacktestEngine', 'EnhancedFeatureEngineer',
                   'MLModelManager', 'MLBacktestAnalyzer'])
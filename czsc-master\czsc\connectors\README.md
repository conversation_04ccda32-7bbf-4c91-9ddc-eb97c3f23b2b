# CZSC 数据连接器

本目录包含了 CZSC 库的各种数据连接器，用于从不同的数据源获取行情数据并转换为 CZSC 库标准的 `RawBar` 对象。

## 通达信数据连接器 (tdx_connector.py)

通达信数据连接器基于 QUANTAXIS 库的 QATdx 模块，用于获取股票数据并转换为 CZSC 库的 RawBar 对象。

### 安装依赖

使用通达信数据连接器前，需要先安装 QUANTAXIS 库：

```bash
pip install quantaxis
```

### 使用方法

```python
from czsc.connectors.tdx_connector import get_raw_bars

# 获取日线数据
bars = get_raw_bars(
    symbol="000001",  # 股票代码
    freq="日线",      # 周期，支持 '1分钟', '5分钟', '15分钟', '30分钟', '60分钟', '日线', '周线', '月线'
    sdt="2023-01-01", # 开始日期
    edt="2023-12-31", # 结束日期
    fq="前复权"        # 复权方式，支持 '前复权', '后复权', '不复权'
)

# 可选参数
bars = get_raw_bars(
    symbol="000001", 
    freq="日线", 
    sdt="2023-01-01", 
    edt="2023-12-31", 
    fq="前复权",
    ip="********",    # 通达信服务器IP，可选
    port=7709,        # 通达信服务器端口，可选
    logger=my_logger  # 自定义logger对象，可选
)
```

### 返回结果

函数返回 `RawBar` 对象列表，每个 `RawBar` 对象包含以下属性：

- `symbol`: 股票代码
- `id`: 唯一标识符
- `dt`: 日期时间
- `freq`: 周期
- `open`: 开盘价
- `close`: 收盘价
- `high`: 最高价
- `low`: 最低价
- `vol`: 成交量
- `amount`: 成交额

### 注意事项

1. 通达信数据连接器依赖于 QUANTAXIS 库，请确保已正确安装该库
2. 如果未指定通达信服务器 IP 和端口，将使用 QUANTAXIS 默认的服务器
3. 对于不支持的周期或无效的股票代码，函数将返回空列表
4. 如果指定的日期范围内没有数据，函数也将返回空列表

### 示例

完整的使用示例可以参考 `examples/use_tdx_connector.py`。

### 测试

可以通过运行 `test/test_tdx_connector.py` 来测试通达信数据连接器的功能。
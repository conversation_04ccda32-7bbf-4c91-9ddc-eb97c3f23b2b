#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强集成测试
测试修复后的数据获取和时间处理
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_time_precision_fix():
    """测试时间精度修复"""
    print("🔍 测试时间精度修复...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        
        # 测试较短时间范围的30分钟数据
        symbol = '518880'
        start_date = '2025-07-01'
        end_date = '2025-07-18'
        
        print(f"📊 获取{symbol}的30分钟数据（时间范围：{start_date}至{end_date}）...")
        
        df = get_kline(symbol, freq='30min', start_date=start_date, end_date=end_date)
        
        if df is None or df.empty:
            print("❌ 数据获取失败")
            return False
        
        print(f"✅ 数据获取成功: {len(df)}条")
        print(f"📅 时间范围: {df['dt'].min()} 至 {df['dt'].max()}")
        
        # 检查时间精度
        print("\n📊 时间精度检查:")
        unique_times = df['dt'].unique()
        print(f"   唯一时间戳数量: {len(unique_times)}")
        print(f"   数据总量: {len(df)}")
        print(f"   重复比例: {(len(df) - len(unique_times)) / len(df) * 100:.1f}%")
        
        # 显示前几个时间戳
        print("\n📅 前10个时间戳:")
        for i, dt in enumerate(df['dt'].head(10)):
            print(f"   {i+1}: {dt}")
        
        # 检查时间间隔
        if len(df) > 1:
            time_diffs = df['dt'].diff().dropna()
            print(f"\n⏰ 时间间隔分析:")
            print(f"   最小间隔: {time_diffs.min()}")
            print(f"   最大间隔: {time_diffs.max()}")
            print(f"   平均间隔: {time_diffs.mean()}")
            
            # 检查是否有30分钟间隔
            thirty_min_intervals = time_diffs[time_diffs == pd.Timedelta(minutes=30)]
            print(f"   30分钟间隔数量: {len(thirty_min_intervals)}")
        
        # 检查数据质量
        print(f"\n📊 数据质量:")
        print(f"   时间排序: {'✅' if df['dt'].is_monotonic_increasing else '❌'}")
        print(f"   重复时间: {df['dt'].duplicated().sum()}条")
        print(f"   symbol字段: {'✅' if 'symbol' in df.columns and df['symbol'].iloc[0] == symbol else '❌'}")
        
        return len(unique_times) == len(df)  # 没有重复时间戳才算成功
        
    except Exception as e:
        print(f"❌ 时间精度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_czsc_with_fixed_data():
    """测试修复后的数据与CZSC集成"""
    print("\n🔍 测试修复后的数据与CZSC集成...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        from simpleczsc import KlineAnalyze
        
        # 获取较短时间范围的数据
        symbol = '518880'
        start_date = '2025-07-01'
        end_date = '2025-07-18'
        
        df = get_kline(symbol, freq='30min', start_date=start_date, end_date=end_date)
        
        if df is None or df.empty:
            print("❌ 数据获取失败")
            return False
        
        print(f"✅ 获取数据成功: {len(df)}条")
        
        # 转换为CZSC格式
        kline_list = []
        for _, row in df.iterrows():
            kline_dict = {
                'symbol': str(row['symbol']),
                'dt': row['dt'],
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'vol': float(row['vol'])
            }
            kline_list.append(kline_dict)
        
        print(f"🔧 转换为CZSC格式: {len(kline_list)}条")
        
        # 验证时间顺序
        for i in range(1, min(5, len(kline_list))):
            if kline_list[i]['dt'] <= kline_list[i-1]['dt']:
                print(f"❌ 时间顺序错误: {kline_list[i-1]['dt']} >= {kline_list[i]['dt']}")
                return False
        
        print("✅ 时间顺序验证通过")
        
        # 创建CZSC分析器
        ka = KlineAnalyze(kline_list)
        
        print("✅ CZSC分析器创建成功")
        print(f"📊 CZSC分析结果:")
        print(f"   - symbol: {ka.symbol}")
        print(f"   - 数据量: {len(kline_list)}条")
        print(f"   - 时间范围: {ka.start_dt} 至 {ka.end_dt}")
        print(f"   - 最新价格: {ka.latest_price}")
        
        # 检查分析结果
        has_fx = hasattr(ka, 'fx_list') and ka.fx_list
        has_bi = hasattr(ka, 'bi_list') and ka.bi_list
        has_xd = hasattr(ka, 'xd_list') and ka.xd_list
        
        print(f"   - 分型(FX): {'✅' if has_fx else '❌'} {len(ka.fx_list) if has_fx else 0}个")
        print(f"   - 笔(BI): {'✅' if has_bi else '❌'} {len(ka.bi_list) if has_bi else 0}个")
        print(f"   - 线段(XD): {'✅' if has_xd else '❌'} {len(ka.xd_list) if has_xd else 0}个")
        
        return True
        
    except Exception as e:
        print(f"❌ CZSC集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_realtime_quote_fix():
    """测试实时行情修复"""
    print("\n🔍 测试实时行情修复...")
    
    try:
        from core.enhanced_data_fetcher import get_realtime_quote
        
        symbol = '518880'
        print(f"📊 获取{symbol}实时行情...")
        
        quote = get_realtime_quote(symbol)
        
        if quote is None:
            print("❌ 实时行情获取失败")
            return False
        
        print("✅ 实时行情获取成功")
        print(f"📊 行情数据:")
        
        # 检查必要字段
        required_fields = ['symbol', 'price', 'datetime', 'timestamp']
        missing_fields = [field for field in required_fields if field not in quote]
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {missing_fields}")
            return False
        
        print(f"   - symbol: {quote['symbol']}")
        print(f"   - price: {quote['price']}")
        print(f"   - datetime: {quote['datetime']}")
        print(f"   - timestamp: {quote['timestamp']}")
        print(f"   - change_rate: {quote.get('change_rate', 0):.2%}")
        
        # 检查是否有错误标记
        if quote.get('error', False):
            print("⚠️ 数据标记为错误状态")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 实时行情测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_integration():
    """测试协调器集成"""
    print("\n🔍 测试协调器集成...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        
        # 创建协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试CZSC数据提取（使用较短时间范围）
        fund_code = '518880'
        print(f"📊 测试协调器CZSC数据提取...")
        
        # 这里我们需要模拟一个较短时间范围的测试
        # 由于协调器内部使用固定的时间范围，我们只能测试它是否能正常工作
        czsc_data = coordinator._extract_czsc_structure_data(fund_code, {})
        
        if not czsc_data or 'error' in czsc_data:
            print(f"❌ CZSC数据提取失败: {czsc_data}")
            return False
        
        print("✅ 协调器CZSC数据提取成功")
        print(f"📊 提取结果:")
        print(f"   - FX分型: {len(czsc_data.get('fx_list', []))}个")
        print(f"   - BI笔: {len(czsc_data.get('bi_list', []))}个")
        print(f"   - XD线段: {len(czsc_data.get('xd_list', []))}个")
        print(f"   - 数据源: {czsc_data.get('data_source', '未知')}")
        print(f"   - 分析时间: {czsc_data.get('analysis_time', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 增强集成测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    tests = [
        ("时间精度修复测试", test_time_precision_fix),
        ("CZSC集成测试", test_czsc_with_fixed_data),
        ("实时行情修复测试", test_realtime_quote_fix),
        ("协调器集成测试", test_coordinator_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 增强集成测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 保存测试报告
    test_report = {
        'test_date': datetime.now().isoformat(),
        'test_type': 'enhanced_integration',
        'total_tests': total,
        'passed_tests': passed,
        'success_rate': passed / total,
        'test_results': [
            {'test_name': name, 'result': result} 
            for name, result in results
        ],
        'status': 'SUCCESS' if passed == total else 'PARTIAL_SUCCESS',
        'improvements': [
            '修复了时间列优先级问题',
            '添加了timestamp字段到实时行情',
            '优化了数据去重逻辑',
            '改进了CZSC数据结构兼容性'
        ]
    }
    
    with open('enhanced_integration_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_report, f, indent=2, ensure_ascii=False)
    
    print(f"💾 测试报告已保存: enhanced_integration_test_results.json")
    
    if passed == total:
        print("🎉 所有增强集成测试通过！")
        print("\n💡 修复总结:")
        print("   ✅ 时间精度问题已解决")
        print("   ✅ timestamp字段已添加")
        print("   ✅ CZSC集成正常工作")
        print("   ✅ 协调器功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
风控配置系统
定义所有风控规则和参数配置
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime


@dataclass
class TechnicalIndicatorConfig:
    """技术指标配置"""
    name: str
    enabled: bool
    parameters: Dict[str, Any]
    weight: float
    description: str


@dataclass
class RiskThreshold:
    """风险阈值配置"""
    low: float
    medium: float
    high: float
    critical: float


class RiskControlConfig:
    """风控配置管理器"""
    
    def __init__(self):
        self.config = self._load_default_config()
        self.last_updated = datetime.now()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认风控配置"""
        return {
            # 技术指标风控配置
            'technical_indicators': {
                'bollinger_bands': {
                    'enabled': True,
                    'buy_position_requirement': 'below_lower_band',  # 买入要求：低于下轨
                    'sell_position_requirement': 'above_upper_band',  # 卖出要求：高于上轨
                    'tolerance': 0.02,  # 2%容忍度
                    'period': 20,  # 布林线周期
                    'std_dev': 2.0,  # 标准差倍数
                    'weight': 0.3,  # 权重
                    'description': '布林线位置验证 - 买入时要求价格低于下轨'
                },
                'rsi': {
                    'enabled': True,
                    'buy_max_threshold': 65,  # 买入时RSI最大值
                    'sell_min_threshold': 35,  # 卖出时RSI最小值
                    'oversold_threshold': 30,  # 超卖阈值
                    'overbought_threshold': 70,  # 超买阈值
                    'period': 14,  # RSI周期
                    'weight': 0.25,  # 权重
                    'description': 'RSI超买超卖验证 - 避免在超买区域买入'
                },
                'volume': {
                    'enabled': True,
                    'min_volume_ratio': 1.2,  # 最小成交量比率
                    'avg_period': 20,  # 平均成交量计算周期
                    'spike_threshold': 3.0,  # 异常放量阈值
                    'weight': 0.2,  # 权重
                    'description': '成交量确认 - 确保有足够流动性支撑'
                },
                'macd': {
                    'enabled': True,
                    'fast_period': 12,  # 快线周期
                    'slow_period': 26,  # 慢线周期
                    'signal_period': 9,  # 信号线周期
                    'bullish_required': False,  # 是否要求MACD看涨
                    'weight': 0.15,  # 权重
                    'description': 'MACD趋势确认 - 辅助判断趋势方向'
                },
                'moving_average': {
                    'enabled': True,
                    'short_period': 5,  # 短期均线
                    'long_period': 20,  # 长期均线
                    'trend_confirmation': True,  # 要求趋势确认
                    'weight': 0.1,  # 权重
                    'description': '均线趋势确认 - 确保价格趋势向上'
                }
            },
            
            # 市场环境风控配置
            'market_environment': {
                'volatility': {
                    'max_threshold': 0.8,  # 最大波动性阈值
                    'calculation_period': 20,  # 波动性计算周期
                    'weight': 0.4,  # 权重
                    'description': '波动性控制 - 高波动时限制交易'
                },
                'liquidity': {
                    'min_score': 0.3,  # 最小流动性评分
                    'weight': 0.3,  # 权重
                    'description': '流动性要求 - 确保基金流动性充足'
                },
                'sentiment': {
                    'min_score': 0.2,  # 最小情绪评分
                    'weight': 0.3,  # 权重
                    'description': '市场情绪 - 避免在极度悲观时买入'
                }
            },
            
            # 组合风险配置
            'portfolio_risk': {
                'position_limits': {
                    'max_single_position': 0.20,  # 单个基金最大仓位20%
                    'max_sector_exposure': 0.40,  # 单个行业最大敞口40%
                    'max_correlation_exposure': 0.60,  # 高相关性基金最大总敞口60%
                    'description': '仓位限制 - 控制集中度风险'
                },
                'correlation': {
                    'max_correlation': 0.7,  # 最大相关性
                    'correlation_period': 60,  # 相关性计算周期
                    'description': '相关性控制 - 避免过度集中投资'
                },
                'diversification': {
                    'min_holdings': 3,  # 最小持仓数量
                    'max_holdings': 15,  # 最大持仓数量
                    'description': '分散化要求 - 保持适度分散'
                }
            },
            
            # 风险阈值配置
            'risk_thresholds': {
                'overall_risk': RiskThreshold(0.2, 0.4, 0.7, 0.9),
                'technical_risk': RiskThreshold(0.3, 0.5, 0.8, 0.95),
                'market_risk': RiskThreshold(0.2, 0.4, 0.6, 0.8),
                'portfolio_risk': RiskThreshold(0.1, 0.3, 0.6, 0.85)
            },
            
            # 动态调整配置
            'dynamic_adjustment': {
                'enabled': True,
                'market_regime_adjustment': {
                    'bull_market': {
                        'rsi_threshold_adjustment': 5,  # 牛市时RSI阈值放宽5点
                        'volume_requirement_adjustment': -0.2,  # 成交量要求降低0.2
                        'description': '牛市调整 - 适度放宽买入条件'
                    },
                    'bear_market': {
                        'rsi_threshold_adjustment': -10,  # 熊市时RSI阈值收紧10点
                        'volume_requirement_adjustment': 0.5,  # 成交量要求提高0.5
                        'bb_tolerance_adjustment': -0.01,  # 布林线容忍度收紧1%
                        'description': '熊市调整 - 收紧买入条件'
                    },
                    'sideways_market': {
                        'description': '震荡市调整 - 保持标准条件'
                    }
                },
                'volatility_adjustment': {
                    'high_volatility': {
                        'all_thresholds_multiplier': 0.8,  # 高波动时所有阈值收紧20%
                        'description': '高波动调整 - 提高风控标准'
                    },
                    'low_volatility': {
                        'all_thresholds_multiplier': 1.1,  # 低波动时所有阈值放宽10%
                        'description': '低波动调整 - 适度放宽标准'
                    }
                }
            },
            
            # 监控和报警配置
            'monitoring': {
                'real_time_enabled': True,
                'check_frequency': 300,  # 检查频率（秒）
                'alert_levels': {
                    'warning': 0.6,  # 警告级别
                    'critical': 0.8,  # 严重级别
                    'emergency': 0.95  # 紧急级别
                },
                'notification': {
                    'email_enabled': False,
                    'log_enabled': True,
                    'console_enabled': True
                }
            },
            
            # 回测和验证配置
            'validation': {
                'backtest_enabled': True,
                'validation_period': 252,  # 验证周期（交易日）
                'performance_metrics': [
                    'sharpe_ratio',
                    'max_drawdown',
                    'win_rate',
                    'profit_factor'
                ]
            }
        }
    
    def get_technical_config(self, indicator_name: str) -> Dict[str, Any]:
        """获取技术指标配置"""
        return self.config['technical_indicators'].get(indicator_name, {})
    
    def get_market_config(self) -> Dict[str, Any]:
        """获取市场环境配置"""
        return self.config['market_environment']
    
    def get_portfolio_config(self) -> Dict[str, Any]:
        """获取组合风险配置"""
        return self.config['portfolio_risk']
    
    def get_risk_thresholds(self) -> Dict[str, RiskThreshold]:
        """获取风险阈值配置"""
        return self.config['risk_thresholds']
    
    def update_config(self, section: str, key: str, value: Any) -> bool:
        """更新配置"""
        try:
            if section in self.config and key in self.config[section]:
                self.config[section][key] = value
                self.last_updated = datetime.now()
                return True
            return False
        except Exception:
            return False
    
    def get_dynamic_adjustment(self, market_regime: str, volatility_level: str) -> Dict[str, Any]:
        """获取动态调整参数"""
        adjustments = {}
        
        # 市场状态调整
        if market_regime in self.config['dynamic_adjustment']['market_regime_adjustment']:
            adjustments.update(
                self.config['dynamic_adjustment']['market_regime_adjustment'][market_regime]
            )
        
        # 波动性调整
        if volatility_level in self.config['dynamic_adjustment']['volatility_adjustment']:
            adjustments.update(
                self.config['dynamic_adjustment']['volatility_adjustment'][volatility_level]
            )
        
        return adjustments
    
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        errors = []
        
        # 检查必要的配置项
        required_sections = ['technical_indicators', 'market_environment', 'portfolio_risk']
        for section in required_sections:
            if section not in self.config:
                errors.append(f"缺少必要配置节: {section}")
        
        # 检查阈值合理性
        try:
            tech_config = self.config['technical_indicators']
            if tech_config['rsi']['buy_max_threshold'] <= tech_config['rsi']['oversold_threshold']:
                errors.append("RSI买入阈值设置不合理")
            
            if tech_config['volume']['min_volume_ratio'] <= 0:
                errors.append("成交量比率阈值必须大于0")
                
        except KeyError as e:
            errors.append(f"配置项缺失: {str(e)}")
        
        return errors
    
    def export_config(self) -> Dict[str, Any]:
        """导出配置"""
        return self.config.copy()
    
    def import_config(self, config_dict: Dict[str, Any]) -> bool:
        """导入配置"""
        try:
            # 验证导入的配置
            temp_config = self.config
            self.config = config_dict
            errors = self.validate_config()
            
            if errors:
                self.config = temp_config  # 恢复原配置
                return False
            
            self.last_updated = datetime.now()
            return True
            
        except Exception:
            return False


# 全局配置实例
RISK_CONTROL_CONFIG = RiskControlConfig()


def get_risk_config() -> RiskControlConfig:
    """获取风控配置实例"""
    return RISK_CONTROL_CONFIG

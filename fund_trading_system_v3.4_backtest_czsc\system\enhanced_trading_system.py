"""
增强版基金交易系统 V3
集成六大维度评估体系的完整自动化交易系统
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
import time
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from coordinators import MultiAgentCoordinatorV3, FundTradingExecutorV3, TradingAgent
from agents.traditional import TechnicalAgent, GuaAnalysisAgent, FundFlowAgent
from agents.enhanced import EnhancedTechnicalAgent, SorosReflexivityAgent
from core.utils import *

# 尝试导入ML增强回测模块
try:
    from backtest.advanced_backtest_system import AdvancedBacktestSystem
    from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
    from config.ml_backtest_config import MLBacktestConfig
    ML_BACKTEST_AVAILABLE = True
except ImportError:
    ML_BACKTEST_AVAILABLE = False


class EnhancedFundTradingSystemV3:
    """
    @class EnhancedFundTradingSystemV3
    @brief 增强版基金交易多智能体系统V3
    @details 集成六大维度评估体系的完整自动化交易系统
    """
    
    def __init__(self, title: str = ""):
        # 配置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化所有代理
        self.technical_agent = TechnicalAgent()
        self.enhanced_technical_agent = EnhancedTechnicalAgent()
        self.gua_agent = GuaAnalysisAgent()
        self.fund_flow_agent = FundFlowAgent()
        self.soros_agent = SorosReflexivityAgent()
        self.trading_agent = TradingAgent(title=title)
        
        # 初始化V3新增组件
        self.coordinator = MultiAgentCoordinatorV3()
        self.executor = FundTradingExecutorV3()

        # 初始化ML回测组件
        self.ml_backtest_available = ML_BACKTEST_AVAILABLE
        if self.ml_backtest_available:
            self.ml_config = MLBacktestConfig.create_default_config()
            self.backtest_system = None  # 延迟初始化

        # 交易基金列表
        self.buy_fund_list = [
            '518880', '159567', '513030', '513080', '513520',
            '513500', '513300', '159329', '513850',
        ]

        # 初始化基金特性分析器
        from optimizers.fund_analyzer import FundCharacteristicsAnalyzer
        self.fund_analyzer = FundCharacteristicsAnalyzer()

        # 自动同步基金分类
        self._sync_fund_categories()
        
        # 新闻和指数检查时间段
        self.news_index_time = [
            ['09:27:00', '09:32:00'], ['10:00:00', '10:06:00'], 
            ['10:30:00', '10:36:00'], ['11:00:00', '11:06:00'],
            ['13:00:00', '13:06:00'], ['13:30:00', '13:36:00'], 
            ['14:00:00', '14:06:00'], ['14:30:00', '14:36:00']
        ]
        
        # 并行处理配置
        self.max_workers = 4
        self.timeout = 30
        
        self.logger.info("Enhanced Fund Trading System V3 initialized")
        self.logger.info("🔄 V3版本特性: 六大维度评估 + 智能冲突解决 + 动态权重管理")

    def _sync_fund_categories(self):
        """同步基金分类"""
        try:
            # 尝试从配置文件加载分类
            self.fund_analyzer.load_categories_from_config()

            # 与当前基金列表同步
            new_classifications = self.fund_analyzer.sync_with_fund_list(self.buy_fund_list)

            if new_classifications:
                self.logger.info(f"新分类的基金: {new_classifications}")
                # 保存更新后的分类到配置文件
                self.fund_analyzer.save_categories_to_config()

            # 显示分类统计
            stats = self.fund_analyzer.get_category_stats()
            self.logger.info(f"基金分类统计: {stats}")

        except Exception as e:
            self.logger.warning(f"基金分类同步失败: {e}")

    def get_fund_category_info(self, fund_code: str) -> Dict[str, Any]:
        """获取基金分类信息"""
        try:
            category = self.fund_analyzer.get_fund_category(fund_code)
            correlation_score = self.fund_analyzer.calculate_correlation_score(fund_code)
            category_adjustment = self.fund_analyzer._get_category_adjustment(category)

            return {
                'category': category,
                'correlation_score': correlation_score,
                'category_adjustment': category_adjustment
            }
        except Exception as e:
            self.logger.error(f"获取基金分类信息失败 {fund_code}: {e}")
            return {
                'category': 'unknown',
                'correlation_score': 0.5,
                'category_adjustment': {'sensitivity': 0.0, 'smoothness': 0.0}
            }

        
    def analyze_fund_v3(self, fund_code: str) -> Dict[str, Any]:
        """V3版本的增强基金分析"""
        self.logger.info(f"Starting V3 enhanced analysis for fund {fund_code}")
        
        # 使用V3协调器进行分析
        analysis_result = self.coordinator.coordinate_analysis(fund_code)
        
        # 获取账户信息
        account_data = self.trading_agent.get_account_info()
        analysis_result['account_data'] = account_data
        
        return analysis_result
    
    def execute_trading_decision_v3(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """V3版本的交易决策执行"""
        fund_code = analysis_result.get('fund_code')
        enhanced_decision = analysis_result.get('enhanced_decision', {})

        # 获取决策信息 - 优先使用风控后的最终决策
        final_decision = analysis_result.get('final_decision', 'hold')  # 使用风控后的决策
        final_confidence = analysis_result.get('final_confidence', 0.0)  # 使用风控后的置信度

        # 如果没有风控决策，则使用原始决策
        if final_decision == 'hold' and 'final_decision' not in analysis_result:
            final_decision = enhanced_decision.get('decision', 'hold')
            final_confidence = enhanced_decision.get('confidence', 0.0)

        self.logger.info(f"执行基金 {fund_code} 决策: {final_decision} (置信度: {final_confidence:.3f})")
        
        if final_decision in ['buy', 'sell'] and final_confidence > 0.5:
            # 使用V3执行器进行交易
            execution_result = self.executor.execute_decision(analysis_result)
            
            # 如果V3执行器支持，同时使用真实交易代理
            if execution_result.get('execution_status') == 'executed':
                # 准备交易数据
                trade_data = {
                    'action': final_decision,
                    'fund_code': fund_code,
                    'price': execution_result.get('price', 1.0),
                    'quantity': execution_result.get('shares', '20000')
                }
                
                # 执行真实交易
                trade_result = self.trading_agent.process(trade_data)
                execution_result['real_trade_result'] = trade_result
                
                self.logger.info(f"Real trade executed for {fund_code}: {trade_result}")
            
            return execution_result
        else:
            return {
                'fund_code': fund_code,
                'action': 'hold',
                'reason': f"Decision: {final_decision}, Confidence: {final_confidence:.2f} (threshold: 0.5)",
                'timestamp': datetime.now().isoformat()
            }
    
    def run_trading_cycle_v3(self) -> List[Dict[str, Any]]:
        """运行V3版本的单次交易周期"""
        cycle_start_time = datetime.now()
        results = []
        
        # 测试基金列表
        fund_codes = self.buy_fund_list
        
        print(f"\n🔄 V3 交易周期开始 - {len(fund_codes)} 只基金 [{cycle_start_time.strftime('%H:%M:%S')}]")
        
        for fund_code in fund_codes:
            try:
                # 执行V3分析
                analysis_result = self.analyze_fund_v3(fund_code)
                
                if 'error' not in analysis_result:
                    # 展示详细分析结果
                    self.display_detailed_analysis_result(analysis_result)
                    
                    # 执行交易决策
                    execution_result = self.execute_trading_decision_v3(analysis_result)
                    result = {
                        'fund_code': fund_code,
                        'analysis_result': analysis_result,
                        'execution_result': execution_result
                    }
                else:
                    print(f"\n❌ 基金 {fund_code} 分析失败: {analysis_result['error']}")
                    result = {
                        'fund_code': fund_code,
                        'error': analysis_result['error']
                    }
                    
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Trading cycle error for {fund_code}: {e}")
                results.append({
                    'fund_code': fund_code,
                    'error': str(e)
                })
        
        # 周期结束摘要
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        
        # 统计结果
        successful_analysis = len([r for r in results if 'error' not in r])
        failed_analysis = len([r for r in results if 'error' in r])
        
        # 统计决策类型 - 使用风控后的最终决策
        original_decisions = {}
        final_decisions = {}

        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})

                # 原始决策统计
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                original_decision = enhanced_decision.get('decision', 'hold')
                original_decisions[original_decision] = original_decisions.get(original_decision, 0) + 1

                # 最终决策统计（风控后）
                final_decision = analysis_result.get('final_decision', original_decision)
                final_decisions[final_decision] = final_decisions.get(final_decision, 0) + 1
        
        # 简化输出 - 显示风控后的最终决策分布
        final_decision_summary = " | ".join([f"{k.upper()}:{v}" for k, v in final_decisions.items()]) if final_decisions else "无决策"

        # 检查是否有风控干预
        risk_interventions = 0
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                original = analysis_result.get('enhanced_decision', {}).get('decision', 'hold')
                final = analysis_result.get('final_decision', original)
                if original != final:
                    risk_interventions += 1

        # 显示统计信息
        if risk_interventions > 0:
            print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 最终决策分布: {final_decision_summary} | 🛡️风控干预:{risk_interventions}次\n")
        else:
            print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 决策分布: {final_decision_summary}\n")
        
        return results

    def display_detailed_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """展示精简的分析结果 - 强制使用增强版展示系统"""
        # 强制使用增强版展示系统
        from system.enhanced_display_system import EnhancedDisplaySystem
        display_system = EnhancedDisplaySystem()
        display_system.display_comprehensive_analysis(analysis_result)
        print("✅ 使用增强版展示系统 - 提供更丰富的分析信息")
        return
        
        # 原版展示逻辑作为备用
        try:
            fund_code = analysis_result.get('fund_code', 'UNKNOWN')
            enhanced_decision = analysis_result.get('enhanced_decision', {})

            # 核心决策结果
            original_decision = enhanced_decision.get('decision', 'hold')
            original_confidence = enhanced_decision.get('confidence', 0)
            weighted_score = enhanced_decision.get('weighted_score', 0)

            # 风控后的最终决策
            final_decision = analysis_result.get('final_decision', original_decision)
            final_confidence = analysis_result.get('final_confidence', original_confidence)

            # 检查是否被风控修改
            risk_control_changed = (original_decision != final_decision)

            # 显示原始决策
            decision_icon = "🟢" if original_decision == "buy" else ("🔴" if original_decision == "sell" else "🟡")
            print(f"\n{decision_icon} {fund_code}: {original_decision.upper()} | 置信度:{original_confidence:.2f} | 评分:{weighted_score:.3f}")

            # 如果风控修改了决策，显示风控信息
            if risk_control_changed:
                risk_control = analysis_result.get('risk_control', {})
                risk_level = risk_control.get('risk_level', 'unknown')

                # 获取风控违规详情
                risk_validation = risk_control.get('risk_validation_result', {}).get('risk_validation', {})
                violations = risk_validation.get('technical_violations', {})

                # 生成风控说明
                risk_icon = "🛡️"
                risk_reasons = []

                if violations:
                    for indicator, violation in violations.items():
                        if indicator == 'bollinger_bands':
                            risk_reasons.append("布林线位置过高")
                        elif indicator == 'rsi':
                            risk_reasons.append("RSI超买")
                        elif indicator == 'volume':
                            risk_reasons.append("成交量不足")
                        else:
                            risk_reasons.append(f"{indicator}违规")

                if not risk_reasons:
                    risk_reasons.append("技术条件不符合")

                risk_summary = ", ".join(risk_reasons[:3])  # 最多显示3个原因

                print(f"   {risk_icon} 风控阻止: {original_decision.upper()} → {final_decision.upper()} | 原因: {risk_summary} | 风险等级: {risk_level}")
                print(f"   📊 最终执行: {final_decision.upper()} (风控后置信度: {final_confidence:.2f})")

            # 显示市场分类
            market_classification = enhanced_decision.get('market_classification', {})
            classification = market_classification.get('primary_classification', '未知')
            print(f"   📊 市场分类: {classification}")

            # 显示回测验证结果
            backtest_validation = analysis_result.get('backtest_validation', {})
            if backtest_validation and 'error' not in backtest_validation:
                quality_score = backtest_validation.get('quality_score', 0.0)
                recommendation = backtest_validation.get('recommendation', 'maintain')
                backtest_performance = backtest_validation.get('backtest_performance', {})
                
                # 显示回测质量评分
                quality_icon = "🟢" if quality_score >= 0.7 else ("🟡" if quality_score >= 0.5 else "🔴")
                print(f"   {quality_icon} 回测验证: 质量评分={quality_score:.3f} | 建议={recommendation}")
                
                # 显示关键回测指标
                if backtest_performance:
                    sharpe = backtest_performance.get('sharpe_ratio', 0.0)
                    win_rate = backtest_performance.get('win_rate', 0.0)
                    max_dd = backtest_performance.get('max_drawdown', 0.0)
                    print(f"   📊 回测指标: 夏普={sharpe:.2f} | 胜率={win_rate:.1%} | 最大回撤={max_dd:.1%}")
                
                # 显示信号强度调整
                original_strength = backtest_validation.get('original_strength', 0.5)
                optimized_strength = backtest_validation.get('optimized_strength', 0.5)
                if abs(optimized_strength - original_strength) > 0.05:
                    strength_change = "↗️" if optimized_strength > original_strength else "↘️"
                    print(f"   🎯 强度优化: {original_strength:.2f} → {optimized_strength:.2f} {strength_change}")

            # 显示LLM分析结果
            llm_analysis = analysis_result.get('llm_analysis', {})
            if llm_analysis and 'error' not in llm_analysis:
                market_sentiment = llm_analysis.get('market_sentiment', '未知')
                llm_confidence = llm_analysis.get('confidence_level', 0)
                strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
                
                # 显示基本LLM信息
                print(f"   🤖 LLM分析: 情绪={market_sentiment} | 置信度={llm_confidence:.2f}")
                
                # 显示完整的策略建议（如果有的话）
                if strategy_suggestion:
                    # 将长文本分行显示，每行最多80个字符
                    suggestion_lines = self._wrap_text(strategy_suggestion, 80)
                    for i, line in enumerate(suggestion_lines):
                        if i == 0:
                            print(f"   💡 策略建议: {line}")
                        else:
                            print(f"             {line}")
                
                # 显示市场驱动因素（如果有的话）
                market_drivers = llm_analysis.get('market_drivers', [])
                if market_drivers:
                    print(f"   📈 市场驱动:")
                    for driver in market_drivers[:3]:  # 显示前3个驱动因素
                        driver_lines = self._wrap_text(driver, 70)
                        for j, line in enumerate(driver_lines):
                            if j == 0:
                                print(f"     • {line}")
                            else:
                                print(f"       {line}")
                
                # 显示风险点（如果有的话）
                risk_points = llm_analysis.get('risk_points', [])
                if risk_points:
                    print(f"   ⚠️ 风险提示:")
                    for risk in risk_points[:2]:  # 显示前2个风险点
                        risk_lines = self._wrap_text(risk, 70)
                        for k, line in enumerate(risk_lines):
                            if k == 0:
                                print(f"     • {line}")
                            else:
                                print(f"       {line}")
                    
            elif llm_analysis and 'error' in llm_analysis:
                print(f"   🤖 LLM分析: 失败 - {llm_analysis.get('error', '未知错误')[:50]}...")

            # 显示维度评估摘要
            dimension_evaluations = enhanced_decision.get('dimension_evaluations', {})
            if dimension_evaluations:
                dim_summary = []
                for dim_name, dim_data in dimension_evaluations.items():
                    score = dim_data.get('score', 0)
                    state = dim_data.get('state', 'unknown')
                    dim_summary.append(f"{dim_name}:{score:.2f}")

                print(f"   🎯 维度评估: {' | '.join(dim_summary[:3])}")  # 只显示前3个

        except Exception as e:
            print(f"❌ {fund_code} 分析展示失败: {str(e)}")

    def _wrap_text(self, text: str, width: int) -> List[str]:
        """将长文本按指定宽度分行"""
        if not text:
            return []
        
        lines = []
        words = text.split()
        current_line = ""
        
        for word in words:
            # 检查添加这个词后是否会超过宽度
            if len(current_line + " " + word) <= width:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                # 如果当前行不为空，保存它并开始新行
                if current_line:
                    lines.append(current_line)
                    current_line = word
                else:
                    # 如果单个词就超过宽度，直接添加
                    lines.append(word)
        
        # 添加最后一行
        if current_line:
            lines.append(current_line)
        
        return lines

    def run_trading_system_v3(self) -> None:
        """V3版本的自动化交易系统主循环 - 集成多策略回测功能"""
        self.logger.info("Starting V3 Enhanced Fund Trading System with Multi-Strategy Backtest")
        print(f"\n🚀 启动增强版基金交易系统 V3")
        print("✨ 新功能: 集成多策略回测系统")
        print("✅ 数据获取: 增强版模块 (88.33%成功率)")
        print("✅ 展示系统: 增强版界面")
        print("="*80)

        try:
            # 首先运行多策略回测分析
            self._run_initial_backtest_analysis()
            
            # 主交易循环
            while ('06:05:00' <= datetime.now().strftime('%H:%M:%S') <= '12:27:00') or \
                  ('12:50:00' <= datetime.now().strftime('%H:%M:%S') <= '23:55:00'):

                # 运行V3交易周期
                results = self.run_trading_cycle_v3()

                # 休息间隔
                time.sleep(15)

        except KeyboardInterrupt:
            self.logger.info("V3 Trading system interrupted by user")
            print(f"\n👋 用户中断，系统退出")
        except Exception as e:
            self.logger.error(f"V3 Trading system error: {e}")
            print(f"\n❌ 系统运行异常: {e}")
        finally:
            self.logger.info("V3 Enhanced trading system stopped")
            print(f"\n🏁 增强版交易系统已停止")
    
    def _run_initial_backtest_analysis(self) -> None:
        """运行初始回测分析"""
        try:
            print("\n📊 启动初始多策略回测分析...")
            from system.multi_strategy_backtest import MultiStrategyBacktestSystem
            
            backtest_system = MultiStrategyBacktestSystem(initial_capital=100000)
            
            # 对主要基金进行快速回测分析
            main_funds = ['518880', '513500']  # 减少基金数量以加快启动速度
            
            for fund_code in main_funds:
                print(f"\n🔍 快速分析基金: {fund_code}")
                try:
                    # 只测试最佳策略
                    single_result = backtest_system.run_single_strategy_backtest(
                        fund_code, 'ma_crossover', max_position_pct=0.8, stop_loss_pct=0.05
                    )
                    
                    if single_result['status'] == 'success':
                        metrics = single_result['metrics']
                        print(f"✅ {fund_code} MA交叉策略:")
                        print(f"   年化收益: {metrics.get('annual_return', 0):.2%}")
                        print(f"   夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
                        print(f"   最大回撤: {metrics.get('max_drawdown', 0):.2%}")
                    else:
                        print(f"⚠️ {fund_code} 回测失败")
                    
                except Exception as e:
                    print(f"⚠️ {fund_code} 分析失败: {e}")
                    continue
            
            print("\n✅ 初始回测分析完成，开始正常交易流程")
            
        except Exception as e:
            print(f"⚠️ 初始回测分析失败: {e}")
            print("💡 系统将继续运行，但不包含回测分析功能")

    def diagnose_system_status_v3(self, fund_code: str = '513500') -> Dict[str, Any]:
        """V3版本的系统诊断"""
        self.logger.info(f"🔧 V3系统诊断开始 - 基金: {fund_code}")

        diagnosis = {
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'issues_found': [],
            'recommendations': [],
            'component_status': {},
            'v3_features_status': {}
        }

        try:
            # 1. 测试V3增强分析
            self.logger.info("🔍 测试V3六大维度分析...")
            analysis_result = self.analyze_fund_v3(fund_code)
            if 'error' in analysis_result:
                diagnosis['issues_found'].append("V3 enhanced analysis failed")
                diagnosis['component_status']['v3_analysis'] = 'failed'
            else:
                diagnosis['component_status']['v3_analysis'] = 'working'

            # 2. 测试传统代理状态
            self.logger.info("🔍 测试传统代理状态...")
            for agent_name, agent in [
                ('technical', self.technical_agent),
                ('enhanced_technical', self.enhanced_technical_agent),
                ('gua', self.gua_agent),
                ('flow', self.fund_flow_agent),
                ('soros', self.soros_agent)
            ]:
                try:
                    result = agent.process({'fund_code': fund_code})
                    if 'error' in result:
                        diagnosis['component_status'][agent_name] = 'error'
                        diagnosis['issues_found'].append(f"{agent_name} agent has errors")
                    else:
                        diagnosis['component_status'][agent_name] = 'working'
                except Exception as e:
                    diagnosis['component_status'][agent_name] = 'failed'
                    diagnosis['issues_found'].append(f"{agent_name} agent failed: {str(e)}")

            # 3. V3特性检查
            diagnosis['v3_features_status']['six_dimension_analysis'] = 'available'
            diagnosis['v3_features_status']['signal_conflict_resolution'] = 'available'
            diagnosis['v3_features_status']['dynamic_weight_management'] = 'available'
            diagnosis['v3_features_status']['fractal_validation'] = 'available'

            # 确定整体状态
            if len(diagnosis['issues_found']) == 0:
                diagnosis['overall_status'] = 'healthy'
            elif len(diagnosis['issues_found']) <= 2:
                diagnosis['overall_status'] = 'warning'
            else:
                diagnosis['overall_status'] = 'critical'

            self.logger.info(f"✅ V3系统诊断完成 - 状态: {diagnosis['overall_status']}")

        except Exception as e:
            diagnosis['overall_status'] = 'critical'
            diagnosis['issues_found'].append(f"System diagnosis failed: {str(e)}")
            self.logger.error(f"V3 system diagnosis error: {e}")

        return diagnosis

    def run_ml_enhanced_backtest(self, fund_codes: List[str] = None,
                                days: int = 252, config_type: str = 'default') -> Dict[str, Any]:
        """
        运行ML增强回测

        Args:
            fund_codes: 基金代码列表
            days: 回测天数
            config_type: 配置类型 ('default', 'conservative', 'aggressive')

        Returns:
            回测结果
        """
        if not self.ml_backtest_available:
            print("❌ ML回测功能不可用，请安装相关依赖")
            return {'error': 'ML backtest not available'}

        print(f"\n🤖 启动ML增强回测 (配置: {config_type})")
        print("="*80)

        # 选择配置
        if config_type == 'conservative':
            self.ml_config = MLBacktestConfig.create_conservative_config()
        elif config_type == 'aggressive':
            self.ml_config = MLBacktestConfig.create_aggressive_config()
        else:
            self.ml_config = MLBacktestConfig.create_default_config()

        # 初始化回测系统
        if not self.backtest_system:
            backtest_config = self.ml_config.get_advanced_backtest_config()
            self.backtest_system = AdvancedBacktestSystem(config=backtest_config)

        # 使用默认基金列表如果未指定
        if fund_codes is None:
            fund_codes = self.buy_fund_list[:3]  # 使用前3个基金进行演示

        results = {}

        for fund_code in fund_codes:
            print(f"\n📊 回测基金: {fund_code}")

            try:
                # 获取历史数据
                data = self._get_historical_data(fund_code, days)

                if data is None or len(data) < 100:
                    print(f"❌ {fund_code} 数据不足")
                    results[fund_code] = {'error': 'Insufficient data'}
                    continue

                # 运行ML增强回测
                ml_result = self.backtest_system.run_ml_enhanced_backtest(
                    data, target_col='close', train_model=True
                )

                # 运行传统回测比较
                comparison_result = self.backtest_system.compare_traditional_vs_ml(data)

                results[fund_code] = {
                    'ml_backtest': ml_result,
                    'comparison': comparison_result,
                    'data_points': len(data)
                }

                # 显示简要结果
                self._display_backtest_summary(fund_code, ml_result, comparison_result)

            except Exception as e:
                print(f"❌ {fund_code} 回测失败: {e}")
                results[fund_code] = {'error': str(e)}

        # 生成总体报告
        self._generate_ml_backtest_report(results)

        return results

"""
组合风险评估器
评估投资组合的风险水平和集中度
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict

from .data_structures import PositionInfo, FundRiskProfile, RiskAlert


class PortfolioRiskAssessor:
    """
    @class PortfolioRiskAssessor
    @brief 组合风险评估器
    @details 评估投资组合的各种风险指标
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.positions = {}  # {fund_code: PositionInfo}
        self.fund_profiles = {}  # {fund_code: FundRiskProfile}
        self.sector_mapping = self._load_sector_mapping()
        self.correlation_matrix = {}
        
    def _load_sector_mapping(self) -> Dict[str, str]:
        """加载基金行业映射"""
        # 简化的行业映射，实际应该从数据库或配置文件加载
        return {
            '513030': '科技',
            '513080': '科技', 
            '513500': '科技',
            '513520': '科技',
            '513300': '科技',
            '513850': '医药',
            '159329': '医药',
            '159561': '新能源',
            '520830': '新能源',
            '159567': '新能源',
            '518880': '黄金',
            '601398': '银行'
        }
    
    def update_position(self, position: PositionInfo):
        """更新持仓信息"""
        self.positions[position.fund_code] = position
        self.logger.debug(f"更新持仓: {position.fund_code}, 份额: {position.shares}")
    
    def remove_position(self, fund_code: str):
        """移除持仓"""
        if fund_code in self.positions:
            del self.positions[fund_code]
            self.logger.debug(f"移除持仓: {fund_code}")
    
    def assess_portfolio_risk(self, new_fund_code: str = None, 
                            new_position_size: float = 0.0) -> Dict[str, Any]:
        """
        @brief 评估组合风险
        @param new_fund_code: 新增基金代码（可选）
        @param new_position_size: 新增仓位大小（可选）
        @return: 风险评估结果
        """
        try:
            # 计算当前组合状态
            current_portfolio = self._calculate_portfolio_metrics()
            
            # 如果有新增仓位，计算影响
            impact_analysis = None
            if new_fund_code and new_position_size > 0:
                impact_analysis = self._analyze_new_position_impact(
                    new_fund_code, new_position_size
                )
            
            # 风险检查
            risk_violations = self._check_risk_violations(current_portfolio, impact_analysis)
            
            # 生成风险评分
            risk_score = self._calculate_risk_score(current_portfolio, risk_violations)
            
            # 生成建议
            recommendations = self._generate_recommendations(
                current_portfolio, risk_violations, impact_analysis
            )
            
            return {
                'assessment_time': datetime.now().isoformat(),
                'portfolio_metrics': current_portfolio,
                'new_position_impact': impact_analysis,
                'risk_violations': risk_violations,
                'risk_score': risk_score,
                'risk_level': self._get_risk_level(risk_score),
                'recommendations': recommendations,
                'alerts': self._generate_risk_alerts(risk_violations)
            }
            
        except Exception as e:
            self.logger.error(f"组合风险评估失败: {str(e)}")
            return {
                'error': str(e),
                'risk_score': 1.0,
                'risk_level': 'critical'
            }
    
    def _calculate_portfolio_metrics(self) -> Dict[str, Any]:
        """计算组合指标"""
        if not self.positions:
            return {
                'total_value': 0.0,
                'position_count': 0,
                'sector_distribution': {},
                'concentration_metrics': {},
                'correlation_metrics': {}
            }
        
        # 计算总价值
        total_value = sum(pos.shares * pos.current_price for pos in self.positions.values())
        
        # 计算仓位分布
        position_weights = {}
        for fund_code, position in self.positions.items():
            position_value = position.shares * position.current_price
            position_weights[fund_code] = position_value / total_value if total_value > 0 else 0
        
        # 计算行业分布
        sector_distribution = self._calculate_sector_distribution(position_weights)
        
        # 计算集中度指标
        concentration_metrics = self._calculate_concentration_metrics(position_weights)
        
        # 计算相关性指标
        correlation_metrics = self._calculate_correlation_metrics(position_weights)
        
        return {
            'total_value': total_value,
            'position_count': len(self.positions),
            'position_weights': position_weights,
            'sector_distribution': sector_distribution,
            'concentration_metrics': concentration_metrics,
            'correlation_metrics': correlation_metrics
        }
    
    def _calculate_sector_distribution(self, position_weights: Dict[str, float]) -> Dict[str, float]:
        """计算行业分布"""
        sector_weights = defaultdict(float)
        
        for fund_code, weight in position_weights.items():
            sector = self.sector_mapping.get(fund_code, '其他')
            sector_weights[sector] += weight
        
        return dict(sector_weights)
    
    def _calculate_concentration_metrics(self, position_weights: Dict[str, float]) -> Dict[str, float]:
        """计算集中度指标"""
        if not position_weights:
            return {}
        
        weights = list(position_weights.values())
        
        # 赫芬达尔指数（HHI）
        hhi = sum(w**2 for w in weights)
        
        # 最大权重
        max_weight = max(weights) if weights else 0
        
        # 前三大权重
        top3_weight = sum(sorted(weights, reverse=True)[:3])
        
        return {
            'herfindahl_index': hhi,
            'max_position_weight': max_weight,
            'top3_weight': top3_weight,
            'effective_positions': 1 / hhi if hhi > 0 else 0
        }
    
    def _calculate_correlation_metrics(self, position_weights: Dict[str, float]) -> Dict[str, float]:
        """计算相关性指标"""
        # 简化的相关性计算，实际应该基于历史价格数据
        fund_codes = list(position_weights.keys())
        
        if len(fund_codes) < 2:
            return {'average_correlation': 0.0, 'max_correlation': 0.0}
        
        # 基于行业的简化相关性估算
        correlations = []
        for i, fund1 in enumerate(fund_codes):
            for fund2 in fund_codes[i+1:]:
                sector1 = self.sector_mapping.get(fund1, '其他')
                sector2 = self.sector_mapping.get(fund2, '其他')
                
                # 同行业相关性高，不同行业相关性低
                if sector1 == sector2:
                    correlation = 0.8  # 同行业高相关性
                else:
                    correlation = 0.3  # 不同行业低相关性
                
                correlations.append(correlation)
        
        return {
            'average_correlation': np.mean(correlations) if correlations else 0.0,
            'max_correlation': max(correlations) if correlations else 0.0
        }
    
    def _analyze_new_position_impact(self, fund_code: str, position_size: float) -> Dict[str, Any]:
        """分析新仓位的影响"""
        # 模拟添加新仓位
        current_total = sum(pos.shares * pos.current_price for pos in self.positions.values())
        new_total = current_total + position_size
        
        # 计算新的权重分布
        new_weights = {}
        for code, pos in self.positions.items():
            new_weights[code] = (pos.shares * pos.current_price) / new_total
        new_weights[fund_code] = position_size / new_total
        
        # 计算影响
        new_sector_dist = self._calculate_sector_distribution(new_weights)
        new_concentration = self._calculate_concentration_metrics(new_weights)
        
        return {
            'new_position_weight': position_size / new_total,
            'new_sector_distribution': new_sector_dist,
            'new_concentration_metrics': new_concentration,
            'sector_impact': self._calculate_sector_impact(fund_code, new_sector_dist),
            'concentration_impact': self._calculate_concentration_impact(new_concentration)
        }
    
    def _calculate_sector_impact(self, fund_code: str, new_sector_dist: Dict[str, float]) -> Dict[str, Any]:
        """计算行业影响"""
        sector = self.sector_mapping.get(fund_code, '其他')
        new_sector_weight = new_sector_dist.get(sector, 0.0)
        
        return {
            'target_sector': sector,
            'new_sector_weight': new_sector_weight,
            'sector_limit_exceeded': new_sector_weight > 0.4,  # 40%行业限制
            'risk_level': 'high' if new_sector_weight > 0.4 else 'medium' if new_sector_weight > 0.3 else 'low'
        }
    
    def _calculate_concentration_impact(self, new_concentration: Dict[str, float]) -> Dict[str, Any]:
        """计算集中度影响"""
        max_weight = new_concentration.get('max_position_weight', 0.0)
        hhi = new_concentration.get('herfindahl_index', 0.0)
        
        return {
            'new_max_weight': max_weight,
            'new_hhi': hhi,
            'position_limit_exceeded': max_weight > 0.2,  # 20%单仓位限制
            'concentration_risk': 'high' if hhi > 0.3 else 'medium' if hhi > 0.2 else 'low'
        }
    
    def _check_risk_violations(self, portfolio_metrics: Dict[str, Any], 
                             impact_analysis: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """检查风险违规"""
        violations = []
        
        # 检查单仓位限制
        concentration = portfolio_metrics.get('concentration_metrics', {})
        max_weight = concentration.get('max_position_weight', 0.0)
        if max_weight > 0.2:
            violations.append({
                'type': 'position_limit',
                'severity': 'high',
                'message': f'单仓位权重 {max_weight:.1%} 超过20%限制',
                'current_value': max_weight,
                'limit': 0.2
            })
        
        # 检查行业集中度
        sector_dist = portfolio_metrics.get('sector_distribution', {})
        for sector, weight in sector_dist.items():
            if weight > 0.4:
                violations.append({
                    'type': 'sector_concentration',
                    'severity': 'medium',
                    'message': f'{sector}行业权重 {weight:.1%} 超过40%限制',
                    'current_value': weight,
                    'limit': 0.4
                })
        
        # 检查总体集中度
        hhi = concentration.get('herfindahl_index', 0.0)
        if hhi > 0.3:
            violations.append({
                'type': 'portfolio_concentration',
                'severity': 'medium',
                'message': f'组合集中度过高 (HHI: {hhi:.3f})',
                'current_value': hhi,
                'limit': 0.3
            })
        
        # 检查持仓数量
        position_count = portfolio_metrics.get('position_count', 0)
        if position_count > 15:
            violations.append({
                'type': 'position_count',
                'severity': 'low',
                'message': f'持仓数量 {position_count} 过多，建议控制在15个以内',
                'current_value': position_count,
                'limit': 15
            })
        elif position_count < 3 and position_count > 0:
            violations.append({
                'type': 'position_count',
                'severity': 'medium',
                'message': f'持仓数量 {position_count} 过少，分散化不足',
                'current_value': position_count,
                'limit': 3
            })
        
        # 如果有新仓位影响分析，检查新增风险
        if impact_analysis:
            sector_impact = impact_analysis.get('sector_impact', {})
            if sector_impact.get('sector_limit_exceeded', False):
                violations.append({
                    'type': 'new_position_sector_risk',
                    'severity': 'high',
                    'message': f'新增仓位将导致{sector_impact["target_sector"]}行业超配',
                    'current_value': sector_impact['new_sector_weight'],
                    'limit': 0.4
                })
            
            concentration_impact = impact_analysis.get('concentration_impact', {})
            if concentration_impact.get('position_limit_exceeded', False):
                violations.append({
                    'type': 'new_position_size_risk',
                    'severity': 'high',
                    'message': '新增仓位将导致单仓位超过20%限制',
                    'current_value': concentration_impact['new_max_weight'],
                    'limit': 0.2
                })
        
        return violations
    
    def _calculate_risk_score(self, portfolio_metrics: Dict[str, Any], 
                            violations: List[Dict[str, Any]]) -> float:
        """计算风险评分"""
        base_score = 0.0
        
        # 基于违规情况计分
        for violation in violations:
            severity = violation['severity']
            if severity == 'critical':
                base_score += 0.4
            elif severity == 'high':
                base_score += 0.3
            elif severity == 'medium':
                base_score += 0.2
            elif severity == 'low':
                base_score += 0.1
        
        # 基于集中度指标调整
        concentration = portfolio_metrics.get('concentration_metrics', {})
        hhi = concentration.get('herfindahl_index', 0.0)
        base_score += hhi * 0.5  # HHI贡献最多0.5分
        
        # 基于相关性调整
        correlation = portfolio_metrics.get('correlation_metrics', {})
        avg_corr = correlation.get('average_correlation', 0.0)
        base_score += max(0, avg_corr - 0.5) * 0.3  # 高相关性增加风险
        
        return min(1.0, base_score)  # 限制在0-1之间
    
    def _get_risk_level(self, risk_score: float) -> str:
        """获取风险等级"""
        if risk_score < 0.3:
            return 'low'
        elif risk_score < 0.6:
            return 'medium'
        elif risk_score < 0.8:
            return 'high'
        else:
            return 'critical'
    
    def _generate_recommendations(self, portfolio_metrics: Dict[str, Any],
                                violations: List[Dict[str, Any]],
                                impact_analysis: Dict[str, Any] = None) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于违规情况生成建议
        for violation in violations:
            if violation['type'] == 'position_limit':
                recommendations.append("建议减少最大仓位至20%以下")
            elif violation['type'] == 'sector_concentration':
                recommendations.append(f"建议减少{violation.get('sector', '')}行业配置")
            elif violation['type'] == 'portfolio_concentration':
                recommendations.append("建议增加持仓分散化")
            elif violation['type'] == 'position_count':
                if violation['current_value'] > violation['limit']:
                    recommendations.append("建议减少持仓数量，聚焦优质标的")
                else:
                    recommendations.append("建议增加持仓数量，提高分散化")
        
        # 基于新仓位影响生成建议
        if impact_analysis:
            sector_impact = impact_analysis.get('sector_impact', {})
            if sector_impact.get('risk_level') == 'high':
                recommendations.append("新增仓位将显著增加行业集中度风险，建议减少仓位或选择其他行业")
        
        return recommendations if recommendations else ["当前组合风险控制良好"]
    
    def _generate_risk_alerts(self, violations: List[Dict[str, Any]]) -> List[RiskAlert]:
        """生成风险预警"""
        alerts = []
        
        for i, violation in enumerate(violations):
            alert = RiskAlert(
                alert_id=f"portfolio_risk_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}",
                fund_code="PORTFOLIO",
                alert_type=violation['type'],
                severity=violation['severity'],
                message=violation['message'],
                current_value=violation['current_value'],
                threshold_value=violation['limit'],
                recommended_action="请查看详细建议",
                alert_time=datetime.now(),
                acknowledged=False
            )
            alerts.append(alert)
        
        return alerts
    
    def get_position_limit_check(self, fund_code: str, position_size: float) -> Dict[str, Any]:
        """检查仓位限制"""
        current_total = sum(pos.shares * pos.current_price for pos in self.positions.values())
        new_total = current_total + position_size
        new_weight = position_size / new_total if new_total > 0 else 0
        
        return {
            'fund_code': fund_code,
            'position_size': position_size,
            'position_weight': new_weight,
            'limit_exceeded': new_weight > 0.2,
            'available_capacity': max(0, 0.2 * new_total - position_size),
            'recommendation': 'reject' if new_weight > 0.2 else 'approve'
        }

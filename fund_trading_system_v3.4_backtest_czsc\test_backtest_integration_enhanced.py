"""
增强版回测集成测试
验证回测功能是否成功集成到交易决策流程中
"""

import sys
import os
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_backtest_signal_validator():
    """测试回测信号验证器"""
    print("🔍 测试回测信号验证器...")
    
    try:
        from fund_trading_system_v3.analyzers.backtest_signal_validator import BacktestSignalValidator
        
        # 创建验证器实例
        validator = BacktestSignalValidator()
        print("   ✅ BacktestSignalValidator 创建成功")
        
        # 测试信号验证
        test_signal = {
            'fund_code': '513500',
            'decision': 'buy',
            'confidence': 0.7,
            'strength': 0.6,
            'signals': ['技术指标买入信号']
        }
        
        result = validator.validate_signal('513500', test_signal)
        print(f"   ✅ 信号验证完成: 质量评分={result.quality_score:.3f}, 建议={result.recommendation}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 回测信号验证器测试失败: {e}")
        traceback.print_exc()
        return False

def test_enhanced_coordinator_with_backtest():
    """测试增强版协调器的回测集成"""
    print("🔗 测试增强版协调器回测集成...")
    
    try:
        from fund_trading_system_v3.coordinators import MultiAgentCoordinatorV3
        
        # 创建协调器实例
        coordinator = MultiAgentCoordinatorV3()
        print("   ✅ MultiAgentCoordinatorV3 创建成功")
        
        # 检查是否包含回测验证器
        if hasattr(coordinator, 'backtest_validator'):
            print("   ✅ 回测验证器已集成到协调器")
        else:
            print("   ❌ 回测验证器未集成到协调器")
            return False
        
        # 测试协调分析（包含回测验证）
        print("   🧪 执行包含回测验证的协调分析...")
        analysis_result = coordinator.coordinate_analysis('513500')
        
        if 'error' not in analysis_result:
            # 检查是否包含回测验证结果
            if 'backtest_validation' in analysis_result:
                backtest_info = analysis_result['backtest_validation']
                print(f"   ✅ 回测验证结果: 质量评分={backtest_info.get('quality_score', 0):.3f}")
                print(f"   📊 回测建议: {backtest_info.get('recommendation', 'unknown')}")
                
                # 检查决策是否被回测结果调整
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                if 'backtest_validation' in enhanced_decision:
                    print("   ✅ 决策已根据回测结果调整")
                else:
                    print("   ⚠️ 决策未根据回测结果调整")
                
                return True
            else:
                print("   ❌ 分析结果中缺少回测验证信息")
                return False
        else:
            print(f"   ❌ 协调分析失败: {analysis_result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"   ❌ 增强版协调器回测集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_trading_system_with_backtest():
    """测试交易系统的回测集成"""
    print("🚀 测试交易系统回测集成...")
    
    try:
        from fund_trading_system_v3.system.enhanced_trading_system import EnhancedFundTradingSystemV3
        
        # 创建交易系统实例
        trading_system = EnhancedFundTradingSystemV3()
        print("   ✅ EnhancedFundTradingSystemV3 创建成功")
        
        # 测试单个基金分析（包含回测）
        print("   🧪 执行包含回测的基金分析...")
        analysis_result = trading_system.analyze_fund_v3('513500')
        
        if 'error' not in analysis_result:
            # 检查回测验证结果
            if 'backtest_validation' in analysis_result:
                backtest_validation = analysis_result['backtest_validation']
                quality_score = backtest_validation.get('quality_score', 0)
                recommendation = backtest_validation.get('recommendation', 'unknown')
                
                print(f"   ✅ 回测验证集成成功")
                print(f"   📊 质量评分: {quality_score:.3f}")
                print(f"   💡 回测建议: {recommendation}")
                
                # 检查决策调整
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                if 'backtest_adjustment' in enhanced_decision:
                    adjustment = enhanced_decision['backtest_adjustment']
                    print(f"   🎯 决策调整: {adjustment}")
                
                return True
            else:
                print("   ❌ 分析结果中缺少回测验证")
                return False
        else:
            print(f"   ❌ 基金分析失败: {analysis_result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"   ❌ 交易系统回测集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_backtest_performance_impact():
    """测试回测对交易决策的影响"""
    print("📈 测试回测对交易决策的影响...")
    
    try:
        from fund_trading_system_v3.system.enhanced_trading_system import EnhancedFundTradingSystemV3
        
        trading_system = EnhancedFundTradingSystemV3()
        
        # 分析多个基金，观察回测的影响
        test_funds = ['513500', '513080', '513300']
        backtest_impacts = []
        
        for fund_code in test_funds:
            try:
                print(f"   🔍 分析基金 {fund_code}...")
                result = trading_system.analyze_fund_v3(fund_code)
                
                if 'error' not in result:
                    enhanced_decision = result.get('enhanced_decision', {})
                    backtest_validation = result.get('backtest_validation', {})
                    
                    original_decision = enhanced_decision.get('decision', 'hold')
                    original_confidence = enhanced_decision.get('confidence', 0.0)
                    
                    # 检查回测调整
                    backtest_adjustment = enhanced_decision.get('backtest_adjustment', 'none')
                    quality_score = backtest_validation.get('quality_score', 0.5)
                    recommendation = backtest_validation.get('recommendation', 'maintain')
                    
                    impact_info = {
                        'fund_code': fund_code,
                        'original_decision': original_decision,
                        'original_confidence': original_confidence,
                        'backtest_adjustment': backtest_adjustment,
                        'quality_score': quality_score,
                        'recommendation': recommendation
                    }
                    
                    backtest_impacts.append(impact_info)
                    
                    print(f"     📊 {fund_code}: {original_decision} | 质量={quality_score:.2f} | 调整={backtest_adjustment}")
                
            except Exception as e:
                print(f"     ❌ {fund_code} 分析失败: {e}")
        
        # 统计回测影响
        if backtest_impacts:
            total_funds = len(backtest_impacts)
            adjusted_funds = len([f for f in backtest_impacts if f['backtest_adjustment'] != 'signal_maintained'])
            high_quality_signals = len([f for f in backtest_impacts if f['quality_score'] >= 0.7])
            
            print(f"   📊 回测影响统计:")
            print(f"     • 总分析基金: {total_funds}")
            print(f"     • 决策被调整: {adjusted_funds} ({adjusted_funds/total_funds*100:.1f}%)")
            print(f"     • 高质量信号: {high_quality_signals} ({high_quality_signals/total_funds*100:.1f}%)")
            
            return True
        else:
            print("   ❌ 没有成功分析的基金")
            return False
        
    except Exception as e:
        print(f"   ❌ 回测影响测试失败: {e}")
        traceback.print_exc()
        return False

def test_backtest_engine_enhancement():
    """测试增强版回测引擎"""
    print("⚙️ 测试增强版回测引擎...")
    
    try:
        from fund_trading_system_v3 import BacktestEngine
        import pandas as pd
        import numpy as np
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        print("   ✅ BacktestEngine 创建成功")
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        test_data = pd.DataFrame({
            'open': np.random.randn(50).cumsum() + 100,
            'high': np.random.randn(50).cumsum() + 102,
            'low': np.random.randn(50).cumsum() + 98,
            'close': np.random.randn(50).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 50)
        }, index=dates)
        
        # 创建测试信号
        signals = pd.Series(0, index=dates)
        signals.iloc[10] = 1  # 买入信号
        signals.iloc[30] = -1  # 卖出信号
        
        # 执行回测
        result = engine.run_backtest(test_data, signals)
        
        # 验证回测结果
        required_metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'trade_count']
        for metric in required_metrics:
            if metric in result:
                print(f"   ✅ {metric}: {result[metric]:.4f}")
            else:
                print(f"   ❌ 缺少指标: {metric}")
                return False
        
        print("   ✅ 回测引擎功能验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 回测引擎测试失败: {e}")
        traceback.print_exc()
        return False

def run_enhanced_integration_tests():
    """运行增强版集成测试"""
    print("🚀 开始增强版回测集成测试")
    print("=" * 60)
    
    tests = [
        ("回测引擎增强测试", test_backtest_engine_enhancement),
        ("回测信号验证器测试", test_backtest_signal_validator),
        ("协调器回测集成测试", test_enhanced_coordinator_with_backtest),
        ("交易系统回测集成测试", test_trading_system_with_backtest),
        ("回测决策影响测试", test_backtest_performance_impact),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"💥 {test_name} - 异常: {str(e)}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = len([r for r in results if r[1]])
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} - {test_name}")
    
    print(f"\n🏁 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有回测集成测试通过！")
        print("💡 回测功能已成功集成到交易决策流程中")
        print("📈 系统现在能够:")
        print("   • 使用历史数据验证信号质量")
        print("   • 根据回测结果优化信号强度")
        print("   • 基于回测表现调整决策置信度")
        print("   • 提供量化的信号质量评分")
        return True
    else:
        print("⚠️ 部分测试失败，请检查集成问题")
        return False

if __name__ == '__main__':
    """增强版回测集成测试主程序"""
    success = run_enhanced_integration_tests()
    
    if success:
        print(f"\n🚀 回测集成验证完成 - 系统已准备就绪！")
        print("💰 现在可以运行 main.py 体验增强版交易系统")
    else:
        print(f"\n🔧 需要修复集成问题后再次测试")
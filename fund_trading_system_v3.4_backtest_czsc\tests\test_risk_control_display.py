"""
测试风控显示效果
"""

import sys
import os
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3
from core.data_structures import DimensionEvaluationResult


def test_risk_control_display():
    """测试风控显示效果"""
    print("测试风控显示效果")
    print("=" * 60)
    
    # 创建交易系统
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    coordinator = trading_system.coordinator
    
    # 测试1：风控阻止买入的情况
    print("\n📊 测试1：风控阻止买入的情况")
    print("-" * 50)
    
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        # 设置技术分析返回不符合风控条件的指标
        mock_tech.return_value = {
            'buy_signal': True,
            'technical_indicators': {
                'bb_position': 0.9,  # 高于上轨
                'rsi': 75,           # RSI过高
                'volume_ratio': 0.8, # 成交量不足
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95
            },
            'confidence': 0.8
        }
        
        mock_gua.return_value = {'gua_score': 0.6}
        mock_flow.return_value = {'high_liquidity': True}
        
        # 设置增强决策返回买入决策
        mock_enhanced.return_value = {
            'decision': 'buy',
            'confidence': 0.75,
            'weighted_score': 0.612,
            'dimension_evaluations': {
                '趋势': DimensionEvaluationResult('趋势', 'up', 0.39, 0.7, [], 'good'),
                '波动性': DimensionEvaluationResult('波动性', 'medium', 0.67, 0.6, [], 'fair'),
                '流动性': DimensionEvaluationResult('流动性', 'good', 1.00, 0.9, [], 'good')
            }
        }
        
        # 执行分析并显示结果
        analysis_result = trading_system.analyze_fund_v3('159561')
        trading_system.display_detailed_analysis_result(analysis_result)
    
    # 测试2：风控允许买入的情况
    print("\n\n📊 测试2：风控允许买入的情况")
    print("-" * 50)
    
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        # 设置技术分析返回符合风控条件的指标
        mock_tech.return_value = {
            'buy_signal': True,
            'technical_indicators': {
                'bb_position': 0.01,  # 低于下轨
                'rsi': 40,            # 适中RSI
                'volume_ratio': 1.5,  # 充足成交量
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95
            },
            'confidence': 0.8
        }
        
        mock_gua.return_value = {'gua_score': 0.7}
        mock_flow.return_value = {'high_liquidity': True}
        
        # 设置增强决策返回买入决策
        mock_enhanced.return_value = {
            'decision': 'buy',
            'confidence': 0.82,
            'weighted_score': 0.745,
            'dimension_evaluations': {
                '趋势': DimensionEvaluationResult('趋势', 'up', 0.75, 0.8, [], 'good'),
                '波动性': DimensionEvaluationResult('波动性', 'low', 0.25, 0.8, [], 'good'),
                '流动性': DimensionEvaluationResult('流动性', 'good', 0.95, 0.9, [], 'good')
            }
        }
        
        # 执行分析并显示结果
        analysis_result = trading_system.analyze_fund_v3('513500')
        trading_system.display_detailed_analysis_result(analysis_result)
    
    # 测试3：持有决策的情况
    print("\n\n📊 测试3：持有决策的情况")
    print("-" * 50)
    
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        mock_tech.return_value = {
            'buy_signal': False,
            'technical_indicators': {},
            'confidence': 0.3
        }
        
        mock_gua.return_value = {'gua_score': 0.4}
        mock_flow.return_value = {'high_liquidity': False}
        
        # 设置增强决策返回持有决策
        mock_enhanced.return_value = {
            'decision': 'hold',
            'confidence': 0.45,
            'weighted_score': 0.320,
            'dimension_evaluations': {
                '趋势': DimensionEvaluationResult('趋势', 'sideways', 0.50, 0.6, [], 'fair'),
                '波动性': DimensionEvaluationResult('波动性', 'medium', 0.55, 0.6, [], 'fair'),
                '流动性': DimensionEvaluationResult('流动性', 'fair', 0.60, 0.7, [], 'fair')
            }
        }
        
        # 执行分析并显示结果
        analysis_result = trading_system.analyze_fund_v3('601398')
        trading_system.display_detailed_analysis_result(analysis_result)


def main():
    """主测试函数"""
    print("🎨 风控显示效果测试")
    print("=" * 60)
    print("本测试将展示以下场景的显示效果:")
    print("1. 风控阻止买入 - 显示详细原因")
    print("2. 风控允许买入 - 正常显示")
    print("3. 持有决策 - 无风控干预")
    print("=" * 60)
    
    try:
        test_risk_control_display()
        
        print("\n" + "=" * 60)
        print("🎉 风控显示效果测试完成！")
        print("=" * 60)
        print("✅ 新的显示特性:")
        print("   ✅ 显示原始分析决策")
        print("   ✅ 显示风控干预信息")
        print("   ✅ 显示具体违规原因")
        print("   ✅ 显示最终执行决策")
        print("   ✅ 显示维度评估详情")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

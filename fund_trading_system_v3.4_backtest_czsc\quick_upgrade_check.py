#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速升级检查
验证if_fq参数是否已经完全移除
"""

import sys
import os
import subprocess

def check_if_fq_removal():
    """检查if_fq参数是否已完全移除"""
    print("🔍 检查if_fq参数移除情况...")
    
    try:
        # 搜索项目中的if_fq使用
        result = subprocess.run(
            ['grep', '-r', 'if_fq', '.', '--include=*.py'],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("❌ 仍有if_fq参数使用:")
            print(result.stdout)
            return False
        else:
            print("✅ if_fq参数已完全移除")
            return True
            
    except FileNotFoundError:
        # Windows系统可能没有grep命令，使用Python搜索
        print("使用Python搜索if_fq...")
        return search_if_fq_python()

def search_if_fq_python():
    """使用Python搜索if_fq"""
    import glob
    
    found_if_fq = []
    
    # 搜索所有Python文件，排除检查脚本自身
    for py_file in glob.glob('**/*.py', recursive=True):
        # 排除检查脚本自身
        if 'quick_upgrade_check.py' in py_file or 'test_' in py_file:
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines, 1):
                    if 'if_fq' in line:
                        found_if_fq.append(f"{py_file}:{i}: {line.strip()}")
        except Exception:
            continue
    
    if found_if_fq:
        print("❌ 仍有if_fq参数使用:")
        for item in found_if_fq:
            print(f"  {item}")
        return False
    else:
        print("✅ if_fq参数已完全移除")
        return True

def check_symbol_field_addition():
    """检查symbol字段是否已添加"""
    print("\n🔍 检查symbol字段添加情况...")
    
    try:
        # 检查enhanced_data_fetcher.py中的symbol字段
        fetcher_file = 'core/enhanced_data_fetcher.py'
        
        if not os.path.exists(fetcher_file):
            print(f"❌ 文件不存在: {fetcher_file}")
            return False
        
        with open(fetcher_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("symbol字段添加到required_cols", "'symbol'" in content and "required_cols" in content),
            ("symbol字段赋值", "df[col] = symbol" in content),
            ("去重逻辑", "drop_duplicates" in content),
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {check_name}: {status}")
            if not check_result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_coordinator_import_fix():
    """检查协调器导入修复"""
    print("\n🔍 检查协调器导入修复...")
    
    try:
        coordinator_file = 'coordinators/multi_agent_coordinator.py'
        
        if not os.path.exists(coordinator_file):
            print(f"❌ 文件不存在: {coordinator_file}")
            return False
        
        with open(coordinator_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查导入修复
        checks = [
            ("正确的数据获取器导入", "from core.enhanced_data_fetcher import get_kline" in content),
            ("CZSC导入", "from simpleczsc import KlineAnalyze" in content),
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {check_name}: {status}")
            if not check_result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_basic_imports():
    """测试基本导入是否正常"""
    print("\n🔍 测试基本导入...")
    
    tests = [
        ("enhanced_data_fetcher", "from core.enhanced_data_fetcher import get_kline"),
        ("simpleczsc", "from simpleczsc import KlineAnalyze"),
        ("multi_agent_coordinator", "from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3"),
    ]
    
    all_passed = True
    
    for test_name, import_statement in tests:
        try:
            exec(import_statement)
            print(f"  {test_name}: ✅")
        except Exception as e:
            print(f"  {test_name}: ❌ ({e})")
            all_passed = False
    
    return all_passed

def main():
    """主检查函数"""
    print("🎯 快速升级检查")
    print("="*60)
    
    checks = [
        ("if_fq参数移除", check_if_fq_removal),
        ("symbol字段添加", check_symbol_field_addition),
        ("协调器导入修复", check_coordinator_import_fix),
        ("基本导入测试", test_basic_imports),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 快速检查结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 检查结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有检查通过！代码修复成功")
        print("\n💡 说明:")
        print("   - if_fq参数已完全移除")
        print("   - symbol字段已正确添加")
        print("   - 数据去重逻辑已添加")
        print("   - 协调器导入已修复")
        print("   - 基本模块导入正常")
        print("\n⚠️ 注意:")
        print("   - 数据获取可能因网络问题失败，这是正常的")
        print("   - 核心代码修复已完成，功能应该正常")
    else:
        print("⚠️ 部分检查失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
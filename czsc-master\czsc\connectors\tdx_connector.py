# -*- coding: utf-8 -*-
"""
author: czsc contributor
create_dt: 2023/10/10 12:00
describe: 通达信数据连接器，基于QATdx获取股票数据并转换为czsc库的RawBar对象
"""

import pandas as pd
import loguru
from typing import List
from datetime import datetime
from czsc.objects import RawBar
from czsc.enum import Freq
from czsc.utils.bar_generator import resample_bars

try:
    from QUANTAXIS.QAFetch import QATdx
except ImportError:
    print("请安装QUANTAXIS库以使用QATdx数据源：pip install quantaxis")


def get_raw_bars(symbol: str, freq: str, sdt: str, edt: str, fq: str = "前复权", **kwargs) -> List[RawBar]:
    """
    获取 CZSC 库定义的标准 RawBar 对象列表，基于QATdx数据源

    :param symbol: 标的代码，如 "000001"
    :param freq: 周期，支持 Freq 对象，或者字符串，如
            '1分钟', '5分钟', '15分钟', '30分钟', '60分钟', '日线', '周线', '月线', '季线', '年线'
    :param sdt: 开始时间，如 "2020-01-01"
    :param edt: 结束时间，如 "2020-12-31"
    :param fq: 除权类型，可选值：'前复权', '后复权', '不复权'
    :param kwargs: 其他参数
        - ip: str, 通达信服务器IP
        - port: int, 通达信服务器端口
        - logger: logger对象
    :return: RawBar 对象列表
    """
    logger = kwargs.get("logger", loguru.logger)
    ip = kwargs.get("ip", None)
    port = kwargs.get("port", None)
    
    # 转换周期格式
    freq = Freq(freq)
    
    # 根据不同周期选择不同的数据获取函数和参数
    if freq == Freq.D:
        _type = "1"  # 日线
    elif freq == Freq.W:
        _type = "2"  # 周线
    elif freq == Freq.M:
        _type = "3"  # 月线
    elif freq == Freq.F1:
        _type = "8"  # 1分钟线
    elif freq == Freq.F5:
        _type = "0"  # 5分钟线
    elif freq == Freq.F15:
        _type = "1"  # 15分钟线
    elif freq == Freq.F30:
        _type = "2"  # 30分钟线
    elif freq == Freq.F60:
        _type = "3"  # 60分钟线
    else:
        raise ValueError(f"不支持的周期：{freq.value}")
    
    # 计算需要获取的K线数量
    start_date = pd.to_datetime(sdt)
    end_date = pd.to_datetime(edt)
    days = (end_date - start_date).days
    
    # 映射复权参数
    if fq == "前复权":
        if_fq = "01"  # QATdx中前复权的代码
    elif fq == "后复权":
        if_fq = "02"  # QATdx中后复权的代码
    else:  # 不复权
        if_fq = "00"
    
    # 根据周期类型选择获取方式
    if freq.value.endswith("分钟"):
        # 获取分钟级别数据
        if freq == Freq.F1:
            lens = days * 240  # 一天约240根1分钟K线
            df = QATdx.QA_fetch_get_stock_min(symbol, sdt, edt, frequence='1min')
        elif freq == Freq.F5:
            lens = days * 48  # 一天约48根5分钟K线
            df = QATdx.QA_fetch_get_stock_min(symbol, sdt, edt, frequence='5min')
        elif freq == Freq.F15:
            lens = days * 16  # 一天约16根15分钟K线
            df = QATdx.QA_fetch_get_stock_min(symbol, sdt, edt, frequence='15min')
        elif freq == Freq.F30:
            lens = days * 8  # 一天约8根30分钟K线
            df = QATdx.QA_fetch_get_stock_min(symbol, sdt, edt, frequence='30min')
        elif freq == Freq.F60:
            lens = days * 4  # 一天约4根60分钟K线
            df = QATdx.QA_fetch_get_stock_min(symbol, sdt, edt, frequence='60min')
    else:
        # 获取日线级别数据
        if freq == Freq.D:
            df = QATdx.QA_fetch_get_stock_day(symbol, sdt, edt, frequence='day')
            df.rename(columns={"date": "datetime"}, inplace=True)
        elif freq == Freq.W:
            df = QATdx.QA_fetch_get_stock_day(symbol, sdt, edt, frequence='week')
            df.rename(columns={"date": "datetime"}, inplace=True)
        elif freq == Freq.M:
            df = QATdx.QA_fetch_get_stock_day(symbol, sdt, edt, frequence='month')
            df.rename(columns={"date": "datetime"}, inplace=True)
    if df is None or df.empty:
        logger.warning(f"获取 {symbol} 的 {freq.value} 数据失败")
        return []
    
    try:
        # 确保datetime列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
            df['datetime'] = pd.to_datetime(df['datetime'])
            
        # 转换输入日期为datetime对象
        sdt_dt = pd.to_datetime(sdt)
        edt_dt = pd.to_datetime(edt)
        
        # 过滤日期范围
        df = df[(df['datetime'] >= sdt_dt) & (df['datetime'] <= edt_dt)]
        
        # 如果数据获取失败，返回空列表
        if df is None or df.empty:
            logger.warning(f"获取 {symbol} 的 {freq.value} 数据失败或数据为空，时间范围: {sdt} 至 {edt}")
            return []
            
    except Exception as e:
        logger.error(f"过滤 {symbol} 的 {freq.value} 数据时出错: {str(e)}")
        return []
    
    try:
        # 确保数据包含必要的列
        if "amount" not in df.columns:
            df["amount"] = df["vol"] * df["close"]
        
        # 重命名列以符合resample_bars的要求
        if "datetime" in df.columns:
            df.rename(columns={"datetime": "dt"}, inplace=True)
        
        if "code" in df.columns:
            df.rename(columns={"code": "symbol"}, inplace=True)
        elif "symbol" not in df.columns:
            df["symbol"] = symbol
        
        # 确保dt列是datetime类型
        df["dt"] = pd.to_datetime(df["dt"])
        
        # 过滤日期范围
        df = df[(df["dt"] >= pd.to_datetime(sdt)) & (df["dt"] <= pd.to_datetime(edt))]
        
        # 如果过滤后数据为空，返回空列表
        if df.empty:
            logger.warning(f"过滤日期范围后，{symbol} 的 {freq.value} 数据为空")
            return []
        
        # 选择必要的列并重置索引
        df = df[["symbol", "dt", "open", "close", "high", "low", "vol", "amount"]].copy().reset_index(drop=True)
        
        # 转换为RawBar对象列表
        raw_bars = resample_bars(df, target_freq=freq, raw_bars=True)
        logger.info(f"成功获取 {symbol} 的 {freq.value} 数据，共 {len(raw_bars)} 条记录")
        return raw_bars
    except Exception as e:
        logger.exception(f"处理 {symbol} 的 {freq.value} 数据时发生错误: {str(e)}")
        return []


if __name__ == '__main__':

    lt_min = get_raw_bars(symbol='518880',freq=Freq.F60, sdt='2025-05-01 09:30:00', edt='2025-06-03 15:00:00')
    print(lt_min[:2])

    lt = get_raw_bars(symbol='518880',freq=Freq.D, sdt='2025-05-01 09:30:00', edt='2025-06-03 15:00:00')
    print(lt)

"""
风控系统单元测试
测试风控agent和相关组件的功能
"""

import unittest
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.enhanced.risk_control_agent import RiskControlAgent
from core.risk_control_config import RiskControlConfig, get_risk_config
from core.market_environment_assessor import MarketEnvironmentAssessor
from core.portfolio_risk_assessor import PortfolioRiskAssessor
from core.decision_explainer import DecisionExplainer
from core.data_structures import (
    RiskControlResult, PositionInfo, MarketEnvironmentAssessment,
    DimensionEvaluationResult
)


class TestRiskControlAgent(unittest.TestCase):
    """测试风控智能体"""
    
    def setUp(self):
        """测试前准备"""
        self.risk_agent = RiskControlAgent()
        self.test_fund_code = '513500'
        
    def test_risk_agent_initialization(self):
        """测试风控agent初始化"""
        self.assertIsNotNone(self.risk_agent)
        self.assertIsNotNone(self.risk_agent.risk_config)
        self.assertIn('technical_indicators', self.risk_agent.risk_config)
        
    def test_buy_decision_validation_pass(self):
        """测试买入决策验证通过"""
        # 准备测试数据 - 符合买入条件
        test_data = {
            'fund_code': self.test_fund_code,
            'analysis_result': {
                'technical_analysis': {
                    'indicators': {
                        'bb_position': 0.1,  # 低于下轨
                        'rsi': 45,  # 适中RSI
                        'volume_ratio': 1.5,  # 充足成交量
                        'macd_bullish': True
                    }
                },
                'dimension_evaluations': {
                    '波动性': Mock(score=0.4),
                    '流动性': Mock(score=0.8),
                    '情绪': Mock(score=0.6)
                }
            },
            'proposed_decision': 'buy'
        }
        
        result = self.risk_agent.process(test_data)
        
        self.assertEqual(result['fund_code'], self.test_fund_code)
        self.assertEqual(result['original_decision'], 'buy')
        self.assertIn('risk_validation', result)
        
    def test_buy_decision_validation_fail_bollinger(self):
        """测试买入决策因布林线条件失败"""
        # 准备测试数据 - 布林线位置过高
        test_data = {
            'fund_code': self.test_fund_code,
            'analysis_result': {
                'technical_analysis': {
                    'indicators': {
                        'bb_position': 0.8,  # 高于上轨
                        'rsi': 45,
                        'volume_ratio': 1.5
                    }
                }
            },
            'proposed_decision': 'buy'
        }
        
        result = self.risk_agent.process(test_data)
        
        self.assertIn('risk_validation', result)
        risk_validation = result['risk_validation']
        self.assertIn('technical_violations', risk_validation)
        self.assertIn('bollinger_bands', risk_validation['technical_violations'])
        
    def test_buy_decision_validation_fail_rsi(self):
        """测试买入决策因RSI条件失败"""
        test_data = {
            'fund_code': self.test_fund_code,
            'analysis_result': {
                'technical_analysis': {
                    'indicators': {
                        'bb_position': 0.1,
                        'rsi': 75,  # RSI过高
                        'volume_ratio': 1.5
                    }
                }
            },
            'proposed_decision': 'buy'
        }
        
        result = self.risk_agent.process(test_data)
        
        risk_validation = result['risk_validation']
        self.assertIn('technical_violations', risk_validation)
        self.assertIn('rsi', risk_validation['technical_violations'])
        
    def test_non_buy_decision_skip_validation(self):
        """测试非买入决策跳过验证"""
        test_data = {
            'fund_code': self.test_fund_code,
            'analysis_result': {},
            'proposed_decision': 'sell'
        }
        
        result = self.risk_agent.process(test_data)
        
        self.assertEqual(result['final_decision'], 'sell')
        risk_validation = result['risk_validation']
        self.assertIn('note', risk_validation['details'])
        
    def test_detailed_technical_validation(self):
        """测试详细技术验证"""
        technical_data = {
            'indicators': {
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95,
                'rsi': 40,
                'volume_ratio': 1.3,
                'macd_dif': 0.5,
                'macd_dea': 0.3,
                'macd_bullish': True
            }
        }
        
        result = self.risk_agent.validate_buy_conditions(
            self.test_fund_code, 100, technical_data
        )
        
        self.assertEqual(result['fund_code'], self.test_fund_code)
        self.assertIn('technical_analysis', result)
        self.assertIn('bollinger_analysis', result['technical_analysis'])
        self.assertIn('rsi_analysis', result['technical_analysis'])
        self.assertIn('volume_analysis', result['technical_analysis'])


class TestRiskControlConfig(unittest.TestCase):
    """测试风控配置"""
    
    def setUp(self):
        """测试前准备"""
        self.config = RiskControlConfig()
        
    def test_config_initialization(self):
        """测试配置初始化"""
        self.assertIsNotNone(self.config.config)
        self.assertIn('technical_indicators', self.config.config)
        self.assertIn('market_environment', self.config.config)
        self.assertIn('portfolio_risk', self.config.config)
        
    def test_get_technical_config(self):
        """测试获取技术指标配置"""
        bb_config = self.config.get_technical_config('bollinger_bands')
        self.assertIn('buy_position_requirement', bb_config)
        self.assertIn('tolerance', bb_config)
        
    def test_update_config(self):
        """测试更新配置"""
        original_value = self.config.config['technical_indicators']['rsi']['buy_max_threshold']
        new_value = 70
        
        success = self.config.update_config('technical_indicators', 'rsi', {'buy_max_threshold': new_value})
        self.assertTrue(success)
        
        updated_value = self.config.config['technical_indicators']['rsi']['buy_max_threshold']
        self.assertEqual(updated_value, new_value)
        
    def test_validate_config(self):
        """测试配置验证"""
        errors = self.config.validate_config()
        self.assertIsInstance(errors, list)
        
    def test_global_config_instance(self):
        """测试全局配置实例"""
        global_config = get_risk_config()
        self.assertIsInstance(global_config, RiskControlConfig)


class TestMarketEnvironmentAssessor(unittest.TestCase):
    """测试市场环境评估器"""
    
    def setUp(self):
        """测试前准备"""
        self.assessor = MarketEnvironmentAssessor()
        
    def test_assess_market_environment(self):
        """测试市场环境评估"""
        # 准备测试数据
        dimension_evaluations = {
            '趋势': DimensionEvaluationResult('趋势', 'up', 0.7, 0.8, [], 'good'),
            '波动性': DimensionEvaluationResult('波动性', 'medium', 0.5, 0.7, [], 'good'),
            '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
            '情绪': DimensionEvaluationResult('情绪', 'positive', 0.6, 0.7, [], 'good'),
            '结构': DimensionEvaluationResult('结构', 'stable', 0.7, 0.8, [], 'good'),
            '转换': DimensionEvaluationResult('转换', 'low', 0.3, 0.6, [], 'good')
        }
        
        assessment = self.assessor.assess_market_environment(dimension_evaluations)
        
        self.assertIsInstance(assessment, MarketEnvironmentAssessment)
        self.assertIn(assessment.market_regime, ['bull', 'bear', 'sideways'])
        self.assertIn(assessment.volatility_level, ['low', 'medium', 'high'])
        self.assertIn(assessment.liquidity_condition, ['good', 'fair', 'poor'])
        
    def test_risk_adjustment_factors(self):
        """测试风险调整因子"""
        assessment = MarketEnvironmentAssessment(
            assessment_time=datetime.now(),
            market_regime='bull',
            volatility_level='low',
            liquidity_condition='good',
            sentiment_score=0.7,
            trend_strength=0.8,
            overall_risk_level='low',
            recommended_strategy='积极买入'
        )
        
        factors = self.assessor.get_risk_adjustment_factors(assessment)
        
        self.assertIn('volatility_adjustment', factors)
        self.assertIn('liquidity_adjustment', factors)
        self.assertIn('sentiment_adjustment', factors)
        self.assertIn('regime_adjustment', factors)
        
    def test_should_halt_trading(self):
        """测试是否暂停交易"""
        # 极端风险情况
        high_risk_assessment = MarketEnvironmentAssessment(
            assessment_time=datetime.now(),
            market_regime='bear',
            volatility_level='high',
            liquidity_condition='poor',
            sentiment_score=0.1,
            trend_strength=0.2,
            overall_risk_level='critical',
            recommended_strategy='避免交易'
        )
        
        should_halt = self.assessor.should_halt_trading(high_risk_assessment)
        self.assertTrue(should_halt)
        
        # 正常风险情况
        normal_assessment = MarketEnvironmentAssessment(
            assessment_time=datetime.now(),
            market_regime='bull',
            volatility_level='medium',
            liquidity_condition='good',
            sentiment_score=0.6,
            trend_strength=0.7,
            overall_risk_level='low',
            recommended_strategy='正常交易'
        )
        
        should_halt = self.assessor.should_halt_trading(normal_assessment)
        self.assertFalse(should_halt)


class TestPortfolioRiskAssessor(unittest.TestCase):
    """测试组合风险评估器"""
    
    def setUp(self):
        """测试前准备"""
        self.assessor = PortfolioRiskAssessor()
        
    def test_position_update(self):
        """测试持仓更新"""
        position = PositionInfo(
            fund_code='513500',
            shares=1000,
            average_cost=1.0,
            current_price=1.1,
            unrealized_pnl=100,
            unrealized_pnl_pct=0.1,
            max_profit=100,
            max_profit_pct=0.1,
            current_drawdown=0,
            current_drawdown_pct=0,
            last_update=datetime.now(),
            entry_date=datetime.now(),
            holding_days=1
        )
        
        self.assessor.update_position(position)
        self.assertIn('513500', self.assessor.positions)
        
    def test_assess_portfolio_risk(self):
        """测试组合风险评估"""
        # 添加测试持仓
        position1 = PositionInfo(
            fund_code='513500', shares=1000, average_cost=1.0, current_price=1.1,
            unrealized_pnl=100, unrealized_pnl_pct=0.1, max_profit=100, max_profit_pct=0.1,
            current_drawdown=0, current_drawdown_pct=0, last_update=datetime.now(),
            entry_date=datetime.now(), holding_days=1
        )
        
        self.assessor.update_position(position1)
        
        result = self.assessor.assess_portfolio_risk()
        
        self.assertIn('portfolio_metrics', result)
        self.assertIn('risk_score', result)
        self.assertIn('risk_level', result)
        self.assertIn('recommendations', result)
        
    def test_position_limit_check(self):
        """测试仓位限制检查"""
        result = self.assessor.get_position_limit_check('513500', 10000)
        
        self.assertIn('fund_code', result)
        self.assertIn('position_weight', result)
        self.assertIn('limit_exceeded', result)
        self.assertIn('recommendation', result)


class TestDecisionExplainer(unittest.TestCase):
    """测试决策解释器"""
    
    def setUp(self):
        """测试前准备"""
        self.explainer = DecisionExplainer()
        
    def test_explain_risk_decision(self):
        """测试风控决策解释"""
        risk_result = RiskControlResult(
            fund_code='513500',
            validation_time=datetime.now(),
            passed=False,
            risk_level='high',
            rejection_reasons=['布林线位置过高', 'RSI超买'],
            technical_violations={'bollinger_bands': '位置过高', 'rsi': '超买'},
            market_environment_score=0.3,
            portfolio_risk_score=0.6,
            recommended_action='hold',
            confidence=0.2,
            details={}
        )
        
        explanation = self.explainer.explain_risk_decision(risk_result)
        
        self.assertIn('fund_code', explanation)
        self.assertIn('overall_summary', explanation)
        self.assertIn('detailed_analysis', explanation)
        self.assertIn('risk_factors', explanation)
        self.assertIn('recommendations', explanation)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

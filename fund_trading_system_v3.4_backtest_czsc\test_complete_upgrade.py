#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整升级测试
测试所有修复后的功能是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_enhanced_data_fetcher():
    """测试增强版数据获取器"""
    print("🔍 测试增强版数据获取器...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        
        # 测试日线数据
        print("📊 测试日线数据获取...")
        df_daily = get_kline('518880', freq='D')
        
        if df_daily is None or df_daily.empty:
            print("❌ 日线数据获取失败")
            return False
        
        print(f"✅ 日线数据获取成功: {len(df_daily)}条")
        print(f"📋 包含字段: {list(df_daily.columns)}")
        
        # 检查必要字段
        required_fields = ['dt', 'open', 'high', 'low', 'close', 'vol', 'symbol']
        missing_fields = [field for field in required_fields if field not in df_daily.columns]
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {missing_fields}")
            return False
        
        print("✅ 所有必要字段都存在")
        
        # 测试30分钟数据
        print("\n📊 测试30分钟数据获取...")
        df_30min = get_kline('518880', freq='30min')
        
        if df_30min is None or df_30min.empty:
            print("❌ 30分钟数据获取失败")
            return False
        
        print(f"✅ 30分钟数据获取成功: {len(df_30min)}条")
        
        # 检查时间排序
        is_sorted = df_30min['dt'].is_monotonic_increasing
        print(f"时间排序检查: {'✅ 正确' if is_sorted else '❌ 错误'}")
        
        # 检查重复时间
        duplicated = df_30min['dt'].duplicated().sum()
        print(f"重复时间检查: {'✅ 无重复' if duplicated == 0 else f'❌ 有{duplicated}条重复'}")
        
        return is_sorted and duplicated == 0
        
    except Exception as e:
        print(f"❌ 数据获取器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_czsc_integration():
    """测试CZSC集成"""
    print("\n🔍 测试CZSC集成...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        from simpleczsc import KlineAnalyze
        
        # 获取数据
        df = get_kline('518880', freq='30min')
        
        if df is None or df.empty:
            print("❌ 无法获取数据")
            return False
        
        # 转换为CZSC格式
        kline_list = []
        for _, row in df.iterrows():
            kline_dict = {
                'symbol': str(row['symbol']),
                'dt': row['dt'],
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'vol': float(row['vol'])
            }
            kline_list.append(kline_dict)
        
        # 创建CZSC分析器
        ka = KlineAnalyze(kline_list)
        
        print(f"✅ CZSC分析器创建成功")
        print(f"📊 分析结果:")
        print(f"   - symbol: {ka.symbol}")
        print(f"   - 数据量: {len(kline_list)}条")
        print(f"   - 时间范围: {ka.start_dt} 至 {ka.end_dt}")
        print(f"   - 最新价格: {ka.latest_price}")
        
        # 检查分析结果
        has_fx = hasattr(ka, 'fx_list') and ka.fx_list
        has_bi = hasattr(ka, 'bi_list') and ka.bi_list
        has_xd = hasattr(ka, 'xd_list') and ka.xd_list
        
        print(f"   - 分型(FX): {'✅' if has_fx else '❌'} {len(ka.fx_list) if has_fx else 0}个")
        print(f"   - 笔(BI): {'✅' if has_bi else '❌'} {len(ka.bi_list) if has_bi else 0}个")
        print(f"   - 线段(XD): {'✅' if has_xd else '❌'} {len(ka.xd_list) if has_xd else 0}个")
        
        return has_fx or has_bi  # 至少要有分型或笔数据
        
    except Exception as e:
        print(f"❌ CZSC集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_integration():
    """测试协调器集成"""
    print("\n🔍 测试协调器集成...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        
        # 创建协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试CZSC数据提取
        fund_code = '518880'
        czsc_data = coordinator._extract_czsc_structure_data(fund_code, {})
        
        if not czsc_data or 'error' in czsc_data:
            print(f"❌ CZSC数据提取失败: {czsc_data}")
            return False
        
        print("✅ 协调器CZSC数据提取成功")
        print(f"📊 提取结果:")
        print(f"   - FX分型: {len(czsc_data.get('fx_list', []))}个")
        print(f"   - BI笔: {len(czsc_data.get('bi_list', []))}个")
        print(f"   - XD线段: {len(czsc_data.get('xd_list', []))}个")
        print(f"   - 数据源: {czsc_data.get('data_source', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_backtest():
    """测试增强版回测"""
    print("\n🔍 测试增强版回测...")
    
    try:
        # 添加根目录到路径
        root_dir = os.path.dirname(current_dir)
        if root_dir not in sys.path:
            sys.path.insert(0, root_dir)
        
        from enhanced_backtest import EnhancedBacktest
        
        # 创建回测实例
        backtest = EnhancedBacktest('518880', initial_capital=100000)
        
        # 获取数据
        df = backtest.get_real_data('518880', freq='D')
        
        if df is None or df.empty:
            print("❌ 回测数据获取失败")
            return False
        
        print(f"✅ 回测数据获取成功: {len(df)}条")
        
        # 计算技术指标
        df = backtest.calculate_advanced_indicators(df)
        
        # 运行策略
        df = backtest.run_strategy(df, 'ma_crossover')
        
        # 执行回测
        results_df = backtest.execute_backtest_with_risk_management(df, max_position_pct=0.8)
        
        if results_df is None or results_df.empty:
            print("❌ 回测执行失败")
            return False
        
        print(f"✅ 回测执行成功: {len(results_df)}条结果")
        
        # 计算指标
        metrics = backtest.calculate_comprehensive_metrics(results_df, df)
        
        print(f"📊 回测结果:")
        print(f"   - 总收益率: {metrics['total_return']:.2%}")
        print(f"   - 年化收益: {metrics['annual_return']:.2%}")
        print(f"   - 夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"   - 最大回撤: {metrics['max_drawdown']:.2%}")
        print(f"   - 交易次数: {metrics['total_trades']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强版回测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 完整升级测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    test_results = []
    
    # 测试1: 增强版数据获取器
    result1 = test_enhanced_data_fetcher()
    test_results.append(("增强版数据获取器", result1))
    
    # 测试2: CZSC集成
    result2 = test_czsc_integration()
    test_results.append(("CZSC集成", result2))
    
    # 测试3: 协调器集成
    result3 = test_coordinator_integration()
    test_results.append(("协调器集成", result3))
    
    # 测试4: 增强版回测
    result4 = test_enhanced_backtest()
    test_results.append(("增强版回测", result4))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 完整升级测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 所有测试通过！系统升级成功")
        
        # 保存测试报告
        test_report = {
            'test_date': datetime.now().isoformat(),
            'total_tests': total,
            'passed_tests': passed,
            'success_rate': passed / total,
            'test_results': [
                {'test_name': name, 'result': result} 
                for name, result in test_results
            ],
            'status': 'SUCCESS' if passed == total else 'PARTIAL_SUCCESS'
        }
        
        import json
        with open('complete_upgrade_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)
        
        print("💾 测试报告已保存: complete_upgrade_test_report.json")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
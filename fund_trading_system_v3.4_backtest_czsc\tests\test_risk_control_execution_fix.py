"""
测试风控决策正确传递到执行器
"""

import sys
import os
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
from core.data_structures import DimensionEvaluationResult


def test_risk_control_execution_integration():
    """测试风控决策正确传递到执行器"""
    print("测试风控决策正确传递到执行器")
    print("-" * 50)
    
    # 创建交易系统
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    coordinator = trading_system.coordinator
    
    # Mock各个agent返回不符合风控条件的买入信号
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        # 设置技术分析返回不符合风控条件的指标
        mock_tech.return_value = {
            'buy_signal': True,
            'technical_indicators': {
                'bb_position': 0.9,  # 高于上轨，不符合买入条件
                'rsi': 75,           # RSI过高，不符合买入条件
                'volume_ratio': 0.8, # 成交量不足，不符合买入条件
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95
            },
            'confidence': 0.8
        }
        
        mock_gua.return_value = {'gua_score': 0.6}
        mock_flow.return_value = {'high_liquidity': True}
        
        # 设置增强决策返回买入决策
        mock_enhanced.return_value = {
            'decision': 'buy',
            'confidence': 0.8,
            'dimension_evaluations': {}
        }
        
        # 执行完整的分析和交易决策流程
        analysis_result = trading_system.analyze_fund_v3('601398')
        
        print(f"基金: {analysis_result['fund_code']}")
        print(f"原始决策: {analysis_result.get('enhanced_decision', {}).get('decision', 'unknown')}")
        print(f"最终决策: {analysis_result.get('final_decision', 'unknown')}")
        print(f"风险等级: {analysis_result.get('risk_control', {}).get('risk_level', 'unknown')}")
        
        # 检查风控是否阻止了买入
        original_decision = analysis_result.get('enhanced_decision', {}).get('decision', 'unknown')
        final_decision = analysis_result.get('final_decision', 'unknown')
        
        if original_decision == 'buy' and final_decision == 'hold':
            print("✅ 风控成功阻止了买入决策")
        else:
            print(f"⚠️ 风控未能阻止买入: {original_decision} -> {final_decision}")
        
        # 测试执行器是否使用正确的决策
        print("\n测试执行器决策传递:")
        execution_result = trading_system.execute_trading_decision_v3(analysis_result)
        
        print(f"执行结果: {execution_result}")
        
        # 检查执行器是否使用了风控后的决策
        if execution_result.get('action') == 'hold':
            print("✅ 执行器正确使用了风控后的hold决策")
        elif execution_result.get('action') == 'buy':
            print("❌ 执行器仍然在执行买入，风控决策未生效")
        else:
            print(f"⚠️ 执行器决策: {execution_result.get('action', 'unknown')}")


def test_risk_control_allows_good_buy():
    """测试风控允许符合条件的买入"""
    print("\n测试风控允许符合条件的买入")
    print("-" * 50)
    
    # 创建交易系统
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    coordinator = trading_system.coordinator
    
    # Mock各个agent返回符合风控条件的买入信号
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        # 设置技术分析返回符合风控条件的指标
        mock_tech.return_value = {
            'buy_signal': True,
            'technical_indicators': {
                'bb_position': 0.01,  # 低于下轨，符合买入条件
                'rsi': 40,            # 适中RSI，符合买入条件
                'volume_ratio': 1.5,  # 充足成交量，符合买入条件
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95
            },
            'confidence': 0.8
        }
        
        mock_gua.return_value = {'gua_score': 0.7}
        mock_flow.return_value = {'high_liquidity': True}
        
        # 设置增强决策返回买入决策
        mock_enhanced.return_value = {
            'decision': 'buy',
            'confidence': 0.8,
            'dimension_evaluations': {
                '波动性': DimensionEvaluationResult('波动性', 'low', 0.3, 0.8, [], 'good'),
                '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                '情绪': DimensionEvaluationResult('情绪', 'positive', 0.7, 0.8, [], 'good')
            }
        }
        
        # 执行完整的分析和交易决策流程
        analysis_result = trading_system.analyze_fund_v3('513500')
        
        print(f"基金: {analysis_result['fund_code']}")
        print(f"原始决策: {analysis_result.get('enhanced_decision', {}).get('decision', 'unknown')}")
        print(f"最终决策: {analysis_result.get('final_decision', 'unknown')}")
        
        # 测试执行器
        execution_result = trading_system.execute_trading_decision_v3(analysis_result)
        print(f"执行结果: {execution_result.get('action', 'unknown')}")
        
        # 检查是否允许买入
        if analysis_result.get('final_decision') == 'buy':
            print("✅ 风控允许符合条件的买入")
        else:
            print("⚠️ 风控阻止了买入")


def main():
    """主测试函数"""
    print("🛡️ 风控决策执行器集成测试")
    print("=" * 60)
    
    try:
        test_risk_control_execution_integration()
        test_risk_control_allows_good_buy()
        
        print("\n" + "=" * 60)
        print("🎉 风控决策执行器集成测试完成！")
        print("=" * 60)
        print("✅ 验证结果:")
        print("   ✅ 风控决策正确传递到执行器")
        print("   ✅ 不符合条件的买入被正确阻止")
        print("   ✅ 符合条件的买入被正确允许")
        print("   ✅ 执行器使用风控后的最终决策")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

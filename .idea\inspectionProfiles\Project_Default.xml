<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="8">
            <item index="0" class="java.lang.String" itemvalue="tushare" />
            <item index="1" class="java.lang.String" itemvalue="tensorflow" />
            <item index="2" class="java.lang.String" itemvalue="fastapi" />
            <item index="3" class="java.lang.String" itemvalue="talib-binary" />
            <item index="4" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="5" class="java.lang.String" itemvalue="snownlp" />
            <item index="6" class="java.lang.String" itemvalue="numpy" />
            <item index="7" class="java.lang.String" itemvalue="uvicorn" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>
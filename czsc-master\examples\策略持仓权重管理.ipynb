{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 策略持仓权重管理\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置 Redis 链接信息，用于存储持仓权重信息\n", "import os\n", "os.environ['RWC_REDIS_URL'] = 'redis://***:***@***:6379/0'\n", "\n", "# 也可以使用 dotenv 来设置环境变量\n", "from dotenv import load_dotenv\n", "# load_dotenv(r'path/to/.env', override=True)\n", "load_dotenv(r\"D:\\ZB\\git_repo\\waditu\\czsc\\examples\\test_offline\\.env\", override=True)\n", "\n", "import czsc\n", "import redis\n", "import pandas as pd\n", "\n", "# print(\"redis_url\", os.getenv(\"RWC_REDIS_URL\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 一、写入策略持仓权重\n", "\n", "\n", "策略持仓权重样例数据可以从飞书文档中下载："]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:17:45.154\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m62\u001b[0m - \u001b[1mTestStrategy Weights: 使用传入的 redis 连接池\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["策略元数据： {'name': 'TestStrategy', 'base_freq': '日线', 'key_prefix': 'Weights', 'description': '测试策略：仅用于读写redis测试', 'author': '测试A', 'outsample_sdt': '20220101', 'update_time': '2024-03-09 20:17:47', 'kwargs': '{}'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:17:48.569\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m172\u001b[0m - \u001b[1m输入数据中有 3938513 条权重信号\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["持仓权重数据：\n", "                    dt    symbol  weight  price\n", "0 2018-03-26 09:01:00  SEsc9001     0.0  442.9\n", "1 2018-03-26 09:02:00  SEsc9001     0.0  441.2\n", "2 2018-03-26 09:03:00  SEsc9001     0.0  440.4\n", "3 2018-03-26 09:04:00  SEsc9001     0.0  439.5\n", "4 2018-03-26 09:05:00  SEsc9001     0.0  440.7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:17:49.073\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m182\u001b[0m - \u001b[1m去除单个品种下相邻时间权重相同的数据后，剩余 29906 条权重信号\u001b[0m\n", "\u001b[32m2024-03-09 20:17:49.298\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m199\u001b[0m - \u001b[1m不允许重复写入，已过滤 0 条重复信号\u001b[0m\n", "\u001b[32m2024-03-09 20:17:49.433\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m223\u001b[0m - \u001b[1m索引 0，即将发布 10000 条权重信号\u001b[0m\n", "\u001b[32m2024-03-09 20:17:54.251\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m225\u001b[0m - \u001b[1m已完成 10000 次发布\u001b[0m\n", "\u001b[32m2024-03-09 20:17:54.252\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m223\u001b[0m - \u001b[1m索引 10000，即将发布 10000 条权重信号\u001b[0m\n", "\u001b[32m2024-03-09 20:17:57.557\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m225\u001b[0m - \u001b[1m已完成 20000 次发布\u001b[0m\n", "\u001b[32m2024-03-09 20:17:57.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m223\u001b[0m - \u001b[1m索引 20000，即将发布 9906 条权重信号\u001b[0m\n", "\u001b[32m2024-03-09 20:17:59.983\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mpublish_dataframe\u001b[0m:\u001b[36m225\u001b[0m - \u001b[1m已完成 29905 次发布\u001b[0m\n", "\u001b[32m2024-03-09 20:18:00.196\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mupdate_last\u001b[0m:\u001b[36m105\u001b[0m - \u001b[1m更新 Weights:LAST:TestStrategy 的 last 时间\u001b[0m\n"]}, {"data": {"text/plain": ["29905"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建 BlockingConnectionPool 连接池，必须设置 decode_responses=True 参数，否则返回的数据是 bytes 类型\n", "connection_pool = redis.BlockingConnectionPool.from_url(os.getenv(\"RWC_REDIS_URL\"), decode_responses=True)\n", "\n", "# 策略写入阶段，必须设置 send_heartbeat=True 参数，这样可以在写入数据时自动更新策略的最新写入时间\n", "rwc = czsc.RedisWeightsClient('TestStrategy', connection_pool=connection_pool, key_prefix='Weights', send_heartbeat=True)\n", "\n", "# 首次写入，建议设置一些策略元数据\n", "rwc.set_metadata(description='测试策略：仅用于读写redis测试', base_freq='日线', author='测试A', outsample_sdt='20220101')\n", "\n", "# 查看策略元数据\n", "print(\"策略元数据：\", rwc.metadata)\n", "\n", "# 写入策略持仓权重，样例数据下载：https://s0cqcxuy3p.feishu.cn/wiki/Pf1fw1woQi4iJikbKJmcYToznxb\n", "dfw = pd.read_feather(r\"C:\\Users\\<USER>\\Downloads\\weight_example.feather\")\n", "print(\"持仓权重数据：\\n\", dfw.head())\n", "\n", "# on_bar 模式下按单条数据写入，效率更高，可以实时更新持仓权重\n", "# rwc.publish(**dfw.iloc[0].to_dict())\n", "\n", "# 盘后或者定时执行的策略，批量写入可以提高效率\n", "rwc.publish_dataframe(dfw, overwrite=False, batch_size=10000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 二、读取策略持仓权重\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>base_freq</th>\n", "      <th>key_prefix</th>\n", "      <th>description</th>\n", "      <th>author</th>\n", "      <th>outsample_sdt</th>\n", "      <th>update_time</th>\n", "      <th>kwargs</th>\n", "      <th>heartbeat_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TestStrategy</td>\n", "      <td>日线</td>\n", "      <td>Weights</td>\n", "      <td>测试策略：仅用于读写redis测试</td>\n", "      <td>测试A</td>\n", "      <td>20220101</td>\n", "      <td>2024-03-09 20:17:47</td>\n", "      <td>{}</td>\n", "      <td>2024-03-09 20:19:18</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           name base_freq key_prefix        description author outsample_sdt         update_time kwargs      heartbeat_time\n", "0  TestStrategy        日线    Weights  测试策略：仅用于读写redis测试    测试A      20220101 2024-03-09 20:17:47     {} 2024-03-09 20:19:18"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取 redis 中的策略元数据\n", "dfm = czsc.get_strategy_mates(key_pattern='Weights:META:*')\n", "dfm"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:20:25.875\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m62\u001b[0m - \u001b[1mTestStrategy Weights: 使用传入的 redis 连接池\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>symbol</th>\n", "      <th>weight</th>\n", "      <th>update_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>DLeg9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>DLj9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>SQag9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229558</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>SQag9001</td>\n", "      <td>-0.705882</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229559</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229560</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZSA9001</td>\n", "      <td>0.277778</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229561</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZSF9001</td>\n", "      <td>0.545455</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229562</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.400000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>229563 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                        dt    symbol    weight          update_time\n", "0      2017-01-03 09:01:00  DLeb9001  0.000000  2024-03-09 20:17:49\n", "1      2017-01-03 09:01:00  DLeg9001  0.000000  2024-03-09 20:17:49\n", "2      2017-01-03 09:01:00   DLj9001  0.000000  2024-03-09 20:17:49\n", "3      2017-01-03 09:01:00  SEsc9001  0.000000  2024-03-09 20:17:49\n", "4      2017-01-03 09:01:00  SQag9001  0.000000  2024-03-09 20:17:49\n", "...                    ...       ...       ...                  ...\n", "229558 2023-07-31 14:36:00  SQag9001 -0.705882  2024-03-09 20:17:49\n", "229559 2023-07-31 14:36:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "229560 2023-07-31 14:36:00  ZZSA9001  0.277778  2024-03-09 20:17:49\n", "229561 2023-07-31 14:36:00  ZZSF9001  0.545455  2024-03-09 20:17:49\n", "229562 2023-07-31 14:36:00  ZZUR9001  0.400000  2024-03-09 20:17:49\n", "\n", "[229563 rows x 4 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取 redis 中的指定策略的持仓权重（全部品种，全部历史）\n", "dfw = czsc.get_strategy_weights(strategy_name='TestStrategy', connection_pool=connection_pool, key_prefix='Weights')\n", "dfw"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:20:41.617\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m67\u001b[0m - \u001b[1mTestStrategy Weights: 使用环境变量 RWC_REDIS_URL 创建 redis 连接池\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>symbol</th>\n", "      <th>weight</th>\n", "      <th>update_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2017-01-03 09:01:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2017-01-03 09:06:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2017-01-03 09:06:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2017-01-03 13:45:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.000000</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51009</th>\n", "      <td>2023-07-31 13:36:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51010</th>\n", "      <td>2023-07-31 14:31:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51011</th>\n", "      <td>2023-07-31 14:31:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51012</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51013</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>51014 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                       dt    symbol    weight          update_time\n", "0     2017-01-03 09:01:00  DLeb9001  0.000000  2024-03-09 20:17:49\n", "1     2017-01-03 09:01:00  ZZAP9001  0.000000  2024-03-09 20:17:49\n", "2     2017-01-03 09:06:00  DLeb9001  0.000000  2024-03-09 20:17:49\n", "3     2017-01-03 09:06:00  ZZAP9001  0.000000  2024-03-09 20:17:49\n", "4     2017-01-03 13:45:00  DLeb9001  0.000000  2024-03-09 20:17:49\n", "...                   ...       ...       ...                  ...\n", "51009 2023-07-31 13:36:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "51010 2023-07-31 14:31:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "51011 2023-07-31 14:31:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "51012 2023-07-31 14:36:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "51013 2023-07-31 14:36:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "\n", "[51014 rows x 4 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取 redis 中的指定策略的持仓权重（指定品种全部历史）\n", "dfw = czsc.get_strategy_weights(strategy_name='TestStrategy', symbols=['DLeb9001', 'ZZAP9001'])\n", "dfw"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:21:00.916\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m67\u001b[0m - \u001b[1mTestStrategy Weights: 使用环境变量 RWC_REDIS_URL 创建 redis 连接池\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>symbol</th>\n", "      <th>weight</th>\n", "      <th>update_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-03 09:06:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-03 09:11:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-03 09:15:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-01-03 09:21:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-01-03 09:26:00</td>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4705</th>\n", "      <td>2023-07-31 11:01:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4707</th>\n", "      <td>2023-07-31 11:26:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4709</th>\n", "      <td>2023-07-31 13:36:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4711</th>\n", "      <td>2023-07-31 14:31:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4713</th>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4714 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                      dt    symbol    weight          update_time\n", "0    2023-01-03 09:06:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "2    2023-01-03 09:11:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "4    2023-01-03 09:15:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "6    2023-01-03 09:21:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "8    2023-01-03 09:26:00  DLeb9001  0.954545  2024-03-09 20:17:49\n", "...                  ...       ...       ...                  ...\n", "4705 2023-07-31 11:01:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "4707 2023-07-31 11:26:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "4709 2023-07-31 13:36:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "4711 2023-07-31 14:31:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "4713 2023-07-31 14:36:00  ZZAP9001  0.954545  2024-03-09 20:17:49\n", "\n", "[4714 rows x 4 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取 redis 中的指定策略的持仓权重（指定时间段）\n", "dfw = czsc.get_strategy_weights(strategy_name='TestStrategy', symbols=['DLeb9001', 'ZZAP9001'], sdt='20230101', edt='20240131')\n", "dfw = dfw.sort_values(['symbol', 'dt'])\n", "dfw"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:21:17.413\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m67\u001b[0m - \u001b[1mTestStrategy Weights: 使用环境变量 RWC_REDIS_URL 创建 redis 连接池\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>weight</th>\n", "      <th>dt</th>\n", "      <th>update_time</th>\n", "      <th>price</th>\n", "      <th>ref</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZZAP9001</td>\n", "      <td>0.954545</td>\n", "      <td>2023-07-27 09:11:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>9701.066217704249</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ZZUR9001</td>\n", "      <td>0.400000</td>\n", "      <td>2023-07-28 10:36:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>3535.4438227861174</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DLeb9001</td>\n", "      <td>0.954545</td>\n", "      <td>2023-07-28 14:16:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>10144.286683491264</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SEsc9001</td>\n", "      <td>1.000000</td>\n", "      <td>2023-07-29 02:16:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>415.3633199230844</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ZZSA9001</td>\n", "      <td>0.277778</td>\n", "      <td>2023-07-31 09:06:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>1731.4345038436766</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>DLj9001</td>\n", "      <td>-0.636364</td>\n", "      <td>2023-07-31 11:01:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>3949.359200482296</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>DLeg9001</td>\n", "      <td>0.894737</td>\n", "      <td>2023-07-31 14:31:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>3586.740974496101</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ZZSF9001</td>\n", "      <td>0.545455</td>\n", "      <td>2023-07-31 14:31:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>10657.215688529335</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SQag9001</td>\n", "      <td>-0.705882</td>\n", "      <td>2023-07-31 14:36:00</td>\n", "      <td>2024-03-09 20:17:49</td>\n", "      <td>3950.9596447677372</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     symbol    weight                  dt          update_time               price ref\n", "0  ZZAP9001  0.954545 2023-07-27 09:11:00  2024-03-09 20:17:49   9701.066217704249  {}\n", "1  ZZUR9001  0.400000 2023-07-28 10:36:00  2024-03-09 20:17:49  3535.4438227861174  {}\n", "2  DLeb9001  0.954545 2023-07-28 14:16:00  2024-03-09 20:17:49  10144.286683491264  {}\n", "3  SEsc9001  1.000000 2023-07-29 02:16:00  2024-03-09 20:17:49   415.3633199230844  {}\n", "4  ZZSA9001  0.277778 2023-07-31 09:06:00  2024-03-09 20:17:49  1731.4345038436766  {}\n", "5   DLj9001 -0.636364 2023-07-31 11:01:00  2024-03-09 20:17:49   3949.359200482296  {}\n", "6  DLeg9001  0.894737 2023-07-31 14:31:00  2024-03-09 20:17:49   3586.740974496101  {}\n", "7  ZZSF9001  0.545455 2023-07-31 14:31:00  2024-03-09 20:17:49  10657.215688529335  {}\n", "8  SQag9001 -0.705882 2023-07-31 14:36:00  2024-03-09 20:17:49  3950.9596447677372  {}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取 redis 中的指定策略的最后一个时刻的持仓权重\n", "dfw = czsc.get_strategy_weights(strategy_name='TestStrategy', only_last=True)\n", "dfw"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 三、删除 redis 中的策略"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-03-09 20:24:03.874\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m62\u001b[0m - \u001b[1mTestStrategy Weights: 使用传入的 redis 连接池\u001b[0m\n", "\u001b[32m2024-03-09 20:24:11.224\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.traders.rwc\u001b[0m:\u001b[36mclear_all\u001b[0m:\u001b[36m262\u001b[0m - \u001b[1mTestStrategy 删除了 29926 条记录\u001b[0m\n"]}], "source": ["import os\n", "import czsc\n", "\n", "# 获取 RWC_REDIS_URL 环境变量，用于连接 Redis\n", "# redis_url = os.getenv(\"RWC_REDIS_URL\")\n", "# czsc.clear_strategy(strategy_name=\"TestStrategy\", redis_url=redis_url, key_prefix=\"Weights\")\n", "\n", "# redis_url 参数可以省略，如果省略则使用环境变量 RWC_REDIS_URL\n", "czsc.clear_strategy(strategy_name=\"TestStrategy\", key_prefix=\"Weights\", connection_pool=connection_pool)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PY311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 2}
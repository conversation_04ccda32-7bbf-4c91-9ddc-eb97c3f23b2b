"""
高级回测系统模块
包含数据预处理、特征工程、时间序列处理、网络架构、模型训练、回测引擎和风险管理模块
"""

from .data_preprocessor import DataPreprocessor
from .feature_engineer import FeatureEngineer
from .timeseries_processor import TimeSeriesProcessor
try:
    # 优先使用PyTorch版本
    from .torch_network_architecture import TorchNetworkArchitecture as NetworkArchitecture
    DATA_PROCESSING_AVAILABLE = True
except ImportError:
    try:
        # 回退到TensorFlow版本
        from .network_architecture import NetworkArchitecture
        DATA_PROCESSING_AVAILABLE = True
    except ImportError:
        # 如果都不可用，创建一个占位符
        class NetworkArchitecture:
            def __init__(self, *args, **kwargs):
                raise ImportError("Neither PyTorch nor TensorFlow is available")
        DATA_PROCESSING_AVAILABLE = False
try:
    # 优先使用PyTorch版本
    from .torch_model_trainer import TorchModelTrainer as ModelTrainer
except ImportError:
    try:
        # 回退到TensorFlow版本
        from .model_trainer import ModelTrainer
    except ImportError:
        # 如果都不可用，创建一个占位符
        class ModelTrainer:
            def __init__(self, *args, **kwargs):
                raise ImportError("Neither PyTorch nor TensorFlow is available for ModelTrainer")
from .backtest_engine import BacktestEngine
from .risk_manager import RiskManager
from .advanced_backtest_system import AdvancedBacktestSystem

__version__ = "1.0.0"
__author__ = "Advanced Backtest System"

__all__ = [
    'DataPreprocessor',
    'FeatureEngineer', 
    'TimeSeriesProcessor',
    'NetworkArchitecture',
    'ModelTrainer',
    'BacktestEngine',
    'RiskManager',
    'AdvancedBacktestSystem'
] 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取器健壮性测试
专门测试数据获取和处理过程中的问题
"""

import sys
import os
import pandas as pd
from datetime import datetime
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def analyze_raw_data_structure():
    """分析原始数据结构"""
    print("🔍 分析原始数据结构...")
    
    try:
        # 直接使用QUANTAXIS获取原始数据
        from QUANTAXIS.QAFetch.QATdx import QA_fetch_get_stock_min
        
        symbol = '518880'
        start_date = '2025-07-01'
        end_date = '2025-07-18'
        
        print(f"📊 直接获取{symbol}的30分钟原始数据...")
        raw_df = QA_fetch_get_stock_min(symbol, start_date, end_date, frequence='30min')
        
        if raw_df is None or raw_df.empty:
            print("❌ 原始数据获取失败")
            return None
        
        print(f"✅ 原始数据获取成功: {len(raw_df)}条")
        print(f"📋 原始数据列: {list(raw_df.columns)}")
        print(f"📋 原始数据索引: {raw_df.index.names}")
        
        # 显示前几条数据
        print("\n📊 前5条原始数据:")
        print(raw_df.head())
        
        # 检查时间列
        if 'datetime' in raw_df.columns:
            print(f"\n📅 datetime列类型: {raw_df['datetime'].dtype}")
            print(f"📅 datetime范围: {raw_df['datetime'].min()} 至 {raw_df['datetime'].max()}")
            
            # 检查重复时间
            duplicated_datetime = raw_df['datetime'].duplicated().sum()
            print(f"📅 datetime重复数量: {duplicated_datetime}")
            
            if duplicated_datetime > 0:
                print("⚠️ 发现重复datetime，显示前10个重复项:")
                duplicated_data = raw_df[raw_df['datetime'].duplicated(keep=False)].head(10)
                print(duplicated_data[['datetime', 'open', 'close', 'vol']])
        
        # 检查索引
        if hasattr(raw_df.index, 'names') and raw_df.index.names:
            print(f"\n📋 索引信息: {raw_df.index.names}")
            if len(raw_df.index.names) > 1:
                print("📋 多级索引结构:")
                for i, name in enumerate(raw_df.index.names):
                    if name:
                        unique_values = raw_df.index.get_level_values(i).unique()
                        print(f"   {name}: {len(unique_values)}个唯一值")
                        if len(unique_values) <= 10:
                            print(f"      值: {list(unique_values)}")
        
        return raw_df
        
    except Exception as e:
        print(f"❌ 原始数据分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_data_processing_steps():
    """测试数据处理各个步骤"""
    print("\n🔍 测试数据处理步骤...")
    
    try:
        # 获取原始数据
        raw_df = analyze_raw_data_structure()
        if raw_df is None:
            return False
        
        print(f"\n🔧 开始数据处理步骤...")
        
        # 步骤1: 重置索引
        print("步骤1: 重置索引")
        if 'date' in raw_df.columns:
            df = raw_df.reset_index(drop=True)
        else:
            df = raw_df.reset_index()
        print(f"   处理后: {len(df)}条数据，列: {list(df.columns)}")
        
        # 步骤2: 统一列名
        print("步骤2: 统一列名")
        original_columns = list(df.columns)
        if 'date' in df.columns:
            df.rename(columns={'date': 'dt'}, inplace=True)
        elif 'datetime' in df.columns:
            df.rename(columns={'datetime': 'dt'}, inplace=True)
        elif df.index.name == 'date':
            df['dt'] = df.index
            df = df.reset_index(drop=True)
        print(f"   列名变化: {original_columns} -> {list(df.columns)}")
        
        # 步骤3: 处理成交量列
        print("步骤3: 处理成交量列")
        if 'vol' not in df.columns and 'volume' in df.columns:
            df.rename(columns={'volume': 'vol'}, inplace=True)
        elif 'vol' not in df.columns:
            df['vol'] = 100000
        print(f"   成交量列: {'vol' in df.columns}")
        
        # 步骤4: 处理日期格式
        print("步骤4: 处理日期格式")
        if 'dt' in df.columns:
            print(f"   dt列类型(处理前): {df['dt'].dtype}")
            df['dt'] = pd.to_datetime(df['dt'])
            print(f"   dt列类型(处理后): {df['dt'].dtype}")
            print(f"   dt范围: {df['dt'].min()} 至 {df['dt'].max()}")
        
        # 步骤5: 检查时间排序
        print("步骤5: 检查时间排序")
        is_sorted_before = df['dt'].is_monotonic_increasing
        print(f"   排序前是否单调递增: {is_sorted_before}")
        
        df = df.sort_values('dt', ascending=True).reset_index(drop=True)
        is_sorted_after = df['dt'].is_monotonic_increasing
        print(f"   排序后是否单调递增: {is_sorted_after}")
        
        # 步骤6: 添加必要列
        print("步骤6: 添加必要列")
        symbol = '518880'
        required_cols = ['dt', 'open', 'high', 'low', 'close', 'vol', 'symbol']
        for col in required_cols:
            if col not in df.columns:
                if col == 'vol':
                    df[col] = 100000
                elif col == 'symbol':
                    df[col] = symbol
        print(f"   最终列: {list(df.columns)}")
        
        # 步骤7: 数据清洗
        print("步骤7: 数据清洗")
        len_before_clean = len(df)
        df = df.dropna(subset=['open', 'high', 'low', 'close'])
        df = df[df['open'] > 0]
        len_after_clean = len(df)
        print(f"   清洗前: {len_before_clean}条，清洗后: {len_after_clean}条")
        
        # 步骤8: 去重分析（关键步骤）
        print("步骤8: 去重分析")
        len_before_dedup = len(df)
        
        # 详细分析重复情况
        duplicated_mask = df['dt'].duplicated(keep=False)
        duplicated_count = duplicated_mask.sum()
        print(f"   重复时间戳总数: {duplicated_count}")
        
        if duplicated_count > 0:
            print("   重复时间戳详情:")
            duplicated_data = df[duplicated_mask].sort_values('dt')
            
            # 按时间戳分组显示重复情况
            for dt, group in duplicated_data.groupby('dt'):
                if len(group) > 1:
                    print(f"     时间 {dt}: {len(group)}条重复记录")
                    print(f"       价格范围: {group['close'].min():.3f} - {group['close'].max():.3f}")
                    print(f"       成交量范围: {group['vol'].min():.0f} - {group['vol'].max():.0f}")
                    
                    # 只显示前3个重复时间戳的详情
                    if list(duplicated_data.groupby('dt')).index((dt, group)) < 3:
                        print("       详细数据:")
                        print(group[['dt', 'open', 'high', 'low', 'close', 'vol']].to_string(index=False))
                    
                    if list(duplicated_data.groupby('dt')).index((dt, group)) >= 10:
                        print("       ... (更多重复数据)")
                        break
        
        # 执行去重
        df_dedup = df.drop_duplicates(subset=['dt'], keep='first')
        len_after_dedup = len(df_dedup)
        
        print(f"   去重前: {len_before_dedup}条")
        print(f"   去重后: {len_after_dedup}条")
        print(f"   去重比例: {(len_before_dedup - len_after_dedup) / len_before_dedup * 100:.1f}%")
        
        # 分析去重是否合理
        dedup_ratio = (len_before_dedup - len_after_dedup) / len_before_dedup
        if dedup_ratio > 0.8:  # 如果去重超过80%，可能有问题
            print("   ⚠️ 警告: 去重比例过高，可能存在数据问题")
            
            # 分析时间分布
            print("   📊 分析时间分布:")
            df_dedup['date_only'] = df_dedup['dt'].dt.date
            date_counts = df_dedup['date_only'].value_counts().sort_index()
            print(f"   数据覆盖天数: {len(date_counts)}")
            print(f"   每天数据量统计:")
            print(f"     平均: {date_counts.mean():.1f}条/天")
            print(f"     最少: {date_counts.min()}条/天")
            print(f"     最多: {date_counts.max()}条/天")
            
            # 显示前几天的数据量
            print("   前10天数据量:")
            for date, count in date_counts.head(10).items():
                print(f"     {date}: {count}条")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_data_fetcher():
    """测试增强版数据获取器"""
    print("\n🔍 测试增强版数据获取器...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        
        # 测试30分钟数据
        symbol = '518880'
        print(f"📊 使用增强版获取器获取{symbol}的30分钟数据...")
        
        df = get_kline(symbol, freq='30min')
        
        if df is None or df.empty:
            print("❌ 增强版获取器失败")
            return False
        
        print(f"✅ 增强版获取器成功: {len(df)}条数据")
        print(f"📋 数据列: {list(df.columns)}")
        print(f"📅 时间范围: {df['dt'].min()} 至 {df['dt'].max()}")
        
        # 检查数据质量
        print("\n📊 数据质量检查:")
        print(f"   时间排序: {'✅' if df['dt'].is_monotonic_increasing else '❌'}")
        print(f"   重复时间: {df['dt'].duplicated().sum()}条")
        print(f"   空值检查: {df.isnull().sum().sum()}个")
        print(f"   symbol字段: {'✅' if 'symbol' in df.columns else '❌'}")
        
        # 显示数据样本
        print("\n📊 数据样本:")
        print(df.head(3).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 增强版数据获取器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 数据获取器健壮性测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    tests = [
        ("原始数据结构分析", analyze_raw_data_structure),
        ("数据处理步骤测试", test_data_processing_steps),
        ("增强版数据获取器测试", test_enhanced_data_fetcher),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "原始数据结构分析":
                # 这个测试返回数据，不是布尔值
                result = test_func() is not None
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 健壮性测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 保存测试报告
    test_report = {
        'test_date': datetime.now().isoformat(),
        'test_type': 'data_fetcher_robustness',
        'total_tests': total,
        'passed_tests': passed,
        'success_rate': passed / total,
        'test_results': [
            {'test_name': name, 'result': result} 
            for name, result in results
        ],
        'status': 'SUCCESS' if passed == total else 'PARTIAL_SUCCESS'
    }
    
    with open('data_fetcher_robustness_report.json', 'w', encoding='utf-8') as f:
        json.dump(test_report, f, indent=2, ensure_ascii=False)
    
    print(f"💾 测试报告已保存: data_fetcher_robustness_report.json")
    
    if passed == total:
        print("🎉 所有健壮性测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
# -*- coding: utf-8 -*-
"""
author: czsc contributor
create_dt: 2023/10/10 12:00
describe: 测试通达信数据连接器
"""

import os
import pytest
import pandas as pd
from datetime import datetime, timedelta
from czsc.objects import RawBar
from czsc.enum import Freq
from czsc.connectors.tdx_connector import get_raw_bars

# 跳过测试的装饰器，如果没有安装QUANTAXIS库或者没有配置通达信服务器
pytestmark = pytest.mark.skipif(
    "QUANTAXIS" not in locals() and "QUANTAXIS" not in globals(),
    reason="QUANTAXIS库未安装，跳过测试"
)


def test_get_raw_bars_daily():
    """测试获取日线数据"""
    # 使用一个常见的股票代码进行测试
    symbol = "000001"  # 平安银行
    # 设置一个较短的时间范围，避免获取过多数据
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    # 测试日线数据获取
    bars = get_raw_bars(symbol=symbol, freq="日线", sdt=start_date, edt=end_date, fq="前复权")
    
    # 验证返回的是RawBar对象列表
    assert isinstance(bars, list), "返回结果应该是一个列表"
    if bars:  # 如果返回了数据
        assert isinstance(bars[0], RawBar), "列表中的元素应该是RawBar对象"
        assert bars[0].symbol == symbol, "RawBar的symbol属性应该与输入的symbol一致"
        assert bars[0].freq == Freq.D, "RawBar的freq属性应该是日线"
        
        # 验证日期范围
        assert bars[0].dt >= pd.to_datetime(start_date), "K线开始日期应该大于等于请求的开始日期"
        assert bars[-1].dt <= pd.to_datetime(end_date), "K线结束日期应该小于等于请求的结束日期"
        
        # 验证K线数据的完整性
        for bar in bars:
            assert hasattr(bar, "open"), "RawBar对象应该有open属性"
            assert hasattr(bar, "high"), "RawBar对象应该有high属性"
            assert hasattr(bar, "low"), "RawBar对象应该有low属性"
            assert hasattr(bar, "close"), "RawBar对象应该有close属性"
            assert hasattr(bar, "vol"), "RawBar对象应该有vol属性"
            assert hasattr(bar, "amount"), "RawBar对象应该有amount属性"


def test_get_raw_bars_minute():
    """测试获取分钟线数据"""
    # 使用一个常见的股票代码进行测试
    symbol = "000001"  # 平安银行
    # 设置一个较短的时间范围，避免获取过多数据
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d")
    
    # 测试5分钟线数据获取
    bars = get_raw_bars(symbol=symbol, freq="5分钟", sdt=start_date, edt=end_date, fq="前复权")
    
    # 验证返回的是RawBar对象列表
    assert isinstance(bars, list), "返回结果应该是一个列表"
    if bars:  # 如果返回了数据
        assert isinstance(bars[0], RawBar), "列表中的元素应该是RawBar对象"
        assert bars[0].symbol == symbol, "RawBar的symbol属性应该与输入的symbol一致"
        assert bars[0].freq == Freq.F5, "RawBar的freq属性应该是5分钟线"
        
        # 验证日期范围
        assert bars[0].dt >= pd.to_datetime(start_date), "K线开始日期应该大于等于请求的开始日期"
        assert bars[-1].dt <= pd.to_datetime(end_date), "K线结束日期应该小于等于请求的结束日期"
        
        # 验证K线数据的完整性
        for bar in bars:
            assert hasattr(bar, "open"), "RawBar对象应该有open属性"
            assert hasattr(bar, "high"), "RawBar对象应该有high属性"
            assert hasattr(bar, "low"), "RawBar对象应该有low属性"
            assert hasattr(bar, "close"), "RawBar对象应该有close属性"
            assert hasattr(bar, "vol"), "RawBar对象应该有vol属性"
            assert hasattr(bar, "amount"), "RawBar对象应该有amount属性"


def test_get_raw_bars_different_fq():
    """测试不同复权方式的数据获取"""
    # 使用一个常见的股票代码进行测试
    symbol = "000001"  # 平安银行
    # 设置一个较短的时间范围，避免获取过多数据
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    # 测试不同复权方式
    fq_types = ["前复权", "后复权", "不复权"]
    for fq in fq_types:
        bars = get_raw_bars(symbol=symbol, freq="日线", sdt=start_date, edt=end_date, fq=fq)
        
        # 验证返回的是RawBar对象列表
        assert isinstance(bars, list), f"{fq}模式下返回结果应该是一个列表"
        if bars:  # 如果返回了数据
            assert isinstance(bars[0], RawBar), f"{fq}模式下列表中的元素应该是RawBar对象"
            assert bars[0].symbol == symbol, f"{fq}模式下RawBar的symbol属性应该与输入的symbol一致"


def test_get_raw_bars_invalid_params():
    """测试无效参数的处理"""
    # 测试不支持的周期
    with pytest.raises(ValueError):
        get_raw_bars(symbol="000001", freq="3分钟", sdt="2023-01-01", edt="2023-01-10")
    
    # 测试无效的日期范围
    bars = get_raw_bars(symbol="000001", freq="日线", sdt="2099-01-01", edt="2099-01-10")
    assert len(bars) == 0, "对于未来的日期范围，应该返回空列表"
    
    # 测试无效的股票代码
    bars = get_raw_bars(symbol="INVALID_CODE", freq="日线", sdt="2023-01-01", edt="2023-01-10")
    assert len(bars) == 0, "对于无效的股票代码，应该返回空列表"


if __name__ == "__main__":
    # 手动运行测试
    test_get_raw_bars_daily()
    test_get_raw_bars_minute()
    test_get_raw_bars_different_fq()
    test_get_raw_bars_invalid_params()
    print("所有测试通过！")
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
import os
import json
import joblib
import warnings
from datetime import datetime
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler

warnings.filterwarnings('ignore')

# 尝试导入深度学习框架
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 移除tensorflow依赖，只使用PyTorch和sklearn
TF_AVAILABLE = False


class MLModelManager:
    """
    机器学习模型管理器
    负责模型训练、验证、选择和持久化
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化ML模型管理器
        
        Args:
            config: 模型配置
        """
        self.config = config or self._get_default_config()
        self.models = {}
        self.scalers = {}
        self.model_performance = {}
        self.training_history = {}
        self.best_model = None
        self.model_log = []
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'models': {
                'random_forest': {
                    'enabled': True,
                    'params': {
                        'n_estimators': [50, 100, 200],
                        'max_depth': [5, 10, 15, None],
                        'min_samples_split': [2, 5, 10],
                        'min_samples_leaf': [1, 2, 4]
                    }
                },
                'gradient_boosting': {
                    'enabled': True,
                    'params': {
                        'n_estimators': [50, 100, 200],
                        'learning_rate': [0.01, 0.1, 0.2],
                        'max_depth': [3, 5, 7]
                    }
                },
                'linear_models': {
                    'enabled': True,
                    'models': ['linear', 'ridge', 'lasso']
                },
                'svm': {
                    'enabled': False,
                    'params': {
                        'C': [0.1, 1, 10],
                        'gamma': ['scale', 'auto'],
                        'kernel': ['rbf', 'linear']
                    }
                }
            },
            'deep_learning': {
                'enabled': True,
                'lstm': {
                    'hidden_size': [32, 64, 128],
                    'num_layers': [1, 2, 3],
                    'dropout': [0.1, 0.2, 0.3],
                    'learning_rate': [0.001, 0.01],
                    'epochs': 100,
                    'batch_size': 32
                }
            },
            'validation': {
                'method': 'time_series_split',
                'n_splits': 5,
                'test_size': 0.2
            },
            'feature_selection': {
                'enabled': True,
                'method': 'correlation',
                'n_features': 20
            }
        }
    
    def train_all_models(self, X: np.ndarray, y: np.ndarray, 
                        feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        训练所有配置的模型
        
        Args:
            X: 特征数据
            y: 目标数据
            feature_names: 特征名称列表
            
        Returns:
            训练结果字典
        """
        self.model_log.append(f"Starting model training with {X.shape[0]} samples, {X.shape[1]} features")
        
        results = {}
        
        # 数据预处理
        X_scaled, scaler = self._preprocess_data(X)
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=self.config['validation']['n_splits'])
        
        # 训练传统机器学习模型
        if self.config['models']:
            ml_results = self._train_ml_models(X_scaled, y, tscv)
            results.update(ml_results)
        
        # 训练深度学习模型
        if self.config['deep_learning']['enabled'] and (TORCH_AVAILABLE or TF_AVAILABLE):
            dl_results = self._train_deep_learning_models(X_scaled, y, tscv)
            results.update(dl_results)
        
        # 选择最佳模型
        self.best_model = self._select_best_model(results)
        
        # 保存缩放器
        self.scalers['main'] = scaler
        
        self.model_log.append(f"Training completed. Best model: {self.best_model}")
        
        return results
    
    def _preprocess_data(self, X: np.ndarray) -> Tuple[np.ndarray, StandardScaler]:
        """数据预处理"""
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        return X_scaled, scaler
    
    def _train_ml_models(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练传统机器学习模型"""
        results = {}
        
        # Random Forest
        if self.config['models']['random_forest']['enabled']:
            rf_result = self._train_random_forest(X, y, cv)
            results['random_forest'] = rf_result
        
        # Gradient Boosting
        if self.config['models']['gradient_boosting']['enabled']:
            gb_result = self._train_gradient_boosting(X, y, cv)
            results['gradient_boosting'] = gb_result
        
        # Linear Models
        if self.config['models']['linear_models']['enabled']:
            linear_results = self._train_linear_models(X, y, cv)
            results.update(linear_results)
        
        # SVM
        if self.config['models']['svm']['enabled']:
            svm_result = self._train_svm(X, y, cv)
            results['svm'] = svm_result
        
        return results
    
    def _train_random_forest(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练随机森林模型"""
        try:
            params = self.config['models']['random_forest']['params']
            
            rf = RandomForestRegressor(random_state=42)
            grid_search = GridSearchCV(rf, params, cv=cv, scoring='neg_mean_squared_error', n_jobs=-1)
            grid_search.fit(X, y)
            
            best_model = grid_search.best_estimator_
            self.models['random_forest'] = best_model
            
            # 计算性能指标
            y_pred = best_model.predict(X)
            mse = mean_squared_error(y, y_pred)
            mae = mean_absolute_error(y, y_pred)
            r2 = r2_score(y, y_pred)
            
            result = {
                'model': best_model,
                'best_params': grid_search.best_params_,
                'cv_score': -grid_search.best_score_,
                'mse': mse,
                'mae': mae,
                'r2': r2,
                'feature_importance': best_model.feature_importances_ if hasattr(best_model, 'feature_importances_') else None
            }
            
            self.model_performance['random_forest'] = result
            return result
            
        except Exception as e:
            self.model_log.append(f"Random Forest training failed: {e}")
            return {'error': str(e)}
    
    def _train_gradient_boosting(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练梯度提升模型"""
        try:
            params = self.config['models']['gradient_boosting']['params']
            
            gb = GradientBoostingRegressor(random_state=42)
            grid_search = GridSearchCV(gb, params, cv=cv, scoring='neg_mean_squared_error', n_jobs=-1)
            grid_search.fit(X, y)
            
            best_model = grid_search.best_estimator_
            self.models['gradient_boosting'] = best_model
            
            # 计算性能指标
            y_pred = best_model.predict(X)
            mse = mean_squared_error(y, y_pred)
            mae = mean_absolute_error(y, y_pred)
            r2 = r2_score(y, y_pred)
            
            result = {
                'model': best_model,
                'best_params': grid_search.best_params_,
                'cv_score': -grid_search.best_score_,
                'mse': mse,
                'mae': mae,
                'r2': r2,
                'feature_importance': best_model.feature_importances_ if hasattr(best_model, 'feature_importances_') else None
            }
            
            self.model_performance['gradient_boosting'] = result
            return result
            
        except Exception as e:
            self.model_log.append(f"Gradient Boosting training failed: {e}")
            return {'error': str(e)}
    
    def _train_linear_models(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练线性模型"""
        results = {}
        models_config = self.config['models']['linear_models']['models']
        
        for model_name in models_config:
            try:
                if model_name == 'linear':
                    model = LinearRegression()
                elif model_name == 'ridge':
                    model = Ridge(alpha=1.0)
                elif model_name == 'lasso':
                    model = Lasso(alpha=1.0)
                else:
                    continue
                
                model.fit(X, y)
                self.models[model_name] = model
                
                # 计算性能指标
                y_pred = model.predict(X)
                mse = mean_squared_error(y, y_pred)
                mae = mean_absolute_error(y, y_pred)
                r2 = r2_score(y, y_pred)
                
                result = {
                    'model': model,
                    'mse': mse,
                    'mae': mae,
                    'r2': r2
                }
                
                results[model_name] = result
                self.model_performance[model_name] = result
                
            except Exception as e:
                self.model_log.append(f"{model_name} training failed: {e}")
                results[model_name] = {'error': str(e)}
        
        return results

    def _train_svm(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练SVM模型"""
        try:
            params = self.config['models']['svm']['params']

            svm = SVR()
            grid_search = GridSearchCV(svm, params, cv=cv, scoring='neg_mean_squared_error', n_jobs=-1)
            grid_search.fit(X, y)

            best_model = grid_search.best_estimator_
            self.models['svm'] = best_model

            # 计算性能指标
            y_pred = best_model.predict(X)
            mse = mean_squared_error(y, y_pred)
            mae = mean_absolute_error(y, y_pred)
            r2 = r2_score(y, y_pred)

            result = {
                'model': best_model,
                'best_params': grid_search.best_params_,
                'cv_score': -grid_search.best_score_,
                'mse': mse,
                'mae': mae,
                'r2': r2
            }

            self.model_performance['svm'] = result
            return result

        except Exception as e:
            self.model_log.append(f"SVM training failed: {e}")
            return {'error': str(e)}

    def _train_deep_learning_models(self, X: np.ndarray, y: np.ndarray, cv) -> Dict[str, Any]:
        """训练深度学习模型"""
        results = {}

        if TORCH_AVAILABLE:
            lstm_result = self._train_pytorch_lstm(X, y)
            results['pytorch_lstm'] = lstm_result
        else:
            # 如果PyTorch不可用，使用sklearn作为替代
            from sklearn.ensemble import RandomForestRegressor
            rf_result = self._train_random_forest(X, y, cv)
            results['fallback_random_forest'] = rf_result

        return results

    def _train_pytorch_lstm(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练PyTorch LSTM模型"""
        try:
            # 简化的LSTM实现
            class SimpleLSTM(nn.Module):
                def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
                    super(SimpleLSTM, self).__init__()
                    self.hidden_size = hidden_size
                    self.num_layers = num_layers
                    self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                                       batch_first=True, dropout=dropout)
                    self.fc = nn.Linear(hidden_size, output_size)

                def forward(self, x):
                    h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
                    c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
                    out, _ = self.lstm(x, (h0, c0))
                    out = self.fc(out[:, -1, :])
                    return out

            # 重塑数据为序列格式
            sequence_length = 10
            if X.shape[0] < sequence_length:
                return {'error': 'Insufficient data for sequence modeling'}

            X_seq = []
            y_seq = []
            for i in range(sequence_length, len(X)):
                X_seq.append(X[i-sequence_length:i])
                y_seq.append(y[i])

            X_seq = np.array(X_seq)
            y_seq = np.array(y_seq)

            # 转换为PyTorch张量
            X_tensor = torch.FloatTensor(X_seq)
            y_tensor = torch.FloatTensor(y_seq).view(-1, 1)

            # 创建模型
            model = SimpleLSTM(X.shape[1], 64, 2, 1, 0.2)
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001)

            # 训练
            model.train()
            for epoch in range(50):  # 简化训练
                optimizer.zero_grad()
                outputs = model(X_tensor)
                loss = criterion(outputs, y_tensor)
                loss.backward()
                optimizer.step()

            # 评估
            model.eval()
            with torch.no_grad():
                predictions = model(X_tensor).numpy().flatten()
                mse = mean_squared_error(y_seq, predictions)
                mae = mean_absolute_error(y_seq, predictions)
                r2 = r2_score(y_seq, predictions)

            self.models['pytorch_lstm'] = model

            result = {
                'model': model,
                'mse': mse,
                'mae': mae,
                'r2': r2,
                'sequence_length': sequence_length
            }

            self.model_performance['pytorch_lstm'] = result
            return result

        except Exception as e:
            self.model_log.append(f"PyTorch LSTM training failed: {e}")
            return {'error': str(e)}

    # TensorFlow LSTM方法已移除，使用PyTorch LSTM替代

    def _select_best_model(self, results: Dict[str, Any]) -> Optional[str]:
        """选择最佳模型"""
        best_model = None
        best_score = float('inf')

        for model_name, result in results.items():
            if 'error' not in result and 'mse' in result:
                if result['mse'] < best_score:
                    best_score = result['mse']
                    best_model = model_name

        return best_model

    def predict(self, X: np.ndarray, model_name: Optional[str] = None) -> np.ndarray:
        """
        使用指定模型进行预测

        Args:
            X: 输入特征
            model_name: 模型名称，如果为None则使用最佳模型

        Returns:
            预测结果
        """
        if model_name is None:
            model_name = self.best_model

        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")

        model = self.models[model_name]
        scaler = self.scalers.get('main')

        # 数据预处理
        if scaler:
            X_scaled = scaler.transform(X)
        else:
            X_scaled = X

        # 预测
        if 'lstm' in model_name and hasattr(model, 'predict'):
            # 深度学习模型需要序列格式
            sequence_length = self.model_performance[model_name].get('sequence_length', 10)
            if X_scaled.shape[0] >= sequence_length:
                X_seq = []
                for i in range(sequence_length, len(X_scaled) + 1):
                    X_seq.append(X_scaled[i-sequence_length:i])
                X_seq = np.array(X_seq)
                predictions = model.predict(X_seq, verbose=0).flatten()
            else:
                predictions = np.zeros(X_scaled.shape[0])
        else:
            # 传统机器学习模型
            predictions = model.predict(X_scaled)

        return predictions

    def save_models(self, save_dir: str):
        """保存所有模型"""
        os.makedirs(save_dir, exist_ok=True)

        for model_name, model in self.models.items():
            model_path = os.path.join(save_dir, f"{model_name}.pkl")

            if 'tensorflow' in model_name and hasattr(model, 'save'):
                model.save(model_path.replace('.pkl', '.h5'))
            elif 'pytorch' in model_name and hasattr(model, 'state_dict'):
                torch.save(model.state_dict(), model_path.replace('.pkl', '.pth'))
            else:
                joblib.dump(model, model_path)

        # 保存缩放器
        if self.scalers:
            scaler_path = os.path.join(save_dir, "scalers.pkl")
            joblib.dump(self.scalers, scaler_path)

        # 保存性能指标
        performance_path = os.path.join(save_dir, "performance.json")
        with open(performance_path, 'w') as f:
            # 转换numpy数组为列表以便JSON序列化
            performance_json = {}
            for k, v in self.model_performance.items():
                performance_json[k] = {}
                for key, value in v.items():
                    if isinstance(value, np.ndarray):
                        performance_json[k][key] = value.tolist()
                    elif hasattr(value, 'tolist'):
                        performance_json[k][key] = value.tolist()
                    else:
                        performance_json[k][key] = value
            json.dump(performance_json, f, indent=2)

    def get_model_summary(self) -> Dict[str, Any]:
        """获取模型训练总结"""
        return {
            'models_trained': list(self.models.keys()),
            'best_model': self.best_model,
            'model_performance': self.model_performance,
            'training_log': self.model_log
        }

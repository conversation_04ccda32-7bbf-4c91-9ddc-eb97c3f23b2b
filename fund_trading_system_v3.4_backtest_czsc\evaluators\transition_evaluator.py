"""
转换维度评估器
负责评估市场转换的可能性和阶段
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import TransitionState


class TransitionEvaluator:
    """
    @class TransitionEvaluator
    @brief 转换维度评估器
    @details 负责评估市场转换的可能性和阶段
    """
    
    def __init__(self):
        self.name = "TransitionEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估转换维度
        @param data: 市场数据
        @return: 转换评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            timeframe_divergence = data.get('timeframe_divergence', {})
            
            # 获取转换相关数据
            transition_probability = timeframe_divergence.get('transition_probability', 0.0)
            consensus_direction = timeframe_divergence.get('consensus_direction', 'neutral')
            transition_signal = timeframe_divergence.get('transition_signal', 'none')
            resonance_score = timeframe_divergence.get('resonance_score', 0.0)
            
            # 转换评分计算
            transition_signals = []
            transition_score = 0.0
            
            # 1. 基础转换概率
            if transition_probability >= 0.8:
                base_transition = "高概率转换"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.5
                transition_signals.append(f"高概率转换({transition_probability:.2f})")
            elif transition_probability >= 0.6:
                base_transition = "中等转换概率"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.4
                transition_signals.append(f"中等转换概率({transition_probability:.2f})")
            elif transition_probability >= 0.3:
                base_transition = "低转换概率"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.3
                transition_signals.append(f"低转换概率({transition_probability:.2f})")
            else:
                base_transition = "无明显转换"
                transition_score += 0.1
                transition_signals.append(f"无明显转换({transition_probability:.2f})")
            
            # 2. 转换信号强度
            signal_strength_map = {
                'strong': 0.9, 'medium': 0.6, 'weak': 0.3, 'none': 0.0
            }
            signal_strength = signal_strength_map.get(transition_signal, 0.0)
            transition_score += signal_strength * 0.3
            transition_signals.append(f"信号强度{transition_signal}({signal_strength:.1f})")
            
            # 3. 方向一致性
            if consensus_direction == 'bullish':
                direction_factor = "看涨一致"
                consensus_strength = 0.8
                transition_score += consensus_strength * 0.2
                transition_signals.append("看涨方向一致")
            elif consensus_direction == 'bearish':
                direction_factor = "看跌一致"
                consensus_strength = 0.8
                transition_score += consensus_strength * 0.2
                transition_signals.append("看跌方向一致")
            else:
                direction_factor = "方向不明"
                consensus_strength = 0.2
                transition_score += consensus_strength * 0.1
                transition_signals.append("转换方向不明")
            
            # 4. 共振评分
            if resonance_score >= 0.7:
                resonance_level = "强共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.2
                transition_signals.append(f"强共振({resonance_score:.2f})")
            elif resonance_score >= 0.5:
                resonance_level = "中等共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.15
                transition_signals.append(f"中等共振({resonance_score:.2f})")
            else:
                resonance_level = "弱共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.1
                transition_signals.append(f"弱共振({resonance_score:.2f})")
            
            # 转换状态判断
            if transition_score >= 0.8:
                transition_state = TransitionState.TRANSITION_COMPLETE
            elif transition_score >= 0.6:
                transition_state = TransitionState.LATE_TRANSITION
            elif transition_score >= 0.4:
                transition_state = TransitionState.MID_TRANSITION
            elif transition_score >= 0.2:
                transition_state = TransitionState.EARLY_TRANSITION
            else:
                transition_state = TransitionState.NO_TRANSITION
            
            # 置信度计算
            data_availability = 1.0 if any([transition_probability, transition_signal != 'none']) else 0.3
            confidence = min(0.95, max(0.2, data_availability * 0.7 + transition_score * 0.3))
            
            # 数据质量评估
            data_quality = "good" if transition_probability > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="转换",
                state=transition_state,
                score=transition_score,
                confidence=confidence,
                signals=transition_signals,
                data_quality=data_quality,
                details={
                    'base_transition': base_transition,
                    'direction_factor': direction_factor,
                    'resonance_level': resonance_level
                },
                indicators={
                    'transition_probability': transition_probability,
                    'consensus_direction': consensus_direction,
                    'transition_signal': transition_signal,
                    'resonance_score': resonance_score,
                    'transition_score': transition_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Transition evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="转换",
                state=TransitionState.NO_TRANSITION,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

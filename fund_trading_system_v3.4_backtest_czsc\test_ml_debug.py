#!/usr/bin/env python3
"""
ML增强回测系统调试测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def create_test_data(days=200):
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', periods=days, freq='D')
    
    # 生成模拟价格数据
    np.random.seed(42)
    price = 100
    prices = []
    
    for i in range(days):
        # 添加趋势和随机波动
        trend = 0.001 * np.sin(i / 20)  # 长期趋势
        noise = np.random.normal(0, 0.02)  # 随机噪声
        price *= (1 + trend + noise)
        prices.append(price)
    
    # 创建OHLCV数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_ml_enhanced_backtest():
    """测试ML增强回测"""
    print("🔍 Testing ML Enhanced Backtest System")
    print("="*60)
    
    try:
        # 导入模块
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建测试数据
        print("📊 Creating test data...")
        data = create_test_data(200)
        print(f"✅ Test data created: {len(data)} rows")
        print(f"   Price range: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        # 创建ML回测引擎
        print("\n🤖 Initializing ML Enhanced Backtest Engine...")
        ml_engine = MLEnhancedBacktestEngine()
        print("✅ ML engine initialized")
        
        # 运行ML回测
        print("\n🚀 Running ML backtest...")
        results = ml_engine.run_ml_backtest(data, train_model=True)
        
        # 打印调试日志
        print("\n" + "="*60)
        print("🔍 ML Debug Log:")
        print("="*60)
        ml_engine.print_ml_log()
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 ML Backtest Results:")
        print("="*60)
        if isinstance(results, dict):
            for key, value in results.items():
                if isinstance(value, (int, float)):
                    print(f"{key}: {value:.6f}")
                elif isinstance(value, dict):
                    print(f"{key}:")
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            print(f"  {sub_key}: {sub_value:.6f}")
                        else:
                            print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")
        else:
            print(f"Results: {results}")
        
        # 检查关键指标
        print("\n" + "="*60)
        print("✅ Test Summary:")
        print("="*60)
        
        if isinstance(results, dict):
            trade_count = results.get('trade_count', 0)
            total_return = results.get('total_return', 0)
            
            print(f"Trade Count: {trade_count}")
            print(f"Total Return: {total_return:.4f}")
            
            if trade_count > 0:
                print("🎉 SUCCESS: ML system generated trades!")
            else:
                print("⚠️ WARNING: No trades generated")
                
            if abs(total_return) > 0.001:
                print("🎉 SUCCESS: Non-zero returns achieved!")
            else:
                print("⚠️ WARNING: Zero returns")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ml_enhanced_backtest()
    if success:
        print("\n🎉 ML Enhanced Backtest Test Completed!")
    else:
        print("\n❌ ML Enhanced Backtest Test Failed!")

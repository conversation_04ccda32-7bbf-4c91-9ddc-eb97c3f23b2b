#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CZSC symbol字段修复
验证数据获取器是否正确添加了symbol字段
"""

import sys
import os
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_data_fetcher_symbol_field():
    """测试数据获取器是否包含symbol字段"""
    print("🔍 测试数据获取器symbol字段...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        
        # 测试获取数据
        fund_code = '518880'
        print(f"📊 获取{fund_code}的数据...")
        
        df = get_kline(fund_code, freq='D')
        
        if df is None or df.empty:
            print("❌ 数据获取失败")
            return False
        
        print(f"✅ 成功获取{len(df)}条数据")
        print(f"📋 数据列: {list(df.columns)}")
        
        # 检查是否包含symbol字段
        if 'symbol' in df.columns:
            print(f"✅ symbol字段存在: {df['symbol'].iloc[0]}")
            return True
        else:
            print("❌ symbol字段缺失")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_czsc_analyze_with_symbol():
    """测试CZSC分析器是否能正常工作"""
    print("\n🔍 测试CZSC分析器...")
    
    try:
        from core.enhanced_data_fetcher import get_kline
        from simpleczsc import KlineAnalyze
        
        # 获取数据
        fund_code = '518880'
        print(f"📊 获取{fund_code}的K线数据...")
        
        kline_data = get_kline(fund_code, freq='30min')
        
        if kline_data is None or len(kline_data) < 50:
            print("❌ K线数据不足")
            return False
        
        print(f"✅ 获取{len(kline_data)}条K线数据")
        
        # 转换为字典格式（KlineAnalyze需要的格式）
        kline_list = []
        for _, row in kline_data.iterrows():
            kline_dict = {
                'symbol': row['symbol'],
                'dt': row['dt'],
                'open': row['open'],
                'high': row['high'],
                'low': row['low'],
                'close': row['close'],
                'vol': row['vol']
            }
            kline_list.append(kline_dict)
        
        print(f"📋 第一条K线数据: {kline_list[0]}")
        
        # 创建CZSC分析器
        print("🔧 创建CZSC分析器...")
        ka = KlineAnalyze(kline_list)
        
        print(f"✅ CZSC分析器创建成功")
        print(f"📊 分析结果:")
        print(f"   - symbol: {ka.symbol}")
        print(f"   - 开始时间: {ka.start_dt}")
        print(f"   - 结束时间: {ka.end_dt}")
        print(f"   - 最新价格: {ka.latest_price}")
        
        # 检查分型和笔数据
        if hasattr(ka, 'fx_list') and ka.fx_list:
            print(f"   - 分型数量: {len(ka.fx_list)}")
        
        if hasattr(ka, 'bi_list') and ka.bi_list:
            print(f"   - 笔数量: {len(ka.bi_list)}")
        
        if hasattr(ka, 'xd_list') and ka.xd_list:
            print(f"   - 线段数量: {len(ka.xd_list)}")
        
        return True
        
    except Exception as e:
        print(f"❌ CZSC分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_czsc_extraction():
    """测试协调器的CZSC数据提取"""
    print("\n🔍 测试协调器CZSC数据提取...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        
        # 创建协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试CZSC数据提取
        fund_code = '518880'
        print(f"📊 提取{fund_code}的CZSC结构数据...")
        
        czsc_data = coordinator._extract_czsc_structure_data(fund_code, {})
        
        if czsc_data and 'error' not in czsc_data:
            print("✅ CZSC结构数据提取成功")
            print(f"📊 提取结果:")
            print(f"   - FX分型数量: {len(czsc_data.get('fx_list', []))}")
            print(f"   - BI笔数量: {len(czsc_data.get('bi_list', []))}")
            print(f"   - XD线段数量: {len(czsc_data.get('xd_list', []))}")
            print(f"   - 数据源: {czsc_data.get('data_source', '未知')}")
            return True
        else:
            print(f"❌ CZSC结构数据提取失败: {czsc_data}")
            return False
            
    except Exception as e:
        print(f"❌ 协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 CZSC Symbol字段修复测试")
    print("="*60)
    
    test_results = []
    
    # 测试1: 数据获取器symbol字段
    result1 = test_data_fetcher_symbol_field()
    test_results.append(("数据获取器symbol字段", result1))
    
    # 测试2: CZSC分析器
    result2 = test_czsc_analyze_with_symbol()
    test_results.append(("CZSC分析器", result2))
    
    # 测试3: 协调器CZSC数据提取
    result3 = test_coordinator_czsc_extraction()
    test_results.append(("协调器CZSC数据提取", result3))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！CZSC symbol字段问题已修复")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)